import React from 'react';
import styled from 'styled-components';

const ItemWrapper = styled.div`
  padding: 10px 0;
  border: 1px solid #bbb;
  background: #fff;
  margin-bottom: 10px;
	.standard-header {
    border-bottom: 1px solid #bbb;
    padding-bottom: 10px;
    padding-left: 10px;
	}
	.content {
    display: flex;
		.item {
      width: 50%;
      text-align: center;
			.title {
        //height: 30px;
        //line-height: 30px;
				padding: 5px 0;
			}
			.value {
        font-size: 16px;
        font-weight: bold;
			}
		}
		.item-20 {
			width: 20%;
		}
	}
`;

export default ItemWrapper;
