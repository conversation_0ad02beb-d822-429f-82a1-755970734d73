import React, { useEffect, useState, useRef } from 'react';
import './aestheticMedicineWeekPredictUpload.css'

import { Button, List, Form, Input, Dialog, Toast, TextArea, Space, Radio, Picker, Popup, PickerView, SearchBar } from "antd-mobile";
import dayjs from "dayjs";
import { DataGrid, GridActionsCellItem } from '@mui/x-data-grid';
import DeleteIcon from '@mui/icons-material/Delete';
import AnalyticsIcon from "@mui/icons-material/Analytics";
import PostAddIcon from '@mui/icons-material/PostAdd';
import DoubleArrowIcon from '@mui/icons-material/DoubleArrow';

import CategoryLayoutUpload from '../../../../public/CategoryLayoutUpload/categoryLayoutUpload'
import AddSalesAmount from './AddSalesAmount/addSalesAmount';

import { http1 } from '../../../../../utils/network'
import { nanoid } from 'nanoid'

const AestheticMedicineWeekPredictUpload = () => {

    const Test = (data) => {
        console.log(data)
    }

    Test('aestheticMedicineWeekPredictUpload')

    const [form] = Form.useForm()

    const parentRef = useRef(null)

    const [structureNameVisible, setStructureNameVisible] = useState(false)  // 机构名称下拉显示

    const [aestheticInfo, setAestheticInfo] = useState({
        structureId: '',  // 机构名称 (传值)
        structureName: '',  // 机构名称 (显示)
    })

    const [structureNameList, setStructureNameList] = useState([])
    const [structureStatusList, setStructureStatusList] = useState([])
    const [structureTypeList, setStructureTypeList] = useState([])
    const [salesCaculateTypeList, setSalesCaculateTypeList] = useState([])
    // 填报人
    const [empname, setEmpname] = useState('')

    // 处理枚举数组格式
    const handleList = (arr) => {
        let temp = []
        temp = arr.length > 0 && arr.map(item => {
            return { label: item.description, value: item.code }
        })
        if (temp.length > 0) {
            return [temp]
        } else { 
            return[[{ label: '', value: '' }]]
        }
    }

    useEffect(() => {
        // 填报人信息
        http1.post('/account/api/userInfo', {}).then(res => {
            setEmpname(res.empname)
        })
        // 机构名称枚举
        http1.post('/hospital/list/query', { page: 1, size: 20 }).then((res) => {
            let temp = []
            temp = res.length > 0 && res.map(item => {
                return { label: item.hospitalName, value: item.hospitalId }
            })
            if (temp.length > 0) {
                setStructureNameList([temp])
            } else { 
                setStructureNameList([[{ label: '', value: '' }]])
            }
        })
        // 机构准入状态枚举
        http1.post('/meta/select', { tag: 'StructureAccessStatus' }).then((res) => {
            setStructureStatusList(handleList(res))
        })
        // 机构类型枚举
        http1.post('/meta/select', { tag: 'StructureType' }).then((res) => {
            setStructureTypeList(handleList(res))
        })
        // 销量核算类型枚举
        http1.post('/meta/select', { tag: 'EffectiveCalculateType' }).then((res) => {
            setSalesCaculateTypeList(handleList(res))
        })
    }, [])
    // 新增
    const [salesAmountVisible, setSalesAmountVisible] = useState(false)  // 新增弹窗

    const addSalesAmount = () => {
        setSalesAmountVisible(true)
    }

    const [salesList, setSalesList] = useState([])  // 新增数据列表

    const handleConfirm = () => {
        form.setFieldValue('structureId', aestheticInfo.structureId)  // 设置机构名称字段
        // form.setFields()
        setStructureNameVisible(false)
    }
    const resetConcel = () => { 
        // setAestheticInfo({...aestheticInfo, structureId: '', structureName: ''})
        setStructureNameVisible(false)
    }

    //表单头
    const columns = React.useMemo(
        () => [
            { field: 'productLabel', headerName: '产品', type: 'string', },
            { field: 'specificationsLabel', headerName: '规格', type: 'string' },
            { field: 'volume', headerName: '支数', type: 'number' },
            { field: 'priceUnit', headerName: '回款单价(元)', type: 'number' },
            { field: 'price', headerName: '纯销金额(元)', type: 'number' },
            {
                field: 'actions',
                headerName: '操作',
                type: 'actions',
                width: 80,
                getActions: (params) => [
                    <GridActionsCellItem
                        icon={<DeleteIcon />}
                        label="Delete"
                        onClick={() => deleteUser(params.id)}
                    />,
                ]
            },
        ],
        [deleteUser],
    );
    const [deleteRow, setDeleteRow] = useState(false)
    const [rowId, setRowId] = useState('')
    //删除行
    const deleteUser = (id) => {
        setRowId(id)
        setDeleteRow(true)
    }
    const confirmDelete = () => {
        let arr = []
        arr = salesList.filter(row => row.id != rowId)
        setSalesList(arr)
    }

    // 新增数据
    const [salesInfo, setSalesInfo] = useState({
        // product: '',
        productLabel: '',
        // specifications: '',
        specificationsLabel: '',
        volume: 0,
        priceUnit: 0,
        price: 0,
    })

    // 新增
    const handleAdd = () => {
        let addData = parentRef.current.childHandleAdd()
        console.log(addData);
        addData.product = addData.product && addData.product[0]
        addData.specifications = addData.specifications && addData.specifications[0]
        addData.priceUnit = Number(addData.priceUnit)
        addData.price = Number(addData.price)

        let id = nanoid()
        if (addData.product && addData.specifications) {
            setSalesList([...salesList, { ...salesInfo, ...addData, id: id }])
            setSalesAmountVisible(false)
            Toast.show({
                icon: 'success',
                content: '新增成功',

            })
            parentRef.current.resetData()
        }
    }
    const handleCancel = () => {
        setSalesAmountVisible(false)
        parentRef.current.resetData()
    }

    const onSubmit = async () => {
        await form.validateFields()
        const values = form.getFieldsValue()
        console.log(values, 11111);
        values.structureId = values.structureId
        values.structureStatus = values.structureStatus && values.structureStatus[0]
        values.structureType = values.structureType && values.structureType[0]
        values.salesCaculateType = values.salesCaculateType && values.salesCaculateType[0]
        values.scheduleComment = values.scheduleComment
        let tempArr = []
        tempArr = salesList.map(item => {
            return {
                product: item.product,
                specifications: item.specifications,
                volume: item.volume,
                priceUnit: item.priceUnit,
                price: item.price
            }
        })

        if (values.structureId && values.structureStatus && values.structureType && values.salesCaculateType) {
            http1.post('/CosmetologyReport/daily/report/submit', {
                type: 1,
                ...values,
                products: tempArr
            })
            Dialog.alert({
                content: '提交成功',
                onConfirm: () => {
                console.log('Confirmed')
                }
            })
        } 
    }


    const handleQueryName = (val) => {

        http1.post('/hospital/list/query', { page: 1, size: 20, hospitalName: val, query: {hospitalName: "like"} }).then((res) => {
            console.log(res);
            let temp = []
            temp = res.length > 0 && res.map(item => {
                return { label: item.hospitalName, value: item.hospitalId }
            })
            if (temp.length > 0) {
                setStructureNameList([temp])
            } else { 
                setStructureNameList([[{ label: '', value: '' }]])
            }
        })
    }

    return (
        <div className={'aestheticMedicineWeekPredictUpload'}>
            <div className={'top'}>医美<DoubleArrowIcon fontSize={'small'} />周预估<DoubleArrowIcon fontSize={'small'} />数据上报</div>
            <div className={'content'}>
                <CategoryLayoutUpload>
                    {/*header*/}
                    <>
                        <div className={'header'}>
                            <div className={'headerContext'}>
                                <PostAddIcon className={'fontMain'} fontSize={'large'} />
                                <div style={{ fontWeight: 'bold' }}>
                                    数据上报
                                </div>
                            </div>
                            <div className={'headerContext'}>
                                <span className={'fontMain'} style={{ fontWeight: 'bold' }}>上报人:</span>
                                <span className={'fontSecond'}>{empname}</span>
                            </div>
                        </div>
                    </>
                    {/*body*/}
                    <>
                        <div className={'container'}>
                            <Form form={form}>
                                <Form.Item
                                    label='上报日期'
                                    trigger='onConfirm'
                                    rules={[{ required: true }]}
                                >
                                    <Input disabled defaultValue={dayjs(new Date()).format('YYYY-MM-DD')}></Input>
                                </Form.Item>
                                <Form.Item
                                    // name='weekForecast'
                                    label='周预估'
                                    trigger='onConfirm'
                                    rules={[{ required: true }]}
                                    onClick={(e, datePickerRef) => {
                                        datePickerRef.current?.open()
                                    }}
                                >
                                    <Input disabled defaultValue={dayjs(new Date()).format('YYYY-MM-DD') + ' 至 ' + dayjs(new Date().getTime() + 1000 * 60 * 60 * 24 * 7).format('YYYY-MM-DD')}></Input>
                                </Form.Item>  
                                <Form.Item
                                    name='structureId'
                                    label='机构名称'
                                    trigger='onConfirm'
                                    rules={[{ required: true, message: '请选择机构名称' }]}
                                    onClick={() => setStructureNameVisible(true)}
                                >
                                    {aestheticInfo.structureName ? <Input value={aestheticInfo.structureName} /> : null}
                                    <Popup
                                        visible={structureNameVisible}
                                        onMaskClick={() => {
                                            setStructureNameVisible(false)
                                        }}
                                        bodyStyle={{
                                            borderTopLeftRadius: '8px',
                                            borderTopRightRadius: '8px',
                                            minHeight: '40vh',
                                        }}
                                    >
                                        <div className={'adm-picker-header'} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', height: '42px' }}>
                                            <span className={'adm-picker-header-button'} style={{ fontSize: '15px', color: 'var(--gensci-main)' }} onClick={ resetConcel}>取消</span>
                                            <span className={'adm-picker-header-button'} style={{ fontSize: '15px', color: 'var(--gensci-main)' }} onClick={handleConfirm}>确认</span>
                                        </div>
                                        <SearchBar placeholder='请输入内容' onChange={e => handleQueryName(e)} style={{ margin: '5px 10px' }} />
                                        <PickerView
                                            columns={structureNameList}
                                            onChange={(val, extend) => {
                                                console.log(extend.items);
                                                setAestheticInfo({ ...aestheticInfo, structureId: extend.items[0].value, structureName: extend.items[0].label })
                                            }}
                                        >
                                        </PickerView>
                                    </Popup>
                                </Form.Item>
                                <Form.Item
                                    name='structureStatus'
                                    label='机构准入状态'
                                    trigger='onConfirm'
                                    rules={[{ required: true, message: '请选择机构准入状态' }]}
                                    onClick={(e, pickerRef) => {
                                        pickerRef.current?.open()
                                    }}
                                >
                                    <Picker
                                        columns={structureStatusList}
                                    >
                                        {items => {
                                            if (items.every(item => item === null)) {
                                                return ''
                                            } else {
                                                return items.map(item => item?.label ?? '未选择')
                                            }
                                        }}
                                    </Picker>
                                </Form.Item>
                                <Form.Item
                                    name='structureType'
                                    label='机构类型'
                                    trigger='onConfirm'
                                    rules={[{ required: true, message: '请选择机构类型' }]}
                                    onClick={(e, pickerRef) => {
                                        pickerRef.current?.open()
                                    }}
                                >
                                    {/* <Input disabled value={aestheticInfo.structureType} /> */}
                                    <Picker
                                        columns={structureTypeList}
                                    >
                                        {items => {
                                            if (items.every(item => item === null)) {
                                                return ''
                                            } else {
                                                return items.map(item => item?.label ?? '未选择')
                                            }
                                        }}
                                    </Picker>
                                </Form.Item>
                                <Form.Item
                                    name='salesCaculateType'
                                    label='销量核算类型'
                                    trigger='onConfirm'
                                    rules={[{ required: true, message: '请选择销量核算类型' }]}
                                    onClick={(e, pickerRef) => {
                                        pickerRef.current?.open()
                                    }}
                                >
                                    <Picker
                                        columns={salesCaculateTypeList}
                                    >
                                        {items => {
                                            if (items.every(item => item === null)) {
                                                return ''
                                            } else {
                                                return items.map(item => item?.label ?? '未选择')
                                            }
                                        }}
                                    </Picker>
                                </Form.Item>
                                <Form.Item
                                    name='scheduleComment'
                                    label='跟进计划'
                                >
                                    <TextArea
                                        placeholder='请输入内容'
                                        rows={3}
                                        maxLength={100}
                                    />
                                </Form.Item>
                            </Form>
                            <List header=' '>
                                <List.Item>
                                    <div className={'countFunc'}>
                                        <div style={{ display: 'flex', alignItems: 'center' }}>
                                            <AnalyticsIcon className={'fontMain'} fontSize={'large'} />
                                            <span style={{ fontWeight: 'bold', marginRight: '5px' }}>纯销额</span>
                                        </div>
                                        <div style={{ display: 'flex', alignItems: 'center' }}>
                                            <span style={{ color: '#ababab', marginRight: '5px' }}>(单位: 元)</span>
                                            <Button size='small' color='primary' onClick={addSalesAmount}>
                                                新增
                                            </Button>
                                            <Dialog
                                                visible={salesAmountVisible}
                                                title={'纯销额新增'}
                                                content={<AddSalesAmount parentRef={parentRef} salesInfo={salesInfo} setSalesInfo={setSalesInfo} />}
                                                closeOnAction
                                                // onClose={() => {
                                                //     setSalesAmountVisible(false)
                                                // }}
                                                actions={[
                                                    [
                                                        {
                                                            key: 'cancel',
                                                            text: '取消',
                                                            danger: true,
                                                            onClick: handleCancel
                                                        },
                                                        {
                                                            key: 'confirm',
                                                            text: '确定',
                                                            bold: true,
                                                            onClick: handleAdd
                                                        },
                                                    ]
                                                ]}
                                            />
                                            <Dialog
                                                visible={deleteRow}
                                                title={'删除'}
                                                content="确认删除该条数据？"
                                                closeOnAction
                                                onClose={() => {
                                                    setDeleteRow(false)
                                                }}
                                                actions={[
                                                    [
                                                        {
                                                            key: 'cancel',
                                                            text: '取消',
                                                            danger: true
                                                        },
                                                        {
                                                            key: 'confirm',
                                                            text: '确定',
                                                            bold: true,
                                                            onClick: confirmDelete
                                                        },
                                                    ]
                                                ]}
                                            />
                                        </div>
                                    </div>
                                    <DataGrid columns={columns} headerAlign="center" rows={salesList} hideFooter={true} style={{ height: '220px' }} disableColumnMenu />
                                </List.Item>
                            </List>
                        </div>
                    </>
                    {/*footer*/}
                    <>
                        <div className={'footer'}>
                            <Button block color='primary' size='middle' style={{ marginTop: '5px', width: 'calc(100% - 20px)' }} onClick={onSubmit}>
                                提交
                            </Button>
                        </div>
                    </>
                </CategoryLayoutUpload>
            </div>
        </div>
    )
}

export default AestheticMedicineWeekPredictUpload
