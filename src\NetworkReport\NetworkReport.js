import React from 'react';
import {BrowserRouter, Route} from 'react-router-dom';
import {Routes} from "react-router";
import {RecoilRoot} from 'recoil';
import './../main.css';

import Home from './page/view/Home/home'
import Category from "./page/view/Category/category";
// import PediatricsPromotionBoard from "./page/view/Category/Pediatric/PromotionBoard";
import PediatricsWeekEstimate from "./page/view/pediatricsWeekEstimate";
import PediatricsWeekEstimate_month from "./page/view/pediatricsWeekEstimate_month";


const App = () => {


    return (
        <RecoilRoot>
            <BrowserRouter>
                <Routes>
                    <Route path='home/*' element={<Home />} />
                    <Route path='category/*' element={<Category />} />
                    {/*<Route path='pediatricsPromotionBoard' element={<PediatricsPromotionBoard />} />*/}
                    <Route path='pediatricsWeekEstimate/*' element={<PediatricsWeekEstimate />} />
                    <Route path='pediatricsMonthEstimate/*' element={<PediatricsWeekEstimate_month />} />
                </Routes>
            </BrowserRouter>
        </RecoilRoot>
    )
}

export default App
