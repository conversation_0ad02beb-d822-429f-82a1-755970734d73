import React, {useEffect, useState} from 'react';
import styled from 'styled-components';
import Header from './component/Header';
import Standard from "./component/Standard";
import NoAchievement from "./component/NoAchievement";
import Shortest from "./component/Shortest";
import TwoQuarterly from "./component/TwoQuarterly";
import Cascader
	from "../../../../../components/private/Category/Pediatric/PediatricDayReportUpload/DataReport/components/Cascader";
import {http1} from "../../../../../utils/network";

import { Toast } from "antd-mobile";

const Wrapper = styled.div`
	background: #eee;
	.picker {
		background: #fff;
	}
`;

const RedBoldText = styled.div`
	color: #C00000;
	font-weight: bold;
`;

const GreenBoldText = styled.div`
	color: #81B337;
	font-weight: bold;
`

const BoldText = styled.div`
	${p => p.isWarning ? 'color: #C00000' : 'color: #81B337'};
	${p => p.isBold ? 'font-weight: bold' : ''};
`;

const isEmpty = num => {
	return num === null || num === undefined;
}

const realNum = (num, isRate, isMil) => {
	if(isEmpty(num)) {
		return ('-')
	}else {
		return isRate ?  Math.floor(num * 100) + '%' : (isMil ? (num / 10000).toFixed(2) : (num).toFixed(2))
	}
}

export { isEmpty, realNum, RedBoldText, GreenBoldText, BoldText }

const PediatricsPromotionBoard = () => {
	const [currentLevel, setCurrentLevel] = useState({});
	const [detail, setDetail] = useState({});

	const getData = async () => {
		try {
			await Toast.show({
				content: '加载中...',
				icon: 'loading',
				duration: 0
			});
			setDetail({});
			if(localStorage.getItem('promotionBoardData')) {
				setDetail(JSON.parse(localStorage.getItem('promotionBoardData')));
			}
			const res = await http1.post('/pediatricsElevate/elevate/board', {
				empNo: currentLevel.empNo
			}) || {};
			setDetail(res);
			localStorage.setItem('promotionBoardData', JSON.stringify(res));
			await Toast.clear();
		} catch (e) {
			setDetail({});
			await Toast.clear();
			if(e && e.message) {
				await Toast.show(e.message)
			}
		}
	}

	useEffect(() => {
		window.document.title = '代表晋升看板';
		if(currentLevel && currentLevel.empNo) {
			getData();
		}
	}, [currentLevel])

	return (
		<Wrapper>
			<Header data={{...currentLevel, level: detail.level}} />
			<Standard data={detail.complianceVOMap || {}} />
			<NoAchievement data={detail.complianceVOMap || {}} />
			<Shortest data={detail.minimumTenureVOMap} />
			<TwoQuarterly data={detail.twoQuartersVOMap} />
			<div className="picker">
				<Cascader onChange={setCurrentLevel} isFilter={true} />
			</div>
		</Wrapper>
	)
}

export default PediatricsPromotionBoard;
