import React, {useEffect, useState} from "react";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import HomeIcon from "@mui/icons-material/Home";
import DoubleArrowIcon from "@mui/icons-material/DoubleArrow";

import "./index.css"
import {http1} from "../../../../../utils/network";
import {Form, Tabs, DatePicker} from "antd-mobile";
import { useNavigate } from "react-router-dom";
import dayjs from "dayjs";


const DataList = () => {
    const [showData, setShowData] = useState(false)
    const [form] = Form.useForm()
    const [subForm] = Form.useForm()
    const time1 = Form.useWatch('time1', form)
    const time2 = Form.useWatch('time2', subForm)
    const [myReportList, setMyReportList] = useState([])
    const [subReportList, setSubReportList] = useState([])
    const [userInfo, setUserInfo] = useState({})
    const navigate = useNavigate()
    useEffect(() => {
        http1.post("/account/api/userInfo", {}).then((res) => {
            setUserInfo(res);
        });
    }, [])

    useEffect(() => {
        // 判断是否是非代表
        http1.post("/nerveDaily/nerveDaily/hasBranch").then((res) => {
            setShowData(res)
        })
    }, [])

    useEffect(() => {
        let selectTime = time1 ? dayjs(time1).format("YYYY-MM-DD") + " 00:00:00" : null;
        // 获取本人数据
        if (userInfo.empno) {
            http1.get(`/nerveDaily/customer/queryTotalList/${userInfo.empno}?time=${selectTime ? new Date(selectTime).getTime() : ''}`).then((res) => {
                setMyReportList(res || [])
            })
        }
    }, [time1, userInfo.empno])

    useEffect(() => {
        let selectTime = time2 ? dayjs(time2).format("YYYY-MM-DD") + " 00:00:00" : null;
        // 获取下属数据
        if (showData && userInfo.empno) {
            http1.get(`/nerveDaily/customer/queryJuniorList/${userInfo.empno}?time=${selectTime ? new Date(selectTime).getTime() : ''}`).then((res) => {
                setSubReportList(res || [])
            })
        }
    }, [time2, showData, userInfo.empno])
    const handleBackHome = () => {
        navigate("/category/customAnalysis/customAnalysisUpload/dataUpdate")
    }
    const handleTarget = () => {
        history.back();
    }
    return (
        <div className={"data-list"}>
            <div className={"top"}>
                {/*<ChevronLeftIcon*/}
                {/*    sx={{ fontSize: 50 }}*/}
                {/*    onClick={() => {*/}
                {/*        handleTarget();*/}
                {/*    }}*/}
                {/*/>*/}
                <HomeIcon
                    sx={{ fontSize: 30 }}
                    style={{ marginRight: "5px" }}
                    onClick={() => {
                        handleBackHome();
                    }}
                />
                客户分析表
                <DoubleArrowIcon fontSize={"small"} />
                数据列表
            </div>
            <Tabs className={"content"}>
                <Tabs.Tab title={showData ? '本人上报' : ''} key={"one"}>
                    <div className={"tabContainer"}>
                        <div className={"tabContainerTop"}>
                            <Form form={form}>
                                <Form.Item
                                    name={"time1"}
                                    label={"上报时间"}
                                    trigger="onConfirm"
                                    onClick={(e, datePickerRef) => {
                                        datePickerRef.current?.open()
                                    }}>
                                    <DatePicker max={new Date()}>
                                        {(value) => value ? dayjs(value).format("YYYY-MM-DD") : null}
                                    </DatePicker>
                                </Form.Item>
                            </Form>
                        </div>
                        {myReportList.map((item) => {
                            return <Card data={item} key={item.id} isMe={true} userInfo={userInfo} />
                        })}
                    </div>
                </Tabs.Tab>
                { showData ?
                    <Tabs.Tab title="下级上报" key={"two"}>
                        <Form form={subForm}>
                            <Form.Item
                                name="time2"
                                label={"上报时间"}
                                trigger="onConfirm"
                                onClick={(e, datePickerRef) => {
                                    datePickerRef.current?.open()
                                }}>
                                <DatePicker max={new Date()}>
                                    {(value) => value ? dayjs(value).format("YYYY-MM-DD") : null}
                                </DatePicker>
                            </Form.Item>
                        </Form>
                        {subReportList.map((item) => {
                            return <Card data={item} key={item.id} isMe={false} userInfo={userInfo} />
                        })}
                    </Tabs.Tab> : null
                }
            </Tabs>
        </div>
    )
}

const Card = (props) => {
    let { data, isMe, userInfo } = props
    const navigate = useNavigate()
    const handleDetail = (id) => {
        navigate("/category/customAnalysis/customAnalysisUpload/dataUpdate", {
            state: { id, isMe: isMe }
        })
    }
    return (
        <div onClick={() => handleDetail(data.id)} style={{borderBottom: '1px solid #ccc', padding: '10px'}}>
            填报人姓名： { data.createdName }
            <div style={{ margin: "10px 0 0 240px" }}>
                { dayjs(data.reportTime).format("YYYY-MM-DD") }
            </div>
        </div>
    )
}

export default DataList
