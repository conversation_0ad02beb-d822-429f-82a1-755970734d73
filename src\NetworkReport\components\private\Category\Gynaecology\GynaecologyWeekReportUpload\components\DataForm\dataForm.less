.dataForm {
    height: 100%;
}

.dataForm .top {
    height: 38px;
    background: var(--gensci-second);
    color: white;
    font-size: 16px;
    display: flex;
    align-items: center;
    // padding: 0 8px;
    box-shadow: 0 2px 2px #d2d2d2;
}

.dataForm .content {
    height: calc(100% - 25px);
}

.dataForm .fontMain {
    color: var(--gensci-main);
}

.dataForm .fontSecond {
    color: var(--gensci-second);
}

.dataForm .header {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px 0 5px;
    background: #3644ac0f
}

.dataForm .container {
    height: 100%;
    overflow: auto;
}

.dataForm .footer {
    height: 100%;
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    align-items: center;
}

.dataForm .countFunc {
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 15px;
    color: black;
}

.dataForm .headerContext {
    display: flex;
    align-items: center;
    font-size: 15px;
}

.reproductionWeekReportUploadDataFormSearchContainer {
    margin: 5px 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.gynaecologyWeekReportUpload__searchContainer {
    margin: 5px 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}