.data-list {
    height: 100%;
		background: #fff;
}

.data-list .top{
    height: 38px;
    background: var(--gensci-second);
    color: white;
    font-size: 15px;
    display: flex;
    align-items: center;
    padding: 0 8px;
    box-shadow: 0 2px 2px #d2d2d2;
}

.data-list .content{
    height: calc(100% - 25px);
		overflow-y: auto;
}

.data-list .tabContainer{
    display: flex;
    flex-direction: column;
    height: 100%;
}

.data-list .tabContainerTop{
    flex: none;
}

.data-list .tabContainerContent{
    flex: 1;
    overflow-y: auto;
}
.data-list .tabcontent{
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.data-list .tabtitle{
    width: 90%;
}
.data-list .righttime{
    width: 100%;
    display: flex;
    justify-content: right;
}

.data-list .contentright{
    margin-top: 18px;
    border-bottom: 1px solid #f5f5f5;
    padding: 5px 5px;
    color: var(--adm-color-weak);
}

.data-list .showText {
    color: #ccc;
    width: 60px;
    margin: 200px auto;
}
