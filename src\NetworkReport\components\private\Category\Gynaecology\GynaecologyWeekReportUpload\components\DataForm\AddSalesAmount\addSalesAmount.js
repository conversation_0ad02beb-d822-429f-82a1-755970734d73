import React, {useState, forwardRef, useImperative<PERSON>andle, useEffect} from 'react';
import addSalesAmount from './addSalesAmount.less'

import {Form, Input, Picker, Stepper} from "antd-mobile";

import { http1} from '../../../../../../../../utils/network'
import Big from 'big.js'

const AddSalesAmount = forwardRef(({addSalesRef, salesAmountInfo, setSalesAmountInfo}) => {

    const [form] = Form.useForm()

    const drugId = Form.useWatch('drugId', form)
    const volume = Form.useWatch('volume', form)
    const priceUnit = Form.useWatch('priceUnit', form)

    useImperativeHandle((addSalesRef), () => {
        return {
            childHandleAdd,
            handleValidate,
            resetData
        }
    })

    const childHandleAdd = () => { 
        (async () => {
            await form.validateFields()
        })()
        let values =  form.getFieldsValue()
        return values
    }

    const handleValidate = async () => { 
        await form.validateFields()
    }

    const resetData = () => { 
        form.resetFields()
    }

    const [productList, setProductList] = useState([])
    const [specificationList, setSpecificationList] = useState([])  //规格

    useEffect(() => {
        http1.post('/meta/select', { tag: 'GynecologyProduct' }).then((res) => {
            let temp = []
            temp = res.length > 0 && res.map(item => { 
                return { label: item.description, value: item.code}
            })
            if (temp.length > 0) {
                setProductList([temp])
            } else { 
                setProductList([[{ label: '', value: '' }]])
            }
        })
    }, [])
    
    useEffect(() => {
        console.log(drugId);
        http1.post('/meta/specifications', { tag: 'GynecologyProduct', tagCode: drugId && drugId[0] }).then((res) => {
            let temp = []
            temp = res.length > 0 && res.map(item => { 
                return { label: item.description, value: item.code}
            })
            if (temp.length > 0) {
                setSpecificationList([temp])
            } else { 
                setSpecificationList([[{ label: '', value: '' }]])
            }
        })

    }, [drugId])

    const handleVolume = (value) => {
        let str = String(value * priceUnit)
        console.log(str);
        if (str.indexOf('.') !== -1) {
            str = str.slice(0, str.indexOf('.') + 3)
            console.log(str);
        } 
        form.setFieldsValue({ num: str })
    }   

    const handlePriceUnit = (value) => {
        if (value.indexOf(".") !== -1) {
          value = value.slice(0, value.indexOf(".") + 3);
        }
        let x = new Big(value) 
        let str = x.times(volume)
        // let str = (volume * value);
        // if (str.indexOf('.') !== -1) {
        //     str = str.slice(0, str.indexOf('.') + 3)
        // }
        form.setFieldsValue({ num: str })
    }   

    return (
        <div className={addSalesAmount.addSalesAmount}>
            <Form
                form={form}
                initialValues={{volume: 0}}
            >
                <Form.Item
                    name='drugId'
                    label='产品'
                    trigger='onConfirm'
                    rules={[{ required: true }]}
                    onClick={(e, pickerRef) => {
                        pickerRef.current?.open()
                    }}
                >
                    <Picker
                        columns={productList}
                        onConfirm={(v, extend) => { 
                            setSalesAmountInfo({...salesAmountInfo, drugName: extend.items[0].label})
                        }}
                    >
                        {items => {
                            if (items.every(item => item === null)) {
                                return null
                            } else {
                                return items.map(item => item?.label ?? '未选择')
                            }
                        }}
                    </Picker>
                </Form.Item>
                <Form.Item
                    name='specifications'
                    label='规格'
                    disabled={!drugId}
                    trigger='onConfirm'
                    rules={[{ required: true }]}
                    onClick={(e, pickerRef) => {
                        pickerRef.current?.open()
                    }}
                >
                    <Picker
                        columns={specificationList}
                        onConfirm={(v, extend) => { 
                            setSalesAmountInfo({...salesAmountInfo, specificationsName: extend.items[0].label})
                        }}
                    >
                        {items => {
                            if (items.every(item => item === null)) {
                                return null
                            } else {
                                return items.map(item => item?.label ?? '未选择')
                            }
                        }}
                    </Picker>
                </Form.Item>
                <Form.Item
                    name='volume'
                    rules={[{type: 'number'},{ required: true }]}
                    label='数量'
                >
                    <Stepper
                        min={0}
                        digits={0}
                        onChange={handleVolume}
                    />
                </Form.Item>
                <Form.Item
                    name='priceUnit'
                    label='单价(元)'
                    rules={[{ required: true }]}
                    getValueFromEvent={(value) => {
                        return value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); // 只能输入两位小数
                    }}
                >
                    <Input type="number" min={0} 
                        onChange={handlePriceUnit}
                    />
                </Form.Item>
                <Form.Item
                    name='num'
                    label='销量(元)'
                    rules={[{ required: true }]}
                >
                    <Input disabled type="number" min={0} />
                </Form.Item>
            </Form>
        </div>
    )
})

export default AddSalesAmount
