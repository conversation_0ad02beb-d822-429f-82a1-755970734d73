import React, { useEffect, useState, useRef } from 'react';
import dataForm from './dataForm.less'

import { useNavigate, useLocation } from 'react-router-dom'
import { createHashHistory } from "history";

import {Button,List,Form,Input,Dialog,Toast,Space,Radio,Picker,Popup,PickerView,SearchBar} from "antd-mobile";
import {EditSFill} from 'antd-mobile-icons'

import {DataGrid, GridActionsCellItem} from '@mui/x-data-grid';
import DeleteIcon from '@mui/icons-material/Delete';
import AnalyticsIcon from "@mui/icons-material/Analytics";
import PostAddIcon from '@mui/icons-material/PostAdd';
import DoubleArrowIcon from '@mui/icons-material/DoubleArrow';
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";

import CategoryLayoutUpload from '../../../../../../public/CategoryLayoutUpload/categoryLayoutUpload'
import AddSalesAmount from './AddSalesAmount/addSalesAmount';

import { http1 } from '../../../../../../../utils/network'
import dayjs from "dayjs";
import { nanoid } from 'nanoid'

const DataForm = ({formType}) => {

    const [form] = Form.useForm()

    const hasSales = Form.useWatch('hasSales', form)

    const parentRef = useRef(null)

    const history = createHashHistory()

    const navigate = useNavigate()

    const info = useLocation();
    if (info.state) {
        var id = info.state.id;
        var time = info.state.time;
        var reporter = info.state.reporter;
    }

    const [structureNameVisible, setStructureNameVisible] = useState(false)  // 机构名称下拉显示

    const [structureInfo, setStructureInfo] = useState({
        structureId: '',  // 机构名称 (传值)
        structureName: '',  // 机构名称 (显示)
    })

    const [tempStructure, setTempStructure] = useState({})

    const [structureNameList, setStructureNameList] = useState([])
    const [structureStatusList, setStructureStatusList] = useState([])
    const [structureTypeList, setStructureTypeList] = useState([])
    const [salesCaculateTypeList, setSalesCaculateTypeList] = useState([])
    // 填报人
    const [empname, setEmpname] = useState('')

    // 处理枚举数组格式
    const handleList = (arr) => {
        let temp = []
        temp = arr.length > 0 && arr.map(item => {
            return { label: item.description, value: item.code}
        })
        if (temp.length > 0) {
            return [temp]
        } else {
            return[[{ label: '', value: '' }]]
        }
    }

    useEffect(() => {
        // 填报人信息
        http1.post('/account/api/userInfo', {}).then(res => {
            setEmpname(res.empname)
        })
        // 机构名称枚举
        http1.post('/hospital/list/query',{page: 1, size: 20}).then((res) => {
            let temp = []
            temp = res.length > 0 && res.map(item => {
                return { label: item.hospitalName, value: item.hospitalId}
            })
            if (temp.length > 0) {
                setStructureShowShow(true)
                setStructureNameList([temp])
            } else {
                setStructureShowShow(false)
                setStructureNameList([[{ label: '', value: '' }]])
            }
        })
        // 机构准入状态枚举
        http1.post('/meta/select', {tag: 'StructureAccessStatus'}).then((res) => {
            setStructureStatusList(handleList(res))
        })
        // 机构类型枚举
        http1.post('/meta/select', { tag: 'StructureType' }).then((res) => {
            setStructureTypeList(handleList(res))
        })
        // 销量核算类型枚举
        http1.post('/meta/select', { tag: 'EffectiveCalculateType' }).then((res) => {
            setSalesCaculateTypeList(handleList(res))
        })
    }, [])

    if (formType === 'update') {
        useEffect(() => {
          http1
            .post("/CosmetologyReport/daily/report/get", { id: id })
            .then((res) => {
              setEmpname(res.createdName)
              form.setFieldsValue({
                hasSales:
                  res.hasSales !== null && res.hasSales !== undefined
                    ? res.hasSales ? 1 : 2
                    : 1,
                structureId: res.structureId !== null && res.structureId !== undefined ? res.structureId : [],
                structureStatus: res.structureStatus !== null && res.structureStatus !== undefined ? [res.structureStatus] : [],
                structureType: res.structureType !== null && res.structureType !== undefined ? [res.structureType] : [],
                salesCaculateType: res.salesCaculateType !== null && res.salesCaculateType !== undefined ? [res.salesCaculateType] : [],
              });
              setStructureInfo({...structureInfo, structureId: res.structureId, structureName: res.structureName,
              });
            });
          if (reporter) {
            http1
              .post("/cosmetologyProduct/daily/product/query", {
                reportId: id,
                branchEmpNo: true,
              })
              .then((res) => {
                console.log(res);
                setSalesList([...res]);
              });
          } else {
             http1
               .post("/cosmetologyProduct/daily/product/query", {
                 reportId: id,
               })
               .then((res) => {
                 console.log(res);
                 setSalesList([...res]);
               });
          }
        }, []);
    }
    // 新增
    const [salesAmountVisible, setSalesAmountVisible] = useState(false)  // 新增弹窗
    const [editShow, setEditShow] = useState(false)

    const addSalesAmount = () => {
        setSalesAmountVisible(true)
        setEditShow(false)
    }

    const [salesList, setSalesList] = useState([])  // 新增数据列表

    const handleConfirm = () => {
        setStructureInfo({
            ...structureInfo,
            structureId: tempStructure.value,
            structureName: tempStructure.label,
        });
        form.setFields([
            {
              name:'structureId', value:tempStructure.value, errors:null
            }
        ])
        form.setFieldValue('structureId', tempStructure.value)  // 设置机构名称字段
        setStructureNameVisible(false)
    }

    //表单头
    const columns = React.useMemo(
        () => [
            { field: 'prdName', headerName: '产品', type: 'string', },
            { field: 'spec', headerName: '规格', type: 'string' },
            { field: 'buyNum', headerName: '支数', type: 'number' },
            { field: 'priceUnit', headerName: '回款单价(元)', type: 'number' },
            { field: 'sumSales', headerName: '纯销金额(元)', type: 'number' },
            {
                field: 'actions',
                headerName: '操作',
                type: 'actions',
                width: 80,
                getActions: (params) => [
                    <EditSFill style={{fontSize: '18px'}} onClick={()=>editUser(params.row)}/>
                ]
            },
        ],
        [editUser],
    );
    //删除行
    const [rowId, setRowId] = useState('')
    const [deleteRowVisible, setDeleteRowVisible] = useState(false)

    const editUser = async (value) => {
        setRowId(value.id)
        console.log(value)
        setSalesInfo({
            prdBrand: value.prdBrand,
            prdCode: value.prdCode,
            threeLvlVarietCode: value.threeLvlVarietCode,
            threeLvlVarietName: value.threeLvlVarietName,
            productCode: value.productCode
        })
        await setSalesAmountVisible(true);
        console.log(value, parentRef.current);
        setEditShow(true)
        await parentRef.current.editFormData(value)
    }

    const handleDelete = () => {
        setDeleteRowVisible(true)
    }

    const deleteRowConfirm = () => {
        setSalesList((prevRows) => prevRows.filter(row => row.id !== rowId))
        setSalesAmountVisible(false);
        setSalesInfo(initSalesInfo)
        parentRef.current.resetData()
    }

    const initSalesInfo = {
        prdBrand: '',
        prdCode: '',
        threeLvlVarietCode: '',
        threeLvlVarietName: '',
        productCode: ''
    }
    // 新增数据
    const [salesInfo, setSalesInfo] = useState({
        prdBrand: '',
        prdCode: '',
        threeLvlVarietCode: '',
        threeLvlVarietName: '',
        productCode: ''
    })

    // 新增
    const handleAdd = async () => {
        await parentRef.current.formValidate()
        let addData = parentRef.current.childHandleAdd()
        addData.prdName = addData.prdName && addData.prdName[0]
        addData.spec = addData.spec && addData.spec[0]
        addData.buyNum = Number(addData.buyNum)
        addData.priceUnit = Number(addData.priceUnit)
        addData.sumSales = Number(addData.sumSales)

        let id = nanoid()
        if (editShow) {
            let tempArr = salesList.map(item => {
                if (item.id === rowId) {
                    return {...salesInfo, ...addData, id: item.id}
                } else {
                    return {...item}
                }
            })
            setSalesList(tempArr)
            Toast.show({
                icon: 'success',
                content: '编辑成功',

            })
        } else {
            setSalesList([...salesList, { ...salesInfo, ...addData,  id: id}])
            Toast.show({
                icon: 'success',
                content: '新增成功',

            })
        }
        setSalesAmountVisible(false)
        setSalesInfo(initSalesInfo)
        parentRef.current.resetData()
    }
    const handleCancel = async () => {
        setSalesAmountVisible(false)
        setSalesInfo(initSalesInfo)
        parentRef.current.resetData()
    }

    const [btnDisabled, setBtnDisabled] = useState(false)

    const onSubmit = async () => {
        await form.validateFields()
        const values = form.getFieldsValue()
        // console.log(values, 11111);
        values.hasSales = values.hasSales === 1 ? true : false
        // values.structureId = values.structureId
        values.structureStatus = values.structureStatus && values.structureStatus[0]
        values.structureType = values.structureType && values.structureType[0]
        values.salesCaculateType = values.salesCaculateType && values.salesCaculateType[0]
        let tempArr = []
        tempArr = salesList.map(item => {
            return {
                prdBrand: item.prdBrand,
                prdName: item.prdName,
                spec: item.spec,
                prdCode: item.prdCode,
                threeLvlVarietCode: item.threeLvlVarietCode,
                threeLvlVarietName: item.threeLvlVarietName,
                priceUnit: item.priceUnit,
                sumSales: item.sumSales,
                buyNum: item.buyNum,
                productCode: item.productCode,
            }
        })
        console.log()
        if (formType === 'update') {
            if (hasSales === 1 && tempArr.length === 0) {
                Dialog.alert({
                  content: "请添加纯销额",
                  onConfirm: () => {
                    console.log("Confirmed");
                  },
                });
              } else {
                setBtnDisabled(true)
                await http1.post('/CosmetologyReport/daily/report/submit', {
                    type: 1,
                    ...values,
                    products: tempArr,
                    id: id,
                }).then(res => {
                    setBtnDisabled(false)
                    Dialog.alert({
                        content: '提交成功',
                        onConfirm: () => {
                            console.log('Confirmed')
                        }
                    })
                    navigate("/category/aestheticMedicine/aestheticMedicineDayReportUpload/dataQuery");
                }, err => {
                    setBtnDisabled(false)
                    Dialog.alert({
                        content: '服务器异常，请稍后重试~',
                        onConfirm: () => {
                            console.log('Confirmed')
                        }
                    })
                })
            }
        } else {
            if (hasSales === 1 && tempArr.length === 0) {
                Dialog.alert({
                  content: "请添加纯销额",
                  onConfirm: () => {
                    console.log("Confirmed");
                  },
                });
              } else {
                setBtnDisabled(true)
                await http1.post('/CosmetologyReport/daily/report/submit', {
                    type: 1,
                    ...values,
                    products: tempArr,
                }).then(res => {
                    setBtnDisabled(false)
                    Dialog.alert({
                        content: '提交成功',
                        onConfirm: () => {
                            console.log('Confirmed')
                        }
                    })
                    navigate("/category/aestheticMedicine/aestheticMedicineDayReportUpload/dataQuery");
                }, err => {
                    console.log(11111,err);
                    setBtnDisabled(false)
                    Dialog.alert({
                        content: '服务器异常，请稍后重试~',
                        onConfirm: () => {
                            console.log('Confirmed')
                        }
                    })
                })

            }
        }
    }

    const [deleteReportVisible, setDeleteReportVisible] = useState(false)

    // 机构名称查询
    const [structureName, setStructureName] = useState('')
    const handleQueryName = (val) => {
        setStructureName(val)
  }

  const [structureShow, setStructureShowShow] = useState(false)

    const onSearchStructureName = () => {
        http1.post('/hospital/list/query', { page: 1, size: 20, hospitalName: structureName, query: {hospitalName: "like"}}).then((res) => {
            console.log(res);
            let temp = []
            temp = res.length > 0 && res.map(item => {
                return { label: item.hospitalName, value: item.hospitalId}
            })
            if (temp.length > 0) {
                setStructureShowShow(true)
                setStructureNameList([temp])
            } else {
                setStructureShowShow(false)
                setStructureNameList([[{ label: '', value: '' }]])
            }
        })
    }

    const deleteReportConfirm = async () => {
        await http1.post("/CosmetologyReport/daily/report/delete", { id: id }).then(res => {
            Dialog.alert({
                content: '删除成功',
                onConfirm: () => {
                console.log('Confirmed')
                }
            })
        });
        await navigate("/category/aestheticMedicine/aestheticMedicineDayReportUpload/dataQuery");
      }

    const handleTarget = () => {
        history.back();
    }

    return (
        <div className={dataForm.dataForm}>
        <div className={dataForm.top}>
            <ChevronLeftIcon
                sx={{ fontSize: 50 }}
                onClick={() => {
                  handleTarget();
            }}
          />医美<DoubleArrowIcon fontSize={'small'}/>日战报<DoubleArrowIcon fontSize={'small'}/>数据上报</div>
            <div className={dataForm.content}>
                <CategoryLayoutUpload>
                    {/*header*/}
                    <>
                        <div className={dataForm.header}>
                            <div className={dataForm.headerContext}>
                                <PostAddIcon className={dataForm.fontMain} fontSize={'large'}/>
                                <div style={{fontWeight: 'bold'}}>
                                    数据上报
                                </div>
                            </div>
                            <div className={dataForm.headerContext}>
                                <span className={dataForm.fontMain} style={{fontWeight: 'bold'}}>上报人:</span>
                                <span className={dataForm.fontSecond}>{empname}</span>
                            </div>
                        </div>
                    </>
                    {/*body*/}
                    <>
                        <div className={dataForm.container}>
                            <Form form={form}>
                                <Form.Item
                                    label='上报日期'
                                    trigger='onConfirm'
                                    rules={[{ required: true }]}
                                >
                                    <Input disabled defaultValue={dayjs(new Date()).format('YYYY-MM-DD')}></Input>
                                </Form.Item>
                                <Form.Item
                                    name='hasSales'
                                    label='当日是否有销量'
                                    rules={[{ required: true, message: '请选择当日是否有销量' }]}
                                    initialValue={1}

                                >
                                    <Radio.Group>
                                        <Space>
                                            <Radio value={1}>是</Radio>
                                            <Radio value={2}>否</Radio>
                                        </Space>
                                    </Radio.Group>
                                </Form.Item>
                                {
                                    hasSales === 1 ?
                                        <div>
                                            <Form.Item
                                              name="structureId"
                                              label="机构名称"
                                              trigger="onConfirm"
                                              rules={[{ required: true }]}
                                              onClick={() => setStructureNameVisible(true)}
                                            >
                                              {structureInfo.structureName ? <Input value={structureInfo.structureName} /> : null}
                                              <Popup
                                                    visible={structureNameVisible}
                                                    onMaskClick={() => {
                                                        setStructureNameVisible(false)
                                                    }}
                                                    bodyStyle={{
                                                        borderTopLeftRadius: '8px',
                                                        borderTopRightRadius: '8px',
                                                        minHeight: '40vh',
                                                    }}
                                                >
                                                <div className={'adm-picker-header'} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', height: '42px' }}>
                                                        <span className={'adm-picker-header-button'} style={{ fontSize: '15px', color: 'var(--gensci-main)' }} onClick={() => setStructureNameVisible(false)}>取消</span>
                                                        <span className={'adm-picker-header-button'} style={{ fontSize: '15px', color: 'var(--gensci-main)' }} onClick={handleConfirm}>确认</span>
                                                    </div>
                                                <div
                                                  className={
                                                    dataForm.aestheticMedicineDayReportUpload__searchContainer
                                                  }
                                                >
                                                  <SearchBar placeholder='请输入内容' onChange={e => handleQueryName(e)} style={{ marginRight: "10px", flex: "1" }} />
                                                  <Button
                                                    size={"small"}
                                                    color={"primary"}
                                                    onClick={() => onSearchStructureName()}
                                                  >
                                                    查询
                                                  </Button>
                                                </div>
                                                {structureShow ? (
                                                  <PickerView
                                                    columns={structureNameList}
                                                    onChange={(value, extend) => {
                                                        setTempStructure(extend.items[0] && extend.items[0])
                                                    }}
                                                  ></PickerView>
                                                ) : (
                                                  <div
                                                    style={{
                                                      width: "60px",
                                                      margin: "80px auto",
                                                      color: "#ccc",
                                                    }}
                                                  >
                                                    暂无数据
                                                  </div>
                                                )}
                                              </Popup>
                                            </Form.Item>
                                            <Form.Item
                                                name='structureStatus'
                                                label='机构准入状态'
                                                trigger='onConfirm'
                                                rules={[{ required: true, message: '请选择机构准入状态' }]}
                                                onClick={(e, pickerRef) => {
                                                    pickerRef.current?.open()
                                                }}
                                            >
                                                <Picker
                                                    columns={structureStatusList}
                                                >
                                                    {items => {
                                                        if (items.every(item => item === null)) {
                                                            return ''
                                                        } else {
                                                            return items.map(item => item?.label ?? '未选择')
                                                        }
                                                    }}
                                                </Picker>
                                            </Form.Item>
                                            <Form.Item
                                                name='structureType'
                                                label='机构类型'
                                                trigger='onConfirm'
                                                rules={[{ required: true, message: '请选择机构类型' }]}
                                                onClick={(e, pickerRef) => {
                                                    pickerRef.current?.open()
                                                }}
                                            >
                                                {/* <Input disabled value={aestheticInfo.structureType} /> */}
                                                <Picker
                                                    columns={structureTypeList}
                                                >
                                                    {items => {
                                                        if (items.every(item => item === null)) {
                                                            return ''
                                                        } else {
                                                            return items.map(item => item?.label ?? '未选择')
                                                        }
                                                    }}
                                                </Picker>
                                            </Form.Item>
                                            <Form.Item
                                                name='salesCaculateType'
                                                label='销量核算类型'
                                                trigger='onConfirm'
                                                rules={[{ required: true, message: '请选择销量核算类型' }]}
                                                onClick={(e, pickerRef) => {
                                                    pickerRef.current?.open()
                                                }}
                                            >
                                                <Picker
                                                    columns={salesCaculateTypeList}
                                                >
                                                    {items => {
                                                        if (items.every(item => item === null)) {
                                                            return ''
                                                        } else {
                                                            return items.map(item => item?.label ?? '未选择')
                                                        }
                                                    }}
                                                </Picker>
                                            </Form.Item>
                                        </div> : null
                                }
                            </Form>
                            <List header=' '>
                                <List.Item>
                                    <div className={dataForm.countFunc}>
                                        <div style={{display: 'flex', alignItems: 'center'}}>
                                            <AnalyticsIcon className={dataForm.fontMain} fontSize={dataForm.large}/>
                                            <span style={{fontWeight: 'bold', marginRight: '5px'}}>纯销额</span>
                                        </div>
                                        <div style={{display: 'flex', alignItems: 'center'}}>
                                            <span style={{color: '#ababab', marginRight: '5px'}}>(单位: 元)</span>
                                            <Button size='small' disabled={hasSales === 2} color='primary' onClick={addSalesAmount}>
                                                新增
                                            </Button>
                                            <Dialog
                                                visible={salesAmountVisible}
                                                title={'纯销额新增'}
                                                content={<AddSalesAmount parentRef={parentRef} salesInfo={salesInfo} setSalesInfo={setSalesInfo} />}
                                                closeOnAction
                                                actions={editShow ?
                                                    [[
                                                        {
                                                            key: 'cancel',
                                                            text: <Button size='small' fill='outline'>取消</Button>,
                                                            // danger: true,
                                                            onClick: handleCancel
                                                        },
                                                        {
                                                            key: 'delete',
                                                            text: <Button color='danger' size='small'>删除</Button>,
                                                            danger: true,
                                                            onClick: handleDelete
                                                        },
                                                        {
                                                            key: 'confirm',
                                                            text: <Button color='primary' size='small'>确定</Button>,
                                                            // bold: true,
                                                            onClick: handleAdd
                                                        },
                                                    ]] : [
                                                    [
                                                        {
                                                            key: 'cancel',
                                                            text: <Button size='small' fill='outline'>取消</Button>,
                                                            danger: true,
                                                            onClick: handleCancel
                                                        },
                                                        {
                                                            key: 'confirm',
                                                            text: <Button color='primary' size='small'>确定</Button>,
                                                            bold: true,
                                                            onClick: handleAdd
                                                        },
                                                    ]
                                                ]}
                                            />
                                            <Dialog
                                              visible={deleteRowVisible}
                                              title={"删除"}
                                              content="是否确认删除？"
                                              closeOnAction
                                              onClose={() => {
                                                setDeleteRowVisible(false);
                                              }}
                                              actions={[
                                                [
                                                  {
                                                    key: "cancel",
                                                    text: "取消",
                                                    danger: true,
                                                  },
                                                  {
                                                    key: "confirm",
                                                    text: "确定",
                                                    bold: true,
                                                    onClick: deleteRowConfirm,
                                                  },
                                                ],
                                              ]}
                                            />
                                        </div>
                                    </div>
                                    <DataGrid columns={columns} headerAlign="center" rows={salesList} hideFooter={true} style={{height: '220px'}} disableColumnMenu />
                                </List.Item>
                            </List>
                        </div>
                    </>
                    {/*footer*/}
                    <>
                        <div className={dataForm.footer}>
                            {formType === "create" ? (
                              <Button
                                disabled={btnDisabled}
                                block
                                color="primary"
                                size="middle"
                                style={{ marginTop: "5px", width: "calc(100% - 20px)" }}
                                onClick={onSubmit}
                              >
                                提交
                              </Button>
                            ) : reporter === "management" ? null : (
                              <div style={{ display: "flex" }}>
                                <Button
                                  disabled={btnDisabled}
                                  color="primary"
                                  size="middle"
                                  style={{ margin: "5px 20px 0 0 ", width: "130px" }}
                                  onClick={onSubmit}
                                >
                                  更新
                                </Button>
                                <Button
                                  color="primary"
                                  size="middle"
                                  style={{ marginTop: "5px", width: "130px" }}
                                  onClick={() => {
                                    setDeleteReportVisible(true);
                                  }}
                                >
                                  删除
                                </Button>
                                <Dialog
                                  visible={deleteReportVisible}
                                  title={"删除"}
                                  content="删除该上报后，该数据一并删除，是否确认？"
                                  closeOnAction
                                  onClose={() => {
                                    setDeleteReportVisible(false);
                                  }}
                                  actions={[
                                    [
                                      {
                                        key: "cancel",
                                        text: "取消",
                                        danger: true,
                                      },
                                      {
                                        key: "confirm",
                                        text: "确定",
                                        bold: true,
                                        onClick: deleteReportConfirm,
                                      },
                                    ],
                                  ]}
                                />
                              </div>
                            )}
                        </div>
                    </>
                </CategoryLayoutUpload>
            </div>
        </div>
    )
}

export default DataForm
