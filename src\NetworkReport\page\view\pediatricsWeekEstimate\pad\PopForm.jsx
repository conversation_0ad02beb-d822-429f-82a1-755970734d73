import React, {useEffect, useState} from 'react';
import {Button, Dialog, Popup, SafeArea, Space, TextArea} from 'antd-mobile';
import styled from 'styled-components';

const PopWrapper = styled(Popup)`
  .adm-popup-close-icon {
    color: #fff !important;
  }
`;

const Wrapper = styled.div`
	.title {
		text-align: center;
		height: 50px;
		line-height: 50px;
		font-size: 16px;
		font-weight: 500;
		background: rgb(22, 93, 255);
		color: #fff;
	}
  .area {
    margin: 10px;
    .t {
      padding-bottom: 5px;
    }
    .adm-text-area-element {
      border: 1px solid #f1f1f1;
    }
  }
  .topic {
    color: red;
    padding: 10px;
    line-height: 22px;
  }
	.submit-wrapper {
		text-align: center;
	}
`;

const PopForm = (props) => {
	const [visible, setVisible] = useState(false);
	const [coreActions, setCoreActions ] = useState(props.value && props.value.coreActions);
	const [pointOfGrowth, setPointOfGrowth  ] = useState(props.value && props.value.pointOfGrowth);

	useEffect(() => {
		if(!visible) {
			setCoreActions(null);
			setPointOfGrowth(null);
		}
	}, [visible])

	useEffect(() => {
		setCoreActions(props.value.coreActions);
		setPointOfGrowth(props.value.pointOfGrowth);
	}, [props.value, visible])

	return (
		<>
			<PopWrapper
				visible={visible}
				onMaskClick={() => {
					setVisible(false)
				}}
				onClose={() => {
					setVisible(false)
				}}
				bodyStyle={{ height: '80vh' }}
				showCloseButton
			>
				<Wrapper>
					<div className="title">
						请填写{props.estDate}增长点与行动计划
					</div>
					<div className="text">
						<div className="area">
							<div className="t">{props.estDate}增长点</div>
							<TextArea
								showCount
								maxLength={500}
								autoSize={{ minRows: 5, maxRows: 5 }}
								onChange={setPointOfGrowth}
								value={pointOfGrowth}
							/>
						</div>
						<div className="area">
							<div className="t">{props.estDate}行动计划</div>
							<TextArea
								showCount
								maxLength={500}
								autoSize={{ minRows: 5, maxRows: 5 }}
								onChange={setCoreActions}
								value={coreActions}
							/>
						</div>
					</div>
					<div className="submit-wrapper">
						<Button
							color='primary'
							fill='solid'
							// onClick={handleSave}
							// loading={loading}
							style={{ width: 'calc(20vw)', background: 'rgb(22, 93, 255)', color: '#fff' }}
							onClick={() => {
								props.handleSave({ coreActions, pointOfGrowth });
								setVisible(false);
							}}
						>
							保存
						</Button>
					</div>
					<SafeArea position={'bottom'} />
				</Wrapper>
			</PopWrapper>
			{props.children(setVisible)}
		</>
	)
}

export default PopForm;
