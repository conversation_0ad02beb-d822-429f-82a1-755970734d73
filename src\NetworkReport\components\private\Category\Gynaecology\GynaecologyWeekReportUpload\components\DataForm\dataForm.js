import React, {useState, useMemo, useEffect, useRef} from 'react';
import dataForm from './dataForm.less'

import { useNavigate, useLocation } from 'react-router-dom'

import { Button, Input, Dialog, Form, List, PickerView, Popup, Radio, SearchBar, Space, Toast } from "antd-mobile";
import {DataGrid, GridActionsCellItem} from "@mui/x-data-grid";
import DeleteIcon from "@mui/icons-material/Delete";
import DoubleArrowIcon from "@mui/icons-material/DoubleArrow";
import CategoryLayoutUpload from "../../../../../../public/CategoryLayoutUpload/categoryLayoutUpload";
import PostAddIcon from "@mui/icons-material/PostAdd";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";

import dayjs from "dayjs";
import AnalyticsIcon from "@mui/icons-material/Analytics";
import AddSalesAmount from "./AddSalesAmount/addSalesAmount";
import AddNewPatient from './AddNewPatient/addNewPatient'
import PeopleIcon from "@mui/icons-material/People";
import { nanoid } from 'nanoid'

import { http1 } from '../../../../../../../utils/network';
import { createHashHistory } from "history";

const DataForm = ({formType}) => {
    
    const [form] = Form.useForm()
    const hospitalId = Form.useWatch('hospitalId', form)
    const hasSales = Form.useWatch('hasSales', form)

    const addSalesRef = useRef(null)
    const addPatientRef = useRef(null)

    const history = createHashHistory()
  
  const navigate = useNavigate()
  
  const info = useLocation();
  if (info.state) { 
    var id = info.state.id;
    var time = info.state.time;
    var reporter = info.state.reporter;
  }
    console.log(info, id);

    const [empname, setEmpname] = useState('') // 上报人
    const [prescriptionHospitalList, setPrescriptionHospitalList] = useState([]) //处方医院
    const [prescriptionHospitalShow, setPrescriptionHospitalShow] = useState(false)
    const [prescriptionHospitalInfo, setPrescriptionHospitalInfo] = useState({
        hospitalId: '',
        hospitalLabel: '',
    })

    const [drugHospitalList, setDrugHospitalList] = useState([])  //购药医院
    const [drugHospitalShow, setDrugHospitalShow] = useState(false)
    const [salesStructureInfo, setSalesStructureInfo] = useState({
        salesStructureId: '',
        salesStructureLabel: '',
    })

    const [deleteReportVisible, setDeleteReportVisible] = useState(false)
   
    useEffect(() => { 
        // 填报人信息
        http1.post('/account/api/userInfo', {}).then(res => { 
            setEmpname(res.empname)
        })
        // 处方医院枚举
        http1.post('/hospital/list/query',{ page: 1, size: 20 }).then((res) => { 
            let temp = []
            temp = res.length > 0 && res.map(item => { 
                return { label: item.hospitalName, value: item.hospitalId}
            })
          if (temp.length > 0) {
                setPrescriptionShow(true)
                setSalesShow(true)
                setPrescriptionHospitalList([temp])
                setDrugHospitalList([temp])
          } else { 
                setPrescriptionShow(true)
                setSalesShow(false)
                setPrescriptionHospitalList([[{ label: '', value: '' }]])
                setDrugHospitalList([[{ label: '', value: '' }]])
            }
        })
    }, [])
    if (formType === 'update') { 
        useEffect(() => {
          http1
            .post("/gynaecologyWeeklyReport/report/get", { id: id })
            .then((res) => {
              setEmpname(res.createdName)
              form.setFieldsValue({
                hasSales:
                  res.hasSales !== null && res.hasSales !== undefined
                    ? res.hasSales ? 1 : 2
                    : [],
                hospitalId:
                  res.hospitalId !== null && res.hospitalId !== undefined
                    ? res.hospitalId
                    : [],
                salesStructureId:
                  res.salesStructureId !== null &&
                  res.salesStructureId !== undefined
                    ? res.salesStructureId
                    : [],
              });
              console.log(form.getFieldsValue());
              setPrescriptionHospitalInfo({
                ...prescriptionHospitalInfo,
                hospitalLabel: res.hospitalName
              });
              setSalesStructureInfo({
                ...salesStructureInfo,
                salesStructureLabel: res.salesStructureName
              });
            });
          if (reporter) {
            http1
              .post("/gynaecologyWeeklyPatients/patients/query", {
                reportId: id,
                branchEmpNo: true,
              })
              .then((res) => {
                console.log(res);
                setNewPatientList([...res]);
              });
            http1
              .post("/gynaecologyWeeklySales/sales/query", {
                reportId: id,
                branchEmpNo: true,

              })
              .then((res) => {
                console.log(res);
                setSalesAmountList([...res]);
              });
          } else { 
             http1
               .post("/gynaecologyWeeklyPatients/patients/query", {
                 reportId: id,
               })
               .then((res) => {
                 console.log(res);
                 setNewPatientList([...res]);
               });
            http1
              .post("/gynaecologyWeeklySales/sales/query", {
                reportId: id,
              })
              .then((res) => {
                console.log(res);
                setSalesAmountList([...res]);
              });
          }
        }, []);
    }
    //纯销额表数据
    const [salesAmountList, setSalesAmountList] = useState([])
    const [salesAmountVisible, setSalesAmountVisible] = useState(false)
    const [salesAmountInfo, setSalesAmountInfo] = useState({
      drugName: "",
      specificationsName: "",
    });
    // 新增弹窗
    const addSalesAmount = () => {
        setSalesAmountVisible(true)
    }
    //增加行
  const addSalesAmountRow = async () => {
        await addSalesRef.current.handleValidate()
        let addData = addSalesRef.current.childHandleAdd()
        console.log(addData);
        addData.drugId = addData.drugId && addData.drugId[0]
        addData.specifications = addData.specifications && addData.specifications[0]
        // addData.volume = addData.volume
        addData.priceUnit = Number(addData.priceUnit)
        addData.num = Number(addData.num)
        let id = nanoid()

            setSalesAmountList([...salesAmountList, { ...addData, ...salesAmountInfo, id: id, type: 'add' }])
            setSalesAmountVisible(false)
            Toast.show({
                icon: 'success',
                content: '新增成功',
         
            })
          addSalesRef.current.resetData()
    }

    const cancelSalesAmountRow = ()=> {
        setSalesAmountVisible(false)
        addSalesRef.current.resetFields()
    }
    //删除行
    const deleteSalesAmountRow = (id) => setSalesAmountList((prevRows) => prevRows.filter((row) => row.id !== id))

    //表单头
    const salesAmountColumns = useMemo(
      () => [
        { field: "drugName", headerName: "产品" },
        { field: "specificationsName", headerName: "规格" },
        { field: "volume", headerName: "数量", type: "number" },
        { field: "priceUnit", headerName: "单价", type: "string" },
        { field: "num", headerName: "销量", type: "string" },
        {
          field: "actions",
          headerName: "操作",
          type: "actions",
          width: 80,
          getActions: (params) => [
            <GridActionsCellItem
              icon={<DeleteIcon />}
              label="Delete"
              onClick={() => deleteSalesAmountRow(params.id)}
            />,
          ],
        },
      ],
      [deleteSalesAmountRow]
    );

    const [code3Info, setCode3Info] = useState({
      doctorCode: '',
      doctorName: ''
  }) 

    //新患数表数据
    let [newPatientList, setNewPatientList] = useState([])
    const [newPatientVisible, setNewPatientVisible] = useState(false)
    const [newPatientInfo, setNewPatientInfo] = useState({
      doctorName: "",
      isPrescriptionLabel: "",
      drugName: "",
    });

    const addNewPatient = () => {
        setNewPatientVisible(true)
    }
    //增加行
    const addNewPatientRow = async () => {
        await addPatientRef.current.handleValidate()
        let addData = addPatientRef.current.childHandleAdd()
        console.log(addData);
        // addData.isPrescription = addData.isPrescription
        addData.drugId = addData.drugId && addData.drugId[0]
        addData.num = Number(addData.num)
        let id = nanoid()
            setNewPatientList([...newPatientList, { ...addData, isPrescription: addData.isPrescription[0], ...newPatientInfo,doctorName: code3Info.doctorName, id: id, type: 'add' }])
            setNewPatientVisible(false)
            Toast.show({
                icon: 'success',
                content: '新增成功',
         
            })
          addPatientRef.current.resetData()
          setCode3Info({...code3Info, doctorName: null})
          newPatientList = newPatientList.map(item => {
            return {
                doctorCode: item.doctorCode,
                isPrescription: item.isPrescription,
                drugId: item.drugId,
                num: item.num,
            }
        })
    }
    const cancelNewPatientRow = () => {
        setNewPatientVisible(false)
        setCode3Info({...code3Info, doctorName: null})
        addPatientRef.current.resetFields()
    }
    //删除行
    const deleteNewPatientRow = (id) => setNewPatientList((prevRows) => prevRows.filter((row) => row.id !== id))
    
    //表单头
    const newPatientColumns = useMemo(
      () => [
        { field: "doctorName", headerName: "编码3", type: "string" },
        {
          field: "isPrescription",
          headerName: "是否新处方客户",
          valueFormatter: (val) => (val.value ? "是" : "否"),
        },
        { field: "drugName", headerName: "产品", type: "string" },
        { field: "num", headerName: "新患数", type: "number" },
        {
          field: "actions",
          headerName: "操作",
          type: "actions",
          width: 80,
          getActions: (params) => [
            <GridActionsCellItem
              icon={<DeleteIcon />}
              label="Delete"
              onClick={() => deleteNewPatientRow(params.id)}
            />,
          ],
        },
      ],
      [deleteNewPatientRow]
    );

    const [btnDisabled, setBtnDisabled] = useState(false)
    //表单提交
  const onSubmit = async () => {
        await form.validateFields()
        let values = form.getFieldsValue()
        console.log(values);
        values.hasSales = values.hasSales === 1 ? true : false
        // values.hospitalId = values.hospitalId && values.hospitalId[0];
        // values.salesStructureId = values.salesStructureId && values.salesStructureId[0]
        
        let tempArr1 = []
        tempArr1 = salesAmountList.map(item => { 
          if (item.type === 'add') {
            return {
              drugId: item.drugId,
              specifications: item.specifications,
              volume: item.volume,
              priceUnit: item.priceUnit,
              num: item.num,
            }
          } else { 
            return {
                drugId: item.drugId,
                specifications: item.specifications,
                volume: item.volume,
                priceUnit: item.priceUnit,
                num: item.num,
                id: item.id,
                reportId: item.reportId
            }
          }
        })

        let tempArr2 = []
        tempArr2 = newPatientList.map(item => {
          if (item.type === 'add') {
            return {
              doctorCode: item.doctorCode,
                isPrescription: item.isPrescription,
                drugId: item.drugId,
                num: item.num,
            }
          } else { 
            return {
                doctorCode: item.doctorCode,
                isPrescription: item.isPrescription,
                drugId: item.drugId,
                num: item.num,
                id: item.id,
                reportId: item.reportId
            }
          }
        })
        console.log(tempArr1,111);
    if (formType === 'update') {
      if (hasSales === 1 && tempArr1.length === 0 && tempArr2.length === 0) {
        Dialog.alert({
          content: "请添加纯销额或新患数",
          onConfirm: () => {
            console.log("Confirmed");
          },
        });
      } else {
        setBtnDisabled(true)
        await http1
          .post("/gynaecologyWeeklyReport/report/submit", {
            ...values,
            sales: tempArr1,
            patients: tempArr2,
            reporter: empname,
            id: id,
          }).then((res) => {
            setBtnDisabled(false)
            Dialog.alert({
              content: "更新成功",
              onConfirm: () => {
                console.log("Confirmed");
              },
            });
            navigate("/category/gynaecology/gynaecologyWeekReportUpload/dataQuery");
          }, err => {
            setBtnDisabled(false)
            Dialog.alert({
              content: "服务器异常，请稍后重试~",
              onConfirm: () => {
                console.log("Confirmed");
              },
            });
          });
      }
    } else {
      if (hasSales === 1 && tempArr1.length === 0 && tempArr2.length === 0) {
        Dialog.alert({
          content: "请添加纯销额或新患数",
          onConfirm: () => {
            console.log("Confirmed");
          },
        });
      } else {
        setBtnDisabled(true)
        await http1
          .post("/gynaecologyWeeklyReport/report/submit", {
            ...values,
            sales: tempArr1,
            patients: tempArr2,
            reporter: empname,
          })
          .then((res) => {
            setBtnDisabled(false)
            Dialog.alert({
              content: "提交成功",
              onConfirm: () => {
                console.log("Confirmed");
              },
            });
            navigate(
              "/category/gynaecology/gynaecologyWeekReportUpload/dataQuery",
              { state: { hasSales } }
            );
          }, err => {
            setBtnDisabled(false)
            Dialog.alert({
              content: "服务器异常，请稍后重试~",
              onConfirm: () => {
                console.log("Confirmed");
              },
            });
          });
      }
    }
    }

    const handleConfirmHospital = () => { 
        form.setFieldValue('hospitalId', prescriptionHospitalInfo.hospitalId)  
        setPrescriptionHospitalShow(false)
    }

     const handleConfirm = () => { 
        form.setFieldValue('salesStructureId', salesStructureInfo.salesStructureId)  
        setDrugHospitalShow(false)
    }

  // 处方医院查询
    const [prescriptionName, setPrescriptionName] = useState('')
  const handleQueryNamePrescription = (val) => { 
        setPrescriptionName(val)
  }
  
  const [prescriptionShow, setPrescriptionShow] = useState(false)

    const onSearchPrescriptionHospital = () => {
        http1.post('/hospital/list/query', {  page: 1, size: 20, hospitalName: prescriptionName, query: {hospitalName: "like"}}).then((res) => { 
            let temp = []
            temp = res.length > 0 && res.map(item => { 
                return { label: item.hospitalName, value: item.hospitalId}
            })
          if (temp.length > 0) {
            setPrescriptionShow(true)
            setPrescriptionHospitalList([temp])
          } else { 
              setPrescriptionShow(false)
              setPrescriptionHospitalList([[{ label: '', value: '' }]])
            }
         })
    }
    // 购药医院查询
    const [salesName, setSalesName] = useState('')
    const handleQueryNameSales = (val) => { 
        setSalesName(val)
  }
  
  const [salesShow, setSalesShow] = useState(false)

    const onSearchSalesHospital = () => { 
        http1.post('/hospital/list/query', {  page: 1, size: 20, hospitalName: salesName, query: {hospitalName: "like"}}).then((res) => { 
            let temp = []
            temp = res.length > 0 && res.map(item => { 
                return { label: item.hospitalName, value: item.hospitalId}
            })
            if (temp.length > 0) {
              setDrugHospitalList([temp])
              setSalesShow(true)
            } else { 
              setDrugHospitalList([[{ label: '', value: '' }]])
              setSalesShow(false)
            }
        })
    }

    // const handleQueryName = (val, desc) => { 
        
    //     http1.post('/hospital/list/query', {  page: 1, size: 20, hospitalName: val, query: {hospitalName: "like"}}).then((res) => { 
    //         console.log(res);
    //         let temp = []
    //         temp = res.length > 0 && res.map(item => { 
    //             return { label: item.hospitalName, value: item.hospitalId}
    //         })
    //         if (temp.length > 0) {
    //             if (desc === 'prescription') {
    //                 setPrescriptionHospitalList([temp])
    //             } else { 
    //                 setDrugHospitalList([temp])
    //             }
    //         } else { 
    //             setPrescriptionHospitalList([[{ label: '', value: '' }]])
    //             setDrugHospitalList([[{ label: '', value: '' }]])
    //         }
    //     })
    // }

    const handleUpdate = () => { 
        onSubmit()
    }

    const deleteReportConfirm = async () => { 
      await http1.post("/gynaecologyWeeklyReport/report/delete", { id: id });
      await navigate("/category/gynaecology/gynaecologyWeekReportUpload/dataQuery");
    }

  const handleTarget = () => { 
    history.back();
  }

    return (
      <div className={dataForm.dataForm}>
        <div className={dataForm.top}>
          <ChevronLeftIcon
            sx={{ fontSize: 50 }}
            onClick={() => {
              handleTarget();
            }}
          />
          妇科
          <DoubleArrowIcon fontSize={"small"}/>
          周战报
          <DoubleArrowIcon fontSize={"small"} />
          数据上报
        </div>
        <div className={dataForm.content}>
          <CategoryLayoutUpload>
            {/*header*/}
            <>
              <div className={dataForm.header}>
                <div className={dataForm.headerContext}>
                  <PostAddIcon
                    className={dataForm.fontMain}
                    fontSize={"large"}
                  />
                  <div style={{ fontWeight: "bold" }}>数据上报</div>
                </div>
                <div className={dataForm.headerContext}>
                  <span
                    className={dataForm.fontMain}
                    style={{ fontWeight: "bold" }}
                  >
                    上报人:
                  </span>
                  <span className={dataForm.fontSecond}>{empname}</span>
                </div>
              </div>
            </>
            {/*body*/}
            <>
              <div className={dataForm.container}>
                <Form form={form} initialValues={{}}>
                  <Form.Item
                    label="上报日期"
                    trigger="onConfirm"
                    rules={[{ required: true }]}
                  >
                    <Input
                      disabled
                      defaultValue={
                        formType === "update"
                          ? dayjs(time).format("YYYY-MM-DD")
                          : dayjs(new Date()).format("YYYY-MM-DD")
                      }
                    ></Input>
                  </Form.Item>
                  <Form.Item
                    name="hasSales"
                    label="当日是否有销量"
                    rules={[{ required: true }]}
                  >
                    <Radio.Group>
                      <Space>
                        <Radio value={1}>有</Radio>
                        <Radio value={2}>无</Radio>
                      </Space>
                    </Radio.Group>
                  </Form.Item>
                  {hasSales === 2 ? null : (
                    <>
                      <Form.Item
                        name="hospitalId"
                        label="处方医院"
                        trigger="onConfirm"
                        rules={[{ required: true }]}
                        onClick={() => setPrescriptionHospitalShow(true)}
                      >
                        {prescriptionHospitalInfo.hospitalLabel ? (
                          <Input
                            value={prescriptionHospitalInfo.hospitalLabel}
                          />
                        ) : null}
                        <Popup
                          visible={prescriptionHospitalShow}
                          onMaskClick={() => {
                            setPrescriptionHospitalShow(false);
                          }}
                          bodyStyle={{
                            borderTopLeftRadius: "8px",
                            borderTopRightRadius: "8px",
                            minHeight: "40vh",
                          }}
                        >
                          <div
                            className={"adm-picker-header"}
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                              height: "42px",
                            }}
                          >
                            <span
                              className={"adm-picker-header-button"}
                              style={{
                                fontSize: "15px",
                                color: "var(--gensci-main)",
                              }}
                              onClick={() => setPrescriptionHospitalShow(false)}
                            >
                              取消
                            </span>
                            <span
                              className={"adm-picker-header-button"}
                              style={{
                                fontSize: "15px",
                                color: "var(--gensci-main)",
                              }}
                              onClick={handleConfirmHospital}
                            >
                              确认
                            </span>
                          </div>
                          <div
                            className={
                              dataForm.gynaecologyWeekReportUpload__searchContainer
                            }
                          >
                            <SearchBar
                              placeholder="请输入内容"
                              onChange={(e) => handleQueryNamePrescription(e)}
                              style={{ marginRight: "10px", flex: "1" }}
                            />
                            <Button
                              size={"small"}
                              color={"primary"}
                              onClick={() => onSearchPrescriptionHospital()}
                            >
                              查询
                            </Button>
                          </div>
                          {prescriptionShow ? (
                            <PickerView
                              columns={prescriptionHospitalList}
                              onChange={(value, extend) => {
                                setPrescriptionHospitalInfo({
                                  ...prescriptionHospitalInfo,
                                  hospitalId: extend.items[0].value,
                                  hospitalLabel: extend.items[0].label,
                                });
                                form.setFields([
                                  {
                                    name:'hospitalId', value:value, errors:null
                                  }
                                ])
                              }}
                            ></PickerView>
                          ) : (
                            <div
                              style={{
                                width: "60px",
                                margin: "80px auto",
                                color: "#ccc",
                              }}
                            >
                              暂无数据
                            </div>
                          )}
                        </Popup>
                      </Form.Item>
                      <Form.Item
                        name="salesStructureId"
                        label="购药医院"
                        trigger="onConfirm"
                        rules={[{ required: true, message: "请选择机构名称" }]}
                        onClick={() => setDrugHospitalShow(true)}
                      >
                        {salesStructureInfo.salesStructureLabel ? (
                          <Input
                            value={salesStructureInfo.salesStructureLabel}
                          />
                        ) : null}
                        <Popup
                          visible={drugHospitalShow}
                          onMaskClick={() => {
                            setDrugHospitalShow(false);
                          }}
                          bodyStyle={{
                            borderTopLeftRadius: "8px",
                            borderTopRightRadius: "8px",
                            minHeight: "40vh",
                          }}
                        >
                          <div
                            className={"adm-picker-header"}
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                              height: "42px",
                            }}
                          >
                            <span
                              className={"adm-picker-header-button"}
                              style={{
                                fontSize: "15px",
                                color: "var(--gensci-main)",
                              }}
                              onClick={() => setDrugHospitalShow(false)}
                            >
                              取消
                            </span>
                            <span
                              className={"adm-picker-header-button"}
                              style={{
                                fontSize: "15px",
                                color: "var(--gensci-main)",
                              }}
                              onClick={handleConfirm}
                            >
                              确认
                            </span>
                          </div>
                          <div
                            className={
                              dataForm.gynaecologyWeekReportUpload__searchContainer
                            }
                          >
                            <SearchBar
                              placeholder="请输入内容"
                              onChange={(e) => handleQueryNameSales(e)}
                              style={{ marginRight: "10px", flex: "1" }}
                            />
                            <Button
                              size={"small"}
                              color={"primary"}
                              onClick={() => onSearchSalesHospital()}
                            >
                              查询
                            </Button>
                          </div>
                          {salesShow ? (
                            <PickerView
                              columns={drugHospitalList}
                              onChange={(value, extend) => {
                                setSalesStructureInfo({
                                  ...salesStructureInfo,
                                  salesStructureId: extend.items[0].value,
                                  salesStructureLabel: extend.items[0].label,
                                });
                                form.setFields([
                                  {
                                    name:'salesStructureId', value:value, errors:null
                                  }
                                ])
                              }}
                            ></PickerView>
                          ) : (
                            <div
                              style={{
                                width: "60px",
                                margin: "80px auto",
                                color: "#ccc",
                              }}
                            >
                              暂无数据
                            </div>
                          )}
                        </Popup>
                      </Form.Item>
                    </>
                  )}
                </Form>
                <List header=" ">
                  <List.Item>
                    <div className={dataForm.countFunc}>
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <AnalyticsIcon
                          className={dataForm.fontMain}
                          fontSize={"large"}
                        />
                        <span
                          style={{ fontWeight: "bold", marginRight: "5px" }}
                        >
                          纯销额
                        </span>
                      </div>
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <span style={{ color: "#ababab", marginRight: "5px" }}>
                          (单位: 元)
                        </span>
                        <Button
                          size="small"
                          color="primary"
                          onClick={addSalesAmount}
                          disabled={hasSales === 2}
                        >
                          新增
                        </Button>
                        <Dialog
                          visible={salesAmountVisible}
                          title={"纯销额新增"}
                          content={
                            <AddSalesAmount
                              addSalesRef={addSalesRef}
                              salesAmountInfo={salesAmountInfo}
                              setSalesAmountInfo={setSalesAmountInfo}
                            />
                          }
                          closeOnAction
                          actions={[
                            [
                              {
                                key: "cancel",
                                text: "取消",
                                danger: true,
                                onClick: cancelSalesAmountRow,
                              },
                              {
                                key: "confirm",
                                text: "确定",
                                bold: true,
                                onClick: addSalesAmountRow,
                              },
                            ],
                          ]}
                        />
                      </div>
                    </div>
                    <DataGrid
                      columns={salesAmountColumns}
                      rows={salesAmountList}
                      hideFooter={true}
                      style={{ height: "220px" }}
                      disableColumnMenu
                    />
                  </List.Item>
                  <List.Item>
                    <div className={dataForm.countFunc}>
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <PeopleIcon
                          className={dataForm.fontMain}
                          fontSize={"large"}
                        />
                        <span
                          style={{ fontWeight: "bold", marginRight: "5px" }}
                        >
                          新患数
                        </span>
                      </div>
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <span style={{ color: "#ababab", marginRight: "5px" }}>
                          (单位: 人)
                        </span>
                        <Button
                          size="small"
                          color="primary"
                          onClick={addNewPatient}
                          disabled={hasSales === 2}
                        >
                          新增
                        </Button>
                        <Dialog
                          visible={newPatientVisible}
                          title={"新患数新增"}
                          content={
                            <AddNewPatient
                              newPatientVisible={newPatientVisible}
                              code3Info={code3Info}
                              setCode3Info={setCode3Info}
                              addPatientRef={addPatientRef}
                              hospitalId={hospitalId}
                              newPatientInfo={newPatientInfo}
                              setNewPatientInfo={setNewPatientInfo}
                            />
                          }
                          closeOnAction
                          actions={[
                            [
                              {
                                key: "cancel",
                                text: "取消",
                                danger: true,
                                onClick: cancelNewPatientRow,
                              },
                              {
                                key: "confirm",
                                text: "确定",
                                bold: true,
                                onClick: addNewPatientRow,
                              },
                            ],
                          ]}
                        />
                      </div>
                    </div>
                    <DataGrid
                      columns={newPatientColumns}
                      rows={newPatientList}
                      hideFooter={true}
                      style={{ height: "220px" }}
                      disableColumnMenu
                    />
                  </List.Item>
                </List>
              </div>
            </>
            {/*footer*/}
            <>
              <div className={dataForm.footer}>
                {formType === "create" ? (
                  <Button
                    disabled={btnDisabled}
                    block
                    color="primary"
                    size="middle"
                    style={{ marginTop: "5px", width: "calc(100% - 20px)" }}
                    onClick={onSubmit}
                  >
                    提交
                  </Button>
                ) : reporter === "management" ? null : (
                  <div style={{ display: "flex" }}>
                    <Button
                      disabled={btnDisabled}
                      color="primary"
                      size="middle"
                      style={{ margin: "5px 20px 0 0 ", width: "130px" }}
                      onClick={handleUpdate}
                    >
                      更新
                    </Button>
                    <Button
                      color="primary"
                      size="middle"
                      style={{ marginTop: "5px", width: "130px" }}
                      onClick={() => {
                        setDeleteReportVisible(true);
                      }}
                    >
                      删除
                    </Button>
                    <Dialog
                      visible={deleteReportVisible}
                      title={"删除"}
                      content="删除该上报后，该数据一并删除，是否确认？"
                      closeOnAction
                      onClose={() => {
                        setDeleteReportVisible(false);
                      }}
                      actions={[
                        [
                          {
                            key: "cancel",
                            text: "取消",
                            danger: true,
                          },
                          {
                            key: "confirm",
                            text: "确定",
                            bold: true,
                            onClick: deleteReportConfirm,
                          },
                        ],
                      ]}
                    />
                  </div>
                )}
              </div>
            </>
          </CategoryLayoutUpload>
        </div>
      </div>
    );
}

export default DataForm
