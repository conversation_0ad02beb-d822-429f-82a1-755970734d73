import React, {useEffect, useState} from 'react';
import {Routes, Route} from "react-router";
import { useNavigate } from "react-router-dom";

import {useSearchParams} from "react-router-dom";
import {setToken, isAuth}  from '../../../utils/auth'
import Board from './Board';
import Report from './Report';
import {http1} from "../../../utils/network";
import styled from 'styled-components';
import PadBoard from './pad/Board';
import PadReport from './pad/Report';

const Wrapper = styled.div`
	.user-header {
    height: 44px;
    /* 自动布局 */
    display: flex;
		background: rgb(22, 93, 255);
		color: #fff;
		padding: 12px 16px;
		.name {
			font-size: 14px;
			padding-right: 20px;
			height: 20px;
			line-height: 20px;
		}
		.empNo {
      height: 20px;
			line-height: 20px;
      background: rgb(232, 243, 255);
			color: rgb(22, 93, 255);
			padding: 0 4px;
      border-radius: 2px;
		}
	}
`;


const PedWeekHome = () => {
	const navigate = useNavigate();
	let [searchParams] = useSearchParams()
	const [userInfo, setUserInfo] = useState({});

	useEffect(() => {
		window.document.title = '周新增预估'
		getUserInfo()
	}, [])

	let [tokenState] = useSearchParams()
	let token = tokenState.get('token');
	const channel = tokenState.get('channel');
	if(token){
		setToken(token)
	}
	if(channel) {
		localStorage.setItem('channel', channel)
	}else {
		localStorage.removeItem('channel')
	}

	const getUserInfo = async () => {
		const res = await http1.post(tokenState.get('deptcode') ? '/account/api/deptInfo/estimate' : '/account/api/userInfo/estimate', {}) || {};
		if(tokenState.get('deptcode')) {
			http1.defaults.headers = {
				...http1.defaults.headers,
				deptcode: res.finaleducation
			}
		}else {
			delete http1.defaults.headers.deptcode
		}
		setUserInfo(res);
		window.localStorage.setItem('__userInfo__',JSON.stringify(res));
	}

	useEffect(() => {
		// navigate('/pediatricsWeekEstimate/board');
		const init = async () => {
			// const level = await http1.post('/pediatricsEstimate/query/level');
			const userAgent = window.navigator.userAgent;
			const isMobile = /iphone|ipod|android|blackberry|opera mini|windows ce|nokia/i.test(userAgent);
			const isHonor = /honorbrt/i.test(userAgent);
			if(isHonor) {
				navigate(`/pediatricsWeekEstimate/padBoard?deptcode=${searchParams.get('deptcode') || ''}`, {replace: true})
			}else if(!isMobile) {
				navigate(`/pediatricsWeekEstimate/padBoard?deptcode=${searchParams.get('deptcode') || ''}`, {replace: true})
			}

		};
		init();
	}, [])

	return (
		<Wrapper>
			<div className="user-header">
				<div className="name">{userInfo.empname}</div>
				<div className="empNo">{userInfo.empno && userInfo.empno.slice(0, 8)}</div>
			</div>
			{(isAuth()|| token) ?
				<Routes>
					<Route path={'board'} element={<Board />}></Route>
					<Route path={'padBoard'} element={<PadBoard />}></Route>
					<Route path={'padReport'} element={<PadReport />}></Route>
					<Route path={'boardTBA'} element={<Board />}></Route>
					<Route path={'report'} element={<Report />}></Route>
				</Routes>: null
			}
		</Wrapper>
	)
}

export default PedWeekHome
