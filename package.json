{"name": "network-report-mobile", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "webpackbuildprod": "webpack --config webpack.config/prod.js", "webpackserverdev": "webpack serve --config webpack.config/dev.js", "webpackserverprod": "webpack serve --config webpack.config/prod.js", "webpackcustomize": "webpack --config webpack.config/customize.js", "webpacklint": "webpack --config webpack.config/lint.js", "build": "node script/build.js", "dev": "webpack serve --config webpack.config/dev.js", "customize": "node script/customize.js"}, "author": "", "license": "ISC", "dependencies": {"@babel/runtime": "^7.18.9", "@babel/runtime-corejs3": "^7.18.9", "@emotion/react": "^11.10.0", "@emotion/styled": "^11.10.0", "@mui/icons-material": "^5.8.4", "@mui/material": "^5.10.0", "@mui/x-data-grid": "^5.15.2", "antd": "^5.11.5", "antd-mobile": "^5.32.1", "antd-mobile-icons": "^0.3.0", "axios": "^0.27.2", "big.js": "^6.2.1", "core-js": "^3.24.1", "dayjs": "^1.11.5", "history": "^5.3.0", "lodash": "^4.17.21", "nanoid": "^4.0.0", "normalize.css": "^8.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "^6.3.0", "react-router-dom": "^6.3.0", "recoil": "^0.7.5", "styled-components": "^5.3.9", "tailwindcss": "^3.3.5"}, "devDependencies": {"@babel/cli": "^7.18.10", "@babel/core": "^7.18.10", "@babel/parser": "^7.18.11", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.18.10", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-react": "^7.18.6", "@babel/register": "^7.18.9", "autoprefixer": "^10.4.16", "babel-loader": "^8.2.5", "babel-plugin-import": "^1.13.5", "clean-webpack-plugin": "^4.0.0", "compression-webpack-plugin": "^10.0.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.7.1", "css-minimizer-webpack-plugin": "^4.0.0", "cssnano": "^5.1.13", "eslint": "^8.21.0", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-webpack-plugin": "^3.2.0", "html-loader": "^4.1.0", "html-minimizer-webpack-plugin": "^4.0.0", "html-webpack-plugin": "^5.5.0", "image-minimizer-webpack-plugin": "^3.3.0", "json-minimizer-webpack-plugin": "^4.0.0", "less": "^4.1.3", "less-loader": "^11.0.0", "mini-css-extract-plugin": "^2.6.1", "postcss": "^8.4.31", "postcss-loader": "^7.0.1", "postcss-preset-env": "^7.7.2", "postcss-safe-parser": "^6.0.0", "purgecss-webpack-plugin": "^4.1.3", "style-loader": "^3.3.1", "stylelint": "^14.10.0", "stylelint-config-recommended": "^9.0.0", "stylelint-config-standard": "^27.0.0", "stylelint-webpack-plugin": "^3.3.0", "terser-webpack-plugin": "^5.3.4", "thread-loader": "^3.0.4", "webpack": "^5.74.0", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.10.0", "webpack-manifest-plugin": "^5.0.0", "workbox-webpack-plugin": "^6.5.4"}}