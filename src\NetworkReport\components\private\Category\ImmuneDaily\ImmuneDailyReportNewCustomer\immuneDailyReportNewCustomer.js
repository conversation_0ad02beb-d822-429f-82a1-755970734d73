import React, { useState, useEffect, createRef } from "react";
import "./immuneDailyReportNewCustomer.css";
import { http1 } from "../../../../../utils/network";

import {
  Button,
  List,
  Form,
  Input,
  Dialog,
  DatePicker,
  Space,
  Radio,
  Picker,
  Popup,
  PickerView,
  SearchBar,
  NavBar,
} from "antd-mobile";
import DoubleArrowIcon from "@mui/icons-material/DoubleArrow";
import CategoryLayoutUpload from "../../../../public/CategoryLayoutUpload/categoryLayoutUpload";
import PostAddIcon from "@mui/icons-material/PostAdd";

const NeuroendocrineDayReportNewCustomer = () => {
  const [form] = Form.useForm();
  const formRef = React.createRef();
  const [name, setName] = useState(""); //客户姓名数据
  const [history, setHistory] = useState(false);
  const [money, setMoney] = useState("");
  const prescriptionHospital = Form.useWatch("prescriptionHospital", form); //监测动态变化

  useEffect(() => {
    codeHttp1(1, "");
    typeProduction();
    console.log("222222", prescriptionHospital);
  }, [prescriptionHospital]);
  const codeHttp1 = (page, value) => {
    http1
      .post(
        "/hospital/list/query",
        value
          ? {
              page: page,
              hospitalName: value,
              query: {
                hospitalName: "like",
              },
              size: 100,
            }
          : { page: page, size: 100 }
      )
      .then((res) => {
        //  console.log("==============", res)
        const codeName1 = res.map((item) => {
          return { label: item.hospitalName, value: item.hospitalId };
        });
        setHospitalColumns([codeName1]);
      });
  };
  const codeHttp3 = () => {
    http1
      .post("/immuneDaily/immuneDaily/doctor/query", {
        hospitalId: drugHospital.value[0],
      })
      .then((res) => {
        console.log("========", res);
        const code = res.map((item) => {
          return { label: item.name, value: item.doctorCode };
        });
        setBasicOffice([code]);
      });
  };

  const typeProduction = () => {
    http1.post("/meta/select", { tag: "JFNSpecifications" }).then((res) => {
      console.log("===---=====", res);
      const code = res.map((item) => {
        return { label: item.description, value: item.code };
      });
      setAllTips([code]);
    });
  };

  const [basicColumns, setBasicColumns] = useState([]);

  const [basicOffice, setBasicOffice] = useState([]);

  const [basicType, setBasicType] = useState([
    [
      { label: "是", value: true },
      { label: "否", value: false },
    ],
  ]);

  const [allTips, setAllTips] = useState([]);

  const [historyList, setHistoryList] = useState([]);

  const centerHospital = () => {
    setHospital(false);
  };
  const centerOffice = () => {
    setOffice(false);
  };
  const centerType = () => {
    setType(false);
  };
  const productcenterType = () => {
    setProductType(false);
  };
  const onSubmit = async () => {
    //提交
    // try {
    const formvalue = await form.validateFields();

    // console.log('formvalue',formvalue)
    const params = {
      hospitalCode: drugHospital.value[0],
      doctorCode: formvalue.office[0],
      isNew: formvalue.type[0],
      specifications: formvalue.tips[0],
      volumn: formvalue.name,
      price: formvalue.money,
    };
    http1.post("/jfnReport/info/save", params).then((res) => {
      console.log("提交表单", res);
      if (res.id) {
        Dialog.alert({
          content: "提交成功",
        });
        formRef.current.resetFields();
        setDrugHospital({
          value: [null],
          extend: null,
        });
      }
    });

    //    } catch {
    //         console.log('error')
    //    }
  };
  const back = () => {
    setHistory(false);
  };
  const intoHistory = () => {
    // setHistory(true)
  };
  //编码1搜索相关的信息
  //医院搜索的相关自定义信息
  const [drugHospitalSelect, setDrugHospitalSelect] = useState({
    value: [null],
    extend: null,
  });
  const [drugHospitalShow, setDrugHospitalShow] = useState(false);

  const [drugHospital, setDrugHospital] = useState({
    value: [null],
    extend: null,
  });
  const drugHospitalConfirm = () => {
    // const formValues = await form.validateFields();
    setDrugHospital(drugHospitalSelect);
    form.setFieldValue("prescriptionHospital", drugHospital.value);
    setDrugHospitalShow(false);
    codeHttp3();
  };

  const [searchvalue, setSearchvalue] = useState("");
  const searchButtoninto = () => {
    codeHttp1(1, searchvalue);
  };
  const [hospitalColumns, setHospitalColumns] = useState([]);

  const formFiled = () => {
    console.log("点下看看");
    if (!drugHospital.value[0]) {
      setBasicOffice([]);
      Dialog.alert({
        content: "请先选择编码1",
      });
    }
  };

  if (history == false) {
    return (
      <div className={"neuroendocrineDayReportNewCustomer"}>
        <div className={"top"}>
          生殖
          <DoubleArrowIcon fontSize={"small"} />
          周战报
          <DoubleArrowIcon fontSize={"small"} />
          数据上报
        </div>
        <div className={"content"}>
          {/* header */}
          <>
            <div className={"header"}>
              <div className={"headerContext"}>
                <div style={{ fontWeight: "bold" }}>日站报创面直营金扶宁</div>
              </div>
              <div className={"headerContext"} onClick={intoHistory}>
                <PostAddIcon className={"fontMain"} fontSize={"small"} />
                <span className={"fontSecond"}>记录</span>
              </div>
            </div>
            <div className={"content"}>
              <Form form={form} ref={formRef}>
                <Form.Item
                  //name='prescriptionHospital'
                  label="编码1"
                  trigger="onConfirm"
                  rules={[{ required: true }]}
                  // disabled
                  onClick={() => {
                    setDrugHospitalSelect(drugHospital);
                    setDrugHospitalShow(true);
                  }}
                  className={"admformitem"}
                >
                  {drugHospital.extend
                    ? drugHospital.extend.items.map(
                        (item) => item?.label ?? "未选择"
                      )
                    : "未选择"}
                  <Popup
                    visible={drugHospitalShow}
                    onMaskClick={() => {
                      setDrugHospitalSelect(drugHospital);
                      setDrugHospitalShow(false);
                    }}
                    bodyStyle={{
                      borderTopLeftRadius: "8px",
                      borderTopRightRadius: "8px",
                      minHeight: "40vh",
                    }}
                  >
                    <div
                      className={"adm-picker-header"}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        height: "42px",
                      }}
                    >
                      <span
                        className={"adm-picker-header-button"}
                        style={{
                          fontSize: "15px",
                          color: "var(--gensci-main)",
                        }}
                        onClick={() => drugHospitalCancel()}
                      >
                        取消
                      </span>
                      <span
                        className={"adm-picker-header-button"}
                        style={{
                          fontSize: "15px",
                          color: "var(--gensci-main)",
                        }}
                        onClick={() => drugHospitalConfirm()}
                      >
                        确认
                      </span>
                    </div>
                    <div className={"searchBarlist"}>
                      <SearchBar
                        placeholder="请输入内容"
                        style={{ margin: "5px 10px", width: "75%" }}
                        onChange={(value) => setSearchvalue(value)}
                      />
                      <Button
                        onClick={searchButtoninto}
                        style={{ marginRight: "5px" }}
                        color="primary"
                        fill="solid"
                        size="small"
                      >
                        搜索
                      </Button>
                    </div>

                    <PickerView
                      value={drugHospitalSelect.value}
                      columns={hospitalColumns}
                      onChange={(value, extend) => {
                        setDrugHospitalSelect({
                          value: value,
                          extend: extend,
                        });
                      }}
                    />
                  </Popup>
                </Form.Item>
                {/* <Form.Item
                                    name='hospital'
                                    label='编码1'
                                    trigger='onConfirm'
                                    rules={[{ required: true, message: '请选择编码1' }]}
                                    onClick={(e, pickref) => {
                                        pickref.current?.open()
                                    }}
                                >
                                <Picker
                                    columns={basicColumns}
                                 >
                                     
                                     {

                                        (e) => {
                                            return <div>{e && e[0] && e[0].label || <span style={{ color: '#ccc'}}>请选择</span>}</div>
                                        }
                                    }
                                </Picker>

                                </Form.Item> */}

                <Form.Item
                  //disabled={!prescriptionHospital}
                  name="office"
                  label="编码3"
                  trigger="onConfirm"
                  rules={[{ required: true, message: "请选择编码3" }]}
                  onClick={(e, pickref) => {
                    pickref.current?.open();
                    formFiled();
                  }}
                >
                  <Picker columns={basicOffice} onConfirm={(v) => {}}>
                    {(e) => {
                      return (
                        <div>
                          {(e && e[0] && e[0].label) || (
                            <span style={{ color: "#ccc" }}>请选择</span>
                          )}
                        </div>
                      );
                    }}
                  </Picker>
                </Form.Item>

                <Form.Item
                  name="type"
                  label="是否新客户"
                  trigger="onConfirm"
                  rules={[{ required: true, message: "请选择" }]}
                  onClick={(e, pickref) => {
                    pickref.current?.open();
                  }}
                >
                  <Picker columns={basicType} onConfirm={(v) => {}}>
                    {(e) => {
                      return (
                        <div>
                          {(e && e[0] && e[0].label) || (
                            <span style={{ color: "#ccc" }}>请选择</span>
                          )}
                        </div>
                      );
                    }}
                  </Picker>
                </Form.Item>

                <Form.Item
                  name="tips"
                  label="规格"
                  trigger="onConfirm"
                  rules={[{ required: true, message: "请选择规格" }]}
                  onClick={(e, pickref) => {
                    pickref.current?.open();
                  }}
                >
                  <Picker columns={allTips} onConfirm={(v) => {}}>
                    {(e) => {
                      return (
                        <div>
                          {(e && e[0] && e[0].label) || (
                            <span style={{ color: "#ccc" }}>请选择</span>
                          )}
                        </div>
                      );
                    }}
                  </Picker>
                </Form.Item>

                <Form.Item
                  name="name"
                  label="支数"
                  rules={[{ required: true, message: "请填写支数" }]}
                >
                  <Input
                    value={name}
                    onChange={(val, extend) => {
                      setName(val);
                    }}
                    placeholder="请填写"
                  />
                </Form.Item>

                <Form.Item
                  name="money"
                  label="金额"
                  rules={[{ required: true, message: "请填写金额" }]}
                >
                  <Input
                    value={money}
                    onChange={(val, extend) => {
                      setMoney(val);
                    }}
                    placeholder="请填写"
                  />
                </Form.Item>
              </Form>
            </div>
          </>

          {/*footer*/}
          <>
            <div className={"footer"}>
              <Button
                block
                color="primary"
                size="middle"
                style={{ marginTop: "5px", width: "calc(100% - 20px)" }}
                onClick={onSubmit}
              >
                提交
              </Button>
            </div>
          </>
        </div>
      </div>
    );
  } else {
    return (
      <div>
        <div className={"historynote"}>
          <NavBar onBack={back}>填报记录</NavBar>
        </div>
        {historyList.map((item, index) => {
          return (
            <div key={index} className={"historymenu"}>
              <div className={"icons"}>
                <PostAddIcon className={"fontMains"} fontSize={"large"} />
              </div>
              <div className={"rightlist"}>
                <div style={{ padding: "5px" }}>
                  <span style={{ color: "#9D9D9D" }}>医院：</span>
                  <span style={{ color: "#6B6491" }}>{item.hospital}</span>
                </div>
                <div style={{ padding: "5px" }}>
                  <span style={{ color: "#9D9D9D" }}>新客户姓名：</span>
                  <span style={{ color: "#6B6491" }}>{item.name}</span>
                </div>
                <div style={{ padding: "5px" }}>
                  <span style={{ color: "#9D9D9D" }}>新客户科室：</span>
                  <span style={{ color: "#6B6491" }}>{item.office}</span>
                </div>
                <div style={{ padding: "5px" }}>
                  <span style={{ color: "#9D9D9D" }}>新患数（人）：</span>
                  <span style={{ color: "#6B6491" }}>{item.num}</span>
                </div>
                <div style={{ padding: "5px" }}>
                  <span style={{ color: "#9D9D9D" }}>新患类型：</span>
                  <span style={{ color: "#6B6491" }}>{item.type}</span>
                </div>
                <div style={{ padding: "5px" }}>
                  <span style={{ color: "#9D9D9D" }}>上报人：</span>
                  <span style={{ color: "#6B6491" }}>{item.upman}</span>
                </div>
                <div style={{ padding: "5px" }}>
                  <span style={{ color: "#9D9D9D" }}>上报时间：</span>
                  <span style={{ color: "#6B6491" }}>{item.time}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  }
};

export default NeuroendocrineDayReportNewCustomer;
