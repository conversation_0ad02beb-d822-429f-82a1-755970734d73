//JS语法规范解析器
module.exports = {

    //ESLint 默认使用Espree作为其解析器，
    "parser":  'espree',

    //定义文件继承的子规范
    "extends": [
        // "eslint:all",
        // "plugin:react/all"
        "eslint:recommended",
        "plugin:react/recommended",
    ],

    //插件可以提供处理器。处理器可以从另一种文件中提取 JavaScript 代码，然后让 ESLint 检测 JavaScript 代码。或者处理器可以在预处理中转换 JavaScript 代码。
    "plugins": ["react"],

    //指定代码的运行环境
    "env":{
        "browser": true,
        "node": true,
        "commonjs": true,
        "es6": true
    },

    //自动发现React的版本，从而进行规范react代码
    "settings": {
        "react": {
            "createClass": "createReactClass", // Regex for Component Factory to use,
                                               // default to "createReactClass"
            "pragma": "React",  // Pragma to use, default to "React"
            "fragment": "Fragment",  // Fragment to use (may be a property of <pragma>), default to "Fragment"
            "version": "detect", // React version. "detect" automatically picks the version you have installed.
                                 // You can also use `16.0`, `16.3`, etc, if you want to override the detected value.
                                 // default to latest and warns if missing
                                 // It will default to "detect" in the future
        },
        "propWrapperFunctions": [
            // The names of any function used to wrap propTypes, e.g. `forbidExtraProps`.
            // If this isn't set, any propTypes wrapped in a function will be skipped.
            "forbidExtraProps",
            {"property": "freeze", "object": "Object"},
            {"property": "myFavoriteWrapper"}
        ],
        "linkComponents": [
            // Components used as alternatives to <a> for linking, eg. <Link to={ url } />
            "Hyperlink",
            {"name": "Link", "linkAttribute": "to"}
        ]
    },

    //指定ESLint可以解析JSX语法
    "parserOptions": {
        //默认设置为 3，5（默认）， 你可以使用 6、7、8、9 或 10 来指定你想要使用的 ECMAScript 版本。
        //你也可以用使用年份命名的版本号指定为 2015（同 6），2016（同 7），或 2017（同 8）或 2018（同 9）或 2019 (same as 10)
        "ecmaVersion": 2019,

        // 设置为 "script" (默认) 或 "module"（如果你的代码是 ECMAScript 模块)。
        "sourceType": 'module',

        //这是个对象，表示你想使用的额外的语言特性:
        //globalReturn - 允许在全局作用域下使用 return 语句
        //impliedStrict - 启用全局 strict mode (如果 ecmaVersion 是 5 或更高)
        //jsx - 启用 JSX
        "ecmaFeatures":{
            "jsx":true
        }
    },

    //ESLint 附带有大量的规则。你可以使用注释或配置文件修改你项目中要使用的规则。要改变一个规则设置，你必须将规则 ID 设置为下列值之一：
    // "off" 或 0 - 关闭规则
    // "warn" 或 1 - 开启规则，使用警告级别的错误：warn (不会导致程序退出)
    // "error" 或 2 - 开启规则，使用错误级别的错误：error (当被触发的时候，程序会退出)
    "rules": {
        "react/jsx-uses-react": "error",
        "react/jsx-uses-vars": "error",
    },

    //要报告未使用的eslint-disable评论，请使用reportUnusedDisableDirectives设置。
    "reportUnusedDisableDirectives": true
};

