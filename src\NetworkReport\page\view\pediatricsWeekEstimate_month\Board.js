import React, {useEffect, useState} from 'react';
import styled from 'styled-components';
import Tabs from "./Tabs";
import {http1} from "../../../utils/network";
import {useNavigate, useSearchParams} from "react-router-dom";
import {To<PERSON>, Button} from 'antd-mobile';
import ApprovalBar from "./ApprovalBar";
import dayjs from "dayjs";

const Wrapper = styled.div`
  padding-bottom: 100px;
  .light {
    color: #2551F2;
  }
  .merge-div {
    display: inline-block;
    border-bottom: 1px solid rgb(148, 163, 184);
  }

  .plan-table {
    overflow-x: auto;
  }
  .plan-table table {
    width: 150vw;
    overflow-x: auto;
    table-layout: fixed;
  }
  table td {
    padding: 7px 16px;
    text-align: center;
  }

  table thead {
    background-color: #F3F4F5;
  }

  table thead tr th {
    padding: 7px 16px;
    font-size: 14px;
  }

  .zhou {
    color: #2551F2;
    /* 16/CN-Medium */
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    margin: 32px 0 16px 0;
  }

  .dept {
    color: #2551F2;
    /* 16/CN-Medium */
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    margin-bottom: 16px;
  }
`;

const Board = props => {
  const navigate = useNavigate();

  const [searchParams] = useSearchParams()

  const readLevelQuery = +searchParams.get('readLevel') || ''
  const empNoQuery = searchParams.get('empNo') || ''
  const status = searchParams.get('status') || ''

  const {roleInfo, readLevel} = props;

  const {deptcode, estDate = [], empNo, level, currentLevel} = roleInfo || {};

  const [tabKey, setTabKey] = useState('');
  const [planData, setPlanData] = useState({}); // 地区经理 周计划
  const [hospitalBoardData, setHospitalBoardData] = useState([]); // 地区经理 机构看板
  const [hospitalBoardDataFull, setHospitalBoardDataFull] = useState([]); // 地区经理 机构看板
  const [deptBoardData, setDeptBoardData] = useState([]); // 地区经理 部门看板
  const [deptBoardDataFull, setDeptBoardDataFull] = useState([]); // 地区经理 部门看板
  const [hospitalPlanDataFull, setHospitalPlanDataFull] = useState([]); // 机构视图 计划看板
  const [deptBoardTr, setDeptBoardTr] = useState([]); // 大区、区总 =》 动态部门看板 表头
  const [userBoardData, setUserBoardData] = useState([]); // 大区、区总 =》 动态部门看板 表头
  const [approvalFlow, setApprovalFlow] = useState([]); // 地区审批记录

  useEffect(() => {
    if (roleInfo && roleInfo.deptcode) {
      if (tabKey === '1') {
        getHospitalData();
        getHospitalPlanData()
        if(level === 6) {
          getApprovalFlow();
        }
      } else if (tabKey === '2') {
        getuserBoardData();
      } else if (tabKey === '3') {
        getDeptData();
      }
    }
  }, [roleInfo, tabKey])

  useEffect(() => {
    props.reloadRole();
  }, [])


  // 获取几口 计划看板 td数据
  const currentHospitalPlanTdRender = (monthIndex, tag, type) => {
    try {
      return hospitalPlanDataFull[monthIndex][tag][type]
    } catch (e) {
      return 0;
    }
  }
  // 地区经理获取审批记录
  // /pediatrics/approval/query/flow
  const getApprovalFlow = async () => {
    try {
      const res = await http1.post(`/pediatrics/approval/query/log?deptCode=${deptcode}`) || []
      setApprovalFlow(res)
    } catch (e) {
      // e
    }
  }

  // 获取机构看板 计划
  const getHospitalPlanData = async () => {
    try {
      const res = await http1.post(`/estimate/query/board/plan/month`,  {
        empNo,
        estDateList: estDate,
        deptcode,
        estDateType: 'month'
      }) || [];
      setHospitalPlanDataFull(res)

    } catch (e) {
      //
    }
  }

  // 获取科室看板数据
  const getDeptData = async () => {
    try {
      Toast.show({
        icon: 'loading',
        duration: 0
      })
      const res = await http1.post('/estimate/query/board/dept/month', {
        empNo,
        estDateList: estDate,
        deptcode,
        estDateType: 'month'
      }) || [];

      // 科室列表
      setDeptBoardDataFull(res || []);
      setDeptBoardData(res[0] && res[0].boardVos || []);

        // 科室列表
        // setDeptBoardData(rowsData);
        // 地区、大区 设置 科室看板动态表头
        setDeptBoardTr(res)
        // 周计划
        // setPlanData(data.planVo || {})


      Toast.clear();
    } catch (e) {
      Toast.clear();
    }

  }

  // 获取机构看板数据
  const getHospitalData = async () => {
    try {
      Toast.show({
        icon: 'loading',
        duration: 0
      })
      const res = await http1.post('/estimate/query/board/hospital/month', {
        empNo,
        estDateList: estDate,
        deptcode,
        estDateType: 'month'
      }) || [];

      const stringhosTypeVoMap = res[0] && res[0].stringhosTypeVoMap || {};
      const planVo = res.planVo || {};

      const arr = [];
      for (const key in stringhosTypeVoMap) {
        arr.push({
          ...stringhosTypeVoMap[key],
          name: key
        })
      }
      setPlanData(planVo);
      setHospitalBoardData(arr)
      setHospitalBoardDataFull(res)
      Toast.clear()
    } catch (e) {
      Toast.clear();
    }
  }

  // 获取人员视图 看板数据
  const getuserBoardData = async () => {
    try {
      Toast.show({
        icon: 'loading',
        duration: 0
      })
      const res = await http1.post('/estimate/query/board/user/month', {
        empNo,
        estDateList: roleInfo.estDate,
        deptcode,
        estDateType: 'month'
      })
      setUserBoardData(res || []);
      Toast.clear()
    } catch (e) {
      Toast.clear()
    }
  }

  const tabOnChange = tabKey => {
    setTabKey(tabKey)
  }

  const deptBoardTrTdRender = (type) => {
    return (
      deptBoardTr.map((item, colIndex) => {
        const planVo = item.planVo || {};
        return (
          <td key={colIndex + item.deptcode + type} className="border border-slate-300">
            {planVo[type]}
          </td>
        )
      })
    )
  }

  // 科室看板 计划 td 取值
  const deptBoardTrTdRender4 = (type) => {
    return (
      deptBoardDataFull.map((item, colIndex) => {
        const planVo = item.planVo || {};
        return (
          <td key={colIndex + item.deptcode + type} className="border border-slate-300">
            {planVo[type]}
          </td>
        )
      })
    )
  }

  // 合计
  const total = (data) => {
    let newPat = 0;
    let longPat = 0;
    let longRate = '0%';

    data.forEach(item => {
      newPat = +item.newPat + newPat;
      longPat = +item.longPat + longPat;
    })
    if(newPat) {
      longRate = ((longPat / newPat) * 100).toFixed(2) + '%';
    }

    return {
      newPat,
      longPat,
      longRate
    }
  }

  const monthMap = estDate.length === 4 ? {
    0: 'current',
    1: 'first',
    2: 'second',
    3: 'third'
  } : {
    0: 'first',
    1: 'second',
    2: 'third'
  }

  // 人员看板 td数据
  const currentValue = (userIndex, monthIndex, type) => {
    try {
      const value = userBoardData[userIndex][monthIndex === -1 ? 'total' : monthMap[monthIndex]][type]
      return value
    } catch (e) {
      return 0
    }
  }

  // 大区-地区 跳转到 机构详情
  const handleToHospital = (item) => {
    if(!(level === 6 || readLevel === 5)) {
      return false
    }
    if(item.name === '合计') {
      return false
    }else {
      const deptcode = searchParams.get('deptcode');
      navigate(`/pediatricsMonthEstimate/report?hideBack=1&name=${item.name}&deptcode=${deptcode || roleInfo.deptcode}&estDate=${estDate}&empNo=${empNoQuery}&readLevel=${readLevelQuery}`)
    }
  }

  // 机构看板 td数据
  const currentHospitalTdValue = (hospitalItem, estDate, type) => {
    try {
      const currentEstDateRow = hospitalBoardDataFull.find(d => d.estDate === estDate) && hospitalBoardDataFull.find(d => d.estDate === estDate).stringhosTypeVoMap || {};
      const stringhosTypeVo = currentEstDateRow[hospitalItem.name] || {};

      return stringhosTypeVo[type]
    } catch (e) {
      return 0
    }
  }

  // 人员看板 计划 td数据
  const currentUserPlanValue = (userIndex, monthIndex, type) => {
    try {
      const value = userBoardData.filter(d => d.deptname !== '合计')[userIndex][monthMap[monthIndex]][type]
      return value
    } catch (e) {
      return 0
    }
  }

  // 人员看板-计划
  const deptBoardTrTdRender2 = (type) => {
    return (
      estDate.map((month, monthIndex) => {
        return (
          userBoardData.filter(d => d.deptname !== '合计').map((item, colIndex) => {
            return (
              <td key={colIndex + item.deptcode + type + month} className="border border-slate-300">
                {currentUserPlanValue(colIndex, monthIndex, type)}
              </td>
            )
          })
        )
      })
    )
  }

  // 机构看板 计划 td数据
  const currentHosPlanValue = (name, monthIndex, type) => {
    try {
      const value = hospitalPlanDataFull[monthIndex]['stringhosTypeVoMap'][name][type]
      return value
    } catch (e) {
      return 0
    }
  }

  // 机构看板-计划
  const deptBoardTrTdRender3 = (type) => {
    return (
      estDate.map((month, monthIndex) => {
        return (
          hospitalBoardData.filter(d => d.name !== '合计').map((item, colIndex) => {
            return (
              <td key={colIndex + item.name + type + month} className="border border-slate-300">
                {currentHosPlanValue(item.name, monthIndex, type)}
              </td>
            )
          })
        )
      })
    )
  }

  // 科室看板 td取值
  const deptBoardTdRender = (deptIndex, monthIndex, type) => {
    try {
      return deptBoardDataFull[monthIndex]['boardVos'][deptIndex][type]
    } catch (e) {
      return 0
    }
  }

  // 获取多月 plan 合计
  const getPlanTotal = (type) => {
    let total = 0;
    deptBoardDataFull.forEach(monthItem => {
      const planVo = monthItem.planVo || {};
      total = total + (+planVo[type])
    })

    return total
  }

  const planTable = () => {
    if ((level === 5 || level === 4 || level === 3) && tabKey === '3') {
      return (
        <div className="table-2">
          <div className="zhou">
            业务计划
          </div>
          <table className="border-collapse border border-slate-400 w-full">
            <thead>
            <tr>
              <th className="border border-slate-300 w-2/12" colSpan={2}>月度计划</th>
              <th className="border border-slate-300 w-5/12">分类</th>
              {
                deptBoardTr.map((item) => {
                  return (
                    <th key={item.estDate} className="border border-slate-300">{item.estDate}</th>
                  )
                })
              }
              <th className="border border-slate-300 light">合计</th>
            </tr>
            </thead>
            <tbody>
              <tr>
                <td className="border border-slate-300" rowSpan={7}>活动计划</td>
                <td className="border border-slate-300" rowSpan={3}>宣传类</td>
                <td className="border border-slate-300">增流活动</td>
                {deptBoardTrTdRender('flowActivityNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('flowActivityNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">帮扶活动</td>
                {deptBoardTrTdRender('helpActivityNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('helpActivityNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">品牌活动</td>
                {deptBoardTrTdRender('brandActivityNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('brandActivityNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300" rowSpan={4}>学术类</td>
                <td className="border border-slate-300">科室会</td>
                {deptBoardTrTdRender('deptMeetingNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('deptMeetingNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">品牌会议</td>
                {deptBoardTrTdRender('regionMeetingNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('regionMeetingNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">第三方会议</td>
                {deptBoardTrTdRender('otherMeetingNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('otherMeetingNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">KA活动</td>
                {deptBoardTrTdRender('kaActivityNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('kaActivityNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300" rowSpan={2} colSpan={2}>用户引流</td>
                <td className="border border-slate-300">新增宝宝数</td>
                {deptBoardTrTdRender('newBabyNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('newBabyNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">有效覆盖数(YZ)</td>
                {deptBoardTrTdRender('reserveNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('reserveNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300" rowSpan={3} colSpan={2}>HCP改善</td>
                <td className="border border-slate-300">观念谨慎型</td>
                {deptBoardTrTdRender('improveConceptNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('improveConceptNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">合作改善型</td>
                {deptBoardTrTdRender('improveCooperateNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('improveCooperateNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">长效和KH改善型</td>
                {deptBoardTrTdRender('improveLongNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('improveLongNum')}
                </td>
              </tr>

              <tr>
                <td className="border border-slate-300" rowSpan={2} colSpan={2}>行为</td>
                <td className="border border-slate-300">大区经理拜访</td>
                {deptBoardTrTdRender('distVisitNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('distVisitNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">大区经理协访</td>
                {deptBoardTrTdRender('distAidVisitNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('distAidVisitNum')}
                </td>
              </tr>

            </tbody>
          </table>
        </div>
      )
    } else if (tabKey === '3') {
      return (
        <div className="table-2">
          <div className="zhou">
            业务计划
          </div>
          <table className="border-collapse border border-slate-400 w-full">
            <thead>
              <tr>
                <th className="border border-slate-300" colSpan={2}>月度计划</th>
                <th className="border border-slate-300">分类</th>
                {
                  estDate.map(month => {
                    return (
                      <th key={month} className="border border-slate-300">{month}</th>
                    )
                  })
                }
                <th className="border border-slate-300 light">合计</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="border border-slate-300" rowSpan={7}>活动计划</td>
                <td className="border border-slate-300" rowSpan={3}>宣传类</td>
                <td className="border border-slate-300">增流活动</td>
                {deptBoardTrTdRender4('flowActivityNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('flowActivityNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">帮扶活动</td>
                {deptBoardTrTdRender4('helpActivityNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('helpActivityNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">品牌活动</td>
                {deptBoardTrTdRender4('brandActivityNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('brandActivityNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300" rowSpan={4}>学术类</td>
                <td className="border border-slate-300">科室会</td>
                {deptBoardTrTdRender4('deptMeetingNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('deptMeetingNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">品牌会议</td>
                {deptBoardTrTdRender4('regionMeetingNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('regionMeetingNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">第三方会议</td>
                {deptBoardTrTdRender4('otherMeetingNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('otherMeetingNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">KA活动</td>
                {deptBoardTrTdRender4('kaActivityNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('kaActivityNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300" rowSpan={2} colSpan={2}>用户引流</td>
                <td className="border border-slate-300">新增宝宝数</td>
                {deptBoardTrTdRender4('newBabyNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('newBabyNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">有效覆盖数(YZ)</td>
                {deptBoardTrTdRender4('reserveNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('reserveNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300" rowSpan={3} colSpan={2}>HCP改善</td>
                <td className="border border-slate-300">观念谨慎型</td>
                {deptBoardTrTdRender4('improveConceptNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('improveConceptNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">合作改善型</td>
                {deptBoardTrTdRender4('improveCooperateNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('improveCooperateNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">长效和KH改善型</td>
                {deptBoardTrTdRender4('improveLongNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('improveLongNum')}
                </td>
              </tr>

              <tr>
                <td className="border border-slate-300" rowSpan={2} colSpan={2}>行为</td>
                <td className="border border-slate-300">大区经理拜访</td>
                {deptBoardTrTdRender4('distVisitNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('distVisitNum')}
                </td>
              </tr>
              <tr>
                <td className="border border-slate-300">大区经理协访</td>
                {deptBoardTrTdRender4('distAidVisitNum')}
                <td className="border border-slate-300 light">
                  {getPlanTotal('distAidVisitNum')}
                </td>
              </tr>

              {/*<tr>*/}
              {/*  <td className="border border-slate-300" colSpan={2}>其他核心管理举措和行动计划</td>*/}
              {/*  <td className="border border-slate-300">*/}
              {/*    {planData.actionPlan}*/}
              {/*  </td>*/}
              {/*</tr>*/}
            </tbody>
          </table>
        </div>
      )
    } else if (tabKey === '2' && (level === 5 || level === 4 || level === 3)) {
      return (
        <div className="table-2">
          <div className="zhou">
            业务计划
          </div>
          <table className="border-collapse border border-slate-400 w-full">
            <thead>
            <tr>
              <th className="border border-slate-300" rowSpan={2} colSpan={2}>月度计划</th>
              <th className="border border-slate-300" rowSpan={2}>分类</th>
              {
                estDate.map(month => {
                  return (
                    <th key={month} className="border border-slate-300" colSpan={userBoardData.filter(d => d.deptname !== '合计').length}>{month}</th>
                  )
                })
              }

            </tr>
            <tr>
              {
                estDate.map(month => {
                  return (
                    userBoardData.filter(d => d.deptname !== '合计').map((item) => {
                      return (
                        <th key={item.deptcode + month} className="border border-slate-300">{item.deptname}</th>
                      )
                    })
                  )
                })
              }
            </tr>
            </thead>
            <tbody>
              <tr>
                <td className="border border-slate-300" rowSpan={7}>活动计划</td>
                <td className="border border-slate-300" rowSpan={3}>宣传类</td>
                <td className="border border-slate-300">增流活动</td>
                {deptBoardTrTdRender2('flowActivityNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">帮扶活动</td>
                {deptBoardTrTdRender2('helpActivityNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">品牌活动</td>
                {deptBoardTrTdRender2('brandActivityNum')}
              </tr>
              <tr>
                <td className="border border-slate-300" rowSpan={4}>学术类</td>
                <td className="border border-slate-300">科室会</td>
                {deptBoardTrTdRender2('deptMeetingNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">品牌会议</td>
                {deptBoardTrTdRender2('regionMeetingNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">第三方会议</td>
                {deptBoardTrTdRender2('otherMeetingNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">KA活动</td>
                {deptBoardTrTdRender2('kaActivityNum')}
              </tr>
              <tr>
                <td className="border border-slate-300" rowSpan={2} colSpan={2}>用户引流</td>
                <td className="border border-slate-300">新增宝宝数</td>
                {deptBoardTrTdRender2('newBabyNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">有效覆盖数(YZ)</td>
                {deptBoardTrTdRender2('reserveNum')}
              </tr>
              <tr>
                <td className="border border-slate-300" rowSpan={3} colSpan={2}>HCP改善</td>
                <td className="border border-slate-300">观念谨慎型</td>
                {deptBoardTrTdRender2('improveConceptNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">合作改善型</td>
                {deptBoardTrTdRender2('improveCooperateNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">长效和KH改善型</td>
                {deptBoardTrTdRender2('improveLongNum')}
              </tr>
              <tr>
                <td className="border border-slate-300" rowSpan={2} colSpan={2}>行为</td>
                <td className="border border-slate-300">大区经理拜访</td>
                {deptBoardTrTdRender2('distVisitNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">大区经理协访</td>
                {deptBoardTrTdRender2('distAidVisitNum')}
              </tr>
            {/*<tr>*/}
            {/*  <td className="border border-slate-300" colSpan={2}>其他核心管理举措和行动计划</td>*/}
            {/*  {deptBoardTrTdRender2('actionPlan')}*/}
            {/*</tr>*/}
            </tbody>
          </table>
        </div>
      )
    }else if(tabKey === '1') {
      // 机构看板 月计划
      return (
        <div className="table-2 plan-table">
          <div className="zhou">
            业务计划
          </div>
          <table className="border-collapse border border-slate-400 w-full" style={{ overflowY: 'auto'}}>
            <thead>
            <tr>
              <th className="border border-slate-300" rowSpan={2} colSpan={2}>月度计划</th>
              <th className="border border-slate-300" rowSpan={2}>分类</th>
              {
                estDate.map(month => {
                  return (
                    <th key={month} className="border border-slate-300" colSpan={hospitalBoardData.filter(d => d.name !== '合计').length}>{month}</th>
                  )
                })
              }

            </tr>
            <tr>
              {
                estDate.map(month => {
                  return (
                    hospitalBoardData.filter(d => d.name !== '合计').map((item) => {
                      return (
                        <th style={{ width: '15vw'}} key={item.name + month} className="border border-slate-300">{item.name}</th>
                      )
                    })
                  )
                })
              }
            </tr>
            </thead>
            <tbody>
              <tr>
                <td className="border border-slate-300" rowSpan={7}>活动计划</td>
                <td className="border border-slate-300" rowSpan={3}>宣传类</td>
                <td className="border border-slate-300">增流活动</td>
                {deptBoardTrTdRender3('flowActivityNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">帮扶活动</td>
                {deptBoardTrTdRender3('helpActivityNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">品牌活动</td>
                {deptBoardTrTdRender3('brandActivityNum')}
              </tr>
              <tr>
                <td className="border border-slate-300" rowSpan={4}>学术类</td>
                <td className="border border-slate-300">科室会</td>
                {deptBoardTrTdRender3('deptMeetingNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">品牌会议</td>
                {deptBoardTrTdRender3('regionMeetingNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">第三方会议</td>
                {deptBoardTrTdRender3('otherMeetingNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">KA活动</td>
                {deptBoardTrTdRender3('kaActivityNum')}
              </tr>
              <tr>
                <td className="border border-slate-300" rowSpan={2} colSpan={2}>用户引流</td>
                <td className="border border-slate-300">新增宝宝数</td>
                {deptBoardTrTdRender3('newBabyNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">有效覆盖数(YZ)</td>
                {deptBoardTrTdRender3('reserveNum')}
              </tr>
              <tr>
                <td className="border border-slate-300" rowSpan={3} colSpan={2}>HCP改善</td>
                <td className="border border-slate-300">观念谨慎型</td>
                {deptBoardTrTdRender3('improveConceptNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">合作改善型</td>
                {deptBoardTrTdRender3('improveCooperateNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">长效和KH改善型</td>
                {deptBoardTrTdRender3('improveLongNum')}
              </tr>
              <tr>
                <td className="border border-slate-300" rowSpan={2} colSpan={2}>行为</td>
                <td className="border border-slate-300">大区经理拜访</td>
                {deptBoardTrTdRender3('distVisitNum')}
              </tr>
              <tr>
                <td className="border border-slate-300">大区经理协访</td>
                {deptBoardTrTdRender3('distAidVisitNum')}
              </tr>
              {/*<tr>*/}
              {/*  <td className="border border-slate-300" colSpan={2}>其他核心管理举措和行动计划</td>*/}
              {/*  {deptBoardTrTdRender2('actionPlan')}*/}
              {/*</tr>*/}
            </tbody>
          </table>
        </div>
      )
    } else if (tabKey === '2') {
      return (
        <div className="table-2">
          <div className="zhou">
            业务计划
          </div>
          <table className="border-collapse border border-slate-400 w-full">
            <thead>
            <tr>
              <th className="border border-slate-300 w-2/12"></th>
              <th className="border border-slate-300 w-5/12">分类</th>
              <th className="border border-slate-300 w-5/12">下周({estDate})</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <td className="border border-slate-300" rowSpan={3}>HCP改善</td>
              <td className="border border-slate-300">观念谨慎型</td>
              <td className="border border-slate-300">
                {planData.improveConceptNum}
              </td>
            </tr>
            <tr>
              <td className="border border-slate-300">合作改善型</td>
              <td className="border border-slate-300">
                {planData.improveCooperateNum}
              </td>
            </tr>
            <tr>
              <td className="border border-slate-300">长效和KH改善型</td>
              <td className="border border-slate-300">
                {planData.improveLongNum}
              </td>
            </tr>

            <tr>
              <td className="border border-slate-300" rowSpan={2}>行为</td>
              <td className="border border-slate-300">大区经理拜访</td>
              <td className="border border-slate-300">
                {planData.distVisitNum}
              </td>
            </tr>
            <tr>
              <td className="border border-slate-300">大区经理协访</td>
              <td className="border border-slate-300">
                {planData.distAidVisitNum}
              </td>
            </tr>
            {/*{*/}
            {/*  level === 6 && (*/}
            {/*    <tr>*/}
            {/*      <td className="border border-slate-300" colSpan={2}>其他核心管理举措和行动计划</td>*/}
            {/*      <td className="border border-slate-300">*/}
            {/*        {planData.actionPlan}*/}
            {/*      </td>*/}
            {/*    </tr>*/}
            {/*  )*/}
            {/*}*/}
            </tbody>
          </table>
        </div>
      )
    }
  }

  //  -1 ： 已驳回 0：未提交  1：已提交，待审核 2： 审核通过
  const statusMap = {
    '-2': {
      color: '#333',
      text: '未完成审核'
    },
    '-1': {
      color: '#ff5500',
      text: '已驳回'
    },
    '0': {
      color: '#333',
      text: '未提交'
    },
    '1': {
      color: '#2db7f5',
      text: '已提交'
    },
    '2': {
      color: '#87d068',
      text: '审核通过'
    }
  }

  const getDeptBoardMonthTotal = (monthIndex) => {
    let newPat = 0;
    let longPat = 0;
    let longRate = 0;
    const monthCol = deptBoardDataFull[monthIndex].boardVos || [];

    monthCol.forEach(item => {
      newPat = +item.newPat + newPat;
      longPat = +item.longPat + longPat;
    })
    if(newPat) {
      longRate = ((longPat / newPat) * 100).toFixed(2) + '%';
    }

    return {
      newPat,
      longPat,
      longRate
    }
  }

  // 多月合计的 合计
  const getDeptBoardMonthTotalTotal = () => {
    let newPat = 0;
    let longPat = 0;
    let longRate = 0;
    deptBoardDataFull.forEach(monthItem => {
      monthItem.boardVos.forEach(deptItem => {
        newPat = +deptItem.newPat + newPat;
        longPat = +deptItem.longPat + longPat;
      })
    })
    if(newPat) {
      longRate = ((longPat / newPat) * 100).toFixed(2) + '%';
    }

    return {
      newPat,
      longPat,
      longRate
    }
  }

  // 多月单行的合计
  const getDeptBoardMonthRowTotal = (deptIndex) => {
    let newPat = 0;
    let longPat = 0;
    let longRate = 0;
    deptBoardDataFull.forEach(monthItem => {
      const deptItem = monthItem.boardVos[deptIndex];
      newPat = +deptItem.newPat + newPat;
      longPat = +deptItem.longPat + longPat;
    })
    if(newPat) {
      longRate = ((longPat / newPat) * 100).toFixed(2) + '%';
    }

    return {
      newPat,
      longPat,
      longRate
    }
  }

  return (
    <Wrapper>
      <Tabs roleInfo={roleInfo} onChange={tabOnChange} readLevel={readLevel} getHospitalData={getHospitalData}/>
      {
        tabKey === '1' && (
          <>
            <div className="zhou">业务目标</div>
            <table className="border-collapse border border-slate-400 w-full">
              <thead>
                <tr>
                  <th className="border border-slate-300" rowSpan={2}>机构分类</th>
                  {
                    estDate.map((item, index) => {
                      return (
                        <React.Fragment key={item + index}>
                          <th className="border border-slate-300" colSpan={3}>{item}预估数</th>
                        </React.Fragment>
                      )
                    })
                  }
                  <th className="border border-slate-300 light" colSpan={5}>合计</th>
                </tr>
                <tr>
                  {
                    estDate.map((item, index) => {
                      return (
                        <React.Fragment key={item + index}>
                          <th className="border border-slate-300">预估数</th>
                          <th className="border border-slate-300">长效预估数</th>
                          <th className="border border-slate-300">长效占比</th>
                        </React.Fragment>
                      )
                    })
                  }
                  {/* 合计 */}
                  <th className="border border-slate-300 light">预估数</th>
                  <th className="border border-slate-300 light">长效预估数</th>
                  <th className="border border-slate-300 light">长效占比</th>
                  <th className="border border-slate-300 light">有预估新增的机构数</th>
                  <th className="border border-slate-300 light">有预估新增的机构占比</th>
                </tr>
              </thead>
              <tbody>
              {
                hospitalBoardData.map(item => {
                  return (
                    <tr key={item.name}>
                      <td
                        className="border border-slate-300"
                        style={item.name === '合计' ? { color: '#2551F2' } : ((level === 6 || readLevel === 5) && {color: '#2551F2', cursor: 'pointer'} || {})}
                        onClick={() => handleToHospital(item)}
                      >
                        {item.name}
                      </td>
                      {
                        estDate.map((monthItem, monthIndex) => {
                          return (
                            <React.Fragment key={monthIndex + monthItem}>
                              <td className="border border-slate-300" style={item.name === '合计' ? { color: '#2551F2'} : {}}>{currentHospitalTdValue(item, monthItem, 'newPat')}</td>
                              <td className="border border-slate-300" style={item.name === '合计' ? { color: '#2551F2'} : {}}>{currentHospitalTdValue(item, monthItem, 'longPat')}</td>
                              <td className="border border-slate-300" style={item.name === '合计' ? { color: '#2551F2'} : {}}>{Number(currentHospitalTdValue(item, monthItem, 'longRate') * 100).toFixed(2)}%</td>
                            </React.Fragment>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">{currentHospitalTdValue(item, '合计', 'newPat')}</td>
                      <td className="border border-slate-300 light">{currentHospitalTdValue(item, '合计', 'longPat')}</td>
                      <td className="border border-slate-300 light">{Number(currentHospitalTdValue(item, '合计', 'longRate') * 100).toFixed(2)}%</td>
                      <td className="border border-slate-300 light" style={item.name === '合计' ? { color: '#2551F2'} : {}}>{currentHospitalTdValue(item, '合计', 'newPatHos')}</td>
                      <td className="border border-slate-300 light" style={item.name === '合计' ? { color: '#2551F2'} : {}}>{Number(currentHospitalTdValue(item, '合计', 'newPatHosRate') * 100).toFixed(2)}%</td>
                    </tr>
                  )
                })
              }
              </tbody>
            </table>
            {planTable()}
            {
              (readLevel === 5 && level === 5 && status === '1' && currentLevel !== 4 && currentLevel !== 3) && (
                <ApprovalBar empNo={empNo} deptcode={deptcode} />
              )
            }
          </>
        )
      }

      {
        // 人员视图
        tabKey === '2' && (
          <>
            <div className="zhou">业务目标</div>
            <table className="border-collapse border border-slate-400 w-full">
              <thead>
              <tr>
                <th className="border border-slate-300" rowSpan={2}>人员分类</th>
                <th className="border border-slate-300" rowSpan={2}>审核状态</th>
                {
                  estDate.map((item, index) => {
                    return (
                      <React.Fragment key={item + index}>
                        <th className="border border-slate-300" colSpan={3}>{item}预估数</th>
                      </React.Fragment>
                    )
                  })
                }
                <th className="border border-slate-300" colSpan={5}>合计</th>
              </tr>
              <tr>
                {
                  estDate.map((item, index) => {
                    return (
                      <React.Fragment key={item + index}>
                        <th className="border border-slate-300">预估数</th>
                        <th className="border border-slate-300">长效预估数</th>
                        <th className="border border-slate-300">长效占比</th>
                      </React.Fragment>
                    )
                  })
                }
                {/* 合计 */}
                <th className="border border-slate-300">预估数</th>
                <th className="border border-slate-300">长效预估数</th>
                <th className="border border-slate-300">长效占比</th>
                <th className="border border-slate-300">有预估新增的机构数</th>
                <th className="border border-slate-300">有预估新增的机构占比</th>
              </tr>
              </thead>
              <tbody>
              {
                userBoardData.map((item, userIndex) => {
                  return (
                    <tr key={item.deptcode}>
                      <td className="border border-slate-300">
                        {/*<a href="javascript:;" onClick={() => navigate()}>
													{item.deptname}
												</a>*/}
                        {
                          (item.deptname === '合计') ? item.deptname :
                            <a onClick={() => {
                              // 大区 看地区tba的情况 需要大区经理进去上报 区总不用进行上报
                              if ((!item.empNo && level === 5) || (!item.empNo && readLevel === 5) || (!item.empNo && readLevel === 4 && level === 4 && empNoQuery === '')) {
                                // const res = await http1.post('/account/api/public/code2tokenNoSign/dept', {
                                //   empNo: empNo,
                                //   deptcode: item.deptcode
                                // }) || {};
                                // window.location.href = `/pediatricsWeekEstimate/board?&token=${res.token}`;
                                window.location.href = `/pediatricsMonthEstimate/board?&tbaDeptcode=${item.deptcode}`;
                              } else {
                                window.location.href = `/pediatricsMonthEstimate/board?&status=${item.status}&deptcode=${item.deptcode}&readLevel=${readLevelQuery ? readLevelQuery + 1 : level}&deptname=${item.deptname}&empNo=${item.empNo || ''}&empName=${item.empName}`;
                              }

                            }} style={{color: '#2551F2'}}>
                              {!item.empNo ? 'TBA-' : ''}{item.deptname}
                            </a>
                        }
                      </td>
                      <td className="border border-slate-300">
                        <div style={{ color: statusMap[item.status].color}}>{(item.deptname === '合计' && level === 4 && !readLevel) ? '--' : statusMap[item.status].text}</div>
                      </td>
                      {
                        estDate.map((item, monthIndex) => {
                          return (
                            <React.Fragment key={item + monthIndex}>
                              <td className="border border-slate-300">{currentValue(userIndex, monthIndex, 'newPat')}</td>
                              <td className="border border-slate-300">{currentValue(userIndex, monthIndex, 'longPat')}</td>
                              <td className="border border-slate-300">{Number(currentValue(userIndex, monthIndex, 'longRate') * 100).toFixed(2)}%</td>
                            </React.Fragment>
                          )
                        })
                      }
                      {/* 合计 */}
                      <td className="border border-slate-300">{currentValue(userIndex, -1, 'newPat')}</td>
                      <td className="border border-slate-300">{currentValue(userIndex, -1, 'longPat')}</td>
                      <td className="border border-slate-300">{Number(currentValue(userIndex, -1, 'longRate') * 100).toFixed(2)}%</td>
                      <td className="border border-slate-300">{item.newPatHos}</td>
                      <td className="border border-slate-300">{Number(item.newPatHosRate * 100).toFixed(2)}%</td>
                    </tr>
                  )
                })
              }
              </tbody>
            </table>
            {planTable()}
          </>
        )
      }

      {
        //  科室 看板
        tabKey === '3' && (
          <>
            <div className="dept">业务目标</div>
            <table className="border-collapse border border-slate-400 w-full">
              <thead>
              <tr>
                <th className="border border-slate-300">科室</th>
                <th className="border border-slate-300">分类</th>
                {
                  deptBoardDataFull.map(deptMonthItem => {
                    return (
                      <th key={deptMonthItem.estDate} className="border border-slate-300">{deptMonthItem.estDate}</th>
                    )
                  })
                }
                <th className="border border-slate-300 light">合计</th>
              </tr>
              </thead>
              <tbody>
              {/* 合计 */}
              <tr>
                <td className="border border-slate-300 light" rowSpan={3}>合计</td>
                <td className="border border-slate-300 light">预估数</td>
                {
                  deptBoardDataFull.map((deptMonthItem, monthIndex) => {
                    return (
                      <th key={deptMonthItem.estDate} className="border border-slate-300 light">{getDeptBoardMonthTotal(monthIndex).newPat}</th>
                    )
                  })
                }
                <th className="border border-slate-300 light">{getDeptBoardMonthTotalTotal().newPat}</th>
              </tr>
              <tr>
                {/*<td></td>*/}
                <td className="border border-slate-300 light">长效预估数</td>
                {
                  deptBoardDataFull.map((deptMonthItem, monthIndex) => {
                    return (
                      <th key={deptMonthItem.estDate} className="border border-slate-300 light">{getDeptBoardMonthTotal(monthIndex).longPat}</th>
                    )
                  })
                }
                <th className="border border-slate-300 light">{getDeptBoardMonthTotalTotal().longPat}</th>
              </tr>
              <tr>
                {/*<td></td>*/}
                <td className="border border-slate-300 light">长效占比</td>
                {
                  deptBoardDataFull.map((deptMonthItem, monthIndex) => {
                    return (
                      <th key={deptMonthItem.estDate} className="border border-slate-300 light">{getDeptBoardMonthTotal(monthIndex).longRate}</th>
                    )
                  })
                }
                <th className="border border-slate-300 light">{getDeptBoardMonthTotalTotal().longRate}</th>
              </tr>

              {
                deptBoardData.map((item, deptIndex) => {
                  return (
                    <React.Fragment key={item.code}>
                      <tr>
                        <td className="border border-slate-300" rowSpan={2}>{item.code}</td>
                        <td className="border border-slate-300">预估数</td>
                        {
                          deptBoardDataFull.map((deptMonthItem, monthIndex) => {
                            return (
                              <th key={deptMonthItem.estDate} className="border border-slate-300">{deptBoardTdRender(deptIndex, monthIndex, 'newPat')}</th>
                            )
                          })
                        }
                        <th className="border border-slate-300 light">{getDeptBoardMonthRowTotal(deptIndex).newPat}</th>
                      </tr>
                      <tr>
                        {/*<td></td>*/}
                        <td className="border border-slate-300">长效预估数</td>
                        {
                          deptBoardDataFull.map((deptMonthItem, monthIndex) => {
                            return (
                              <th key={deptMonthItem.estDate} className="border border-slate-300">{deptBoardTdRender(deptIndex, monthIndex, 'longPat')}</th>
                            )
                          })
                        }
                        <th className="border border-slate-300 light">{getDeptBoardMonthRowTotal(deptIndex).longPat}</th>
                      </tr>
                    </React.Fragment>
                  )
                })
              }
              </tbody>
            </table>
            {planTable()}
          </>
        )
      }
      {
        level === 6 && approvalFlow && !!approvalFlow.length && (
          <div>
            <div style={{ fontWeight: 'bold', marginTop: 16, marginBottom: 8 }}>审批意见</div>
            {
              approvalFlow.map((item, index) => {
                return (
                  <div key={index} style={{ marginBottom: 4, paddingLeft: 16 }}>
                    {dayjs(item.time).format('YYYY年MM月DD日 HH:mm:ss')},
                    {item.name}
                    {item.status}
                    {item.rejectContent && <>，驳回意见：{item.rejectContent}</>}
                  </div>
                )
              })
            }
          </div>
        )
      }
    </Wrapper>
  )
}

export default Board;
