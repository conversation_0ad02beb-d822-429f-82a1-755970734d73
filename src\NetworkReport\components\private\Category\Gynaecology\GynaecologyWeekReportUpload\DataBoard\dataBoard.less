.dataBoard {
    height: 100%;
}

.dataBoard .top {
    height: 30px;
    background: var(--gensci-second);
    color: white;
    font-size: 16px;
    display: flex;
    align-items: center;
    // padding: 0 8px;
    box-shadow: 0 2px 2px #d2d2d2;
}

.dataBoard .content {
    height: calc(100% - 25px);
}

.dataBoard .fontMain {
    color: var(--gensci-main);
}

.dataBoard .fontSecond {
    color: var(--gensci-second);
}

.dataBoard .header {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px 0 5px;
    background: #3644ac0f
}

.dataBoard .sumRow {
    display: flex;
    text-align: center;
    div {
        width: 100px;
        padding-right: 20px;
    }
}

.dataBoard .sumRow3 {
    display: flex;
    text-align: center;
    div {
        width: 90px;
        padding-right: 20px;
    }
}

.dataBoard .container {
    height: 100%;
    overflow: auto;
}

.dataBoard .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    padding: 0 0 15px 43px;
}

.dataBoard .countFunc {
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 15px;
    color: black;
}

.dataBoard .headerContext {
    display: flex;
    align-items: center;
    font-size: 15px;
}

.reproductionWeekReportUploadDataFormSearchContainer {
    margin: 5px 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}