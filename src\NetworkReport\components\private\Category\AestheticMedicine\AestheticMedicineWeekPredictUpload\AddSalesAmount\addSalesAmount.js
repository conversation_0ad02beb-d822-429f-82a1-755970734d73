import React, { useState, useEffect, useImperativeHandle } from 'react';
import addSalesAmount from './addSalesAmount.less'

import { Form, Input, Picker, Stepper } from "antd-mobile";

import { http1 } from '../../../../../../utils/network'

const AddSalesAmount = ({ salesInfo, setSalesInfo, salesAmountVisible, parentRef }) => {

    const [form] = Form.useForm()

    const product = Form.useWatch('product', form)
    const volume = Form.useWatch('volume', form)
    const priceUnit = Form.useWatch('priceUnit', form)

    useImperativeHandle((parentRef), () => {
        return {
            childHandleAdd,
            resetData
        }
    })
    const childHandleAdd = () => {
        (async () => {
            await form.validateFields()
            return values
        })()
        let values = form.getFieldsValue()
        return values
    }

    const resetData = () => {
        form.resetFields()

    }

    const [productList, setProductList] = useState([])
    const [specificationList, setSpecificationList] = useState([])

    // 处理枚举数组格式
    const handleList = (arr) => {
        let temp = []
        temp = arr.length > 0 && arr.map(item => {
            return { label: item.description, value: item.code }
        })
        if (temp.length > 0) {
            return [temp]
        } else { 
            return[[{ label: '', value: '' }]]
        }
    }

    useEffect(() => {
        http1.post('/meta/select', { tag: 'Product' }).then((res) => {
            setProductList(handleList(res))
        })
    }, [])

    useEffect(() => {
        http1.post('/meta/specifications', { tagCode: product && product[0] }).then((res) => {
            console.log(specificationList);
            setSpecificationList(handleList(res))
        })

    }, [product])

    const handleNum = (value) => {
        let str = String(volume * value)
        if (str.indexOf('.') !== -1) { 
            console.log(str.indexOf('.'));
            str = str.slice(0, str.indexOf('.') + 3)
        }
        form.setFieldsValue({price: str})
    }

    return (
        <div className={addSalesAmount.addSalesAmount}>
            <Form form={form}>
                <Form.Item
                    name='product'
                    label='产品'
                    trigger='onConfirm'
                    rules={[{ required: true }]}
                    onClick={(e, pickerRef) => {
                        pickerRef.current?.open()
                    }}
                >
                    <Picker
                        columns={productList}
                        onConfirm={(v, extend) => {
                            console.log(v, extend.items);
                            setSalesInfo({ ...salesInfo, productLabel: extend.items[0].label })
                        }}
                    >
                        {items => {
                            if (items.every(item => item === null)) {
                                return ''
                            } else {
                                return items.map(item => item?.label ?? '未选择')
                            }
                        }}
                    </Picker>
                </Form.Item>
                <Form.Item
                    name='specifications'
                    label='规格'
                    disabled={!product}
                    trigger='onConfirm'
                    rules={[{ required: true }]}
                    onClick={(e, pickerRef) => {
                        pickerRef.current?.open()
                    }}
                >
                    <Picker
                        columns={specificationList}
                        onConfirm={(v, extend) => {
                            setSalesInfo({ ...salesInfo, specificationsLabel: extend.items[0].label })
                        }}
                    >
                        {items => {
                            if (items.every(item => item === null)) {
                                return ''
                            } else {
                                return items.map(item => item?.label ?? '未选择')
                            }
                        }}
                    </Picker>
                </Form.Item>
                <Form.Item
                    name='volume'
                    rules={[{type: 'number'}]}
                    label='支数'
                >
                    <Stepper
                        min={0}
                        digits={0}
                        onChange={(value) => { 
                          form.setFieldsValue({price: value * priceUnit})
                        } }
                    />
                </Form.Item>
                <Form.Item
                    name='priceUnit'
                    label='回款单价(元)'
                    getValueFromEvent={(value) => {
                        return value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); // 只能输入两位小数
                   }}
                >
                    <Input type="number" min={0} 
                        onChange={handleNum}
                    />
                </Form.Item>
                <Form.Item
                    name='price'
                    label='纯销金额(元)'
                >
                    <Input type="number" min={0} />
                </Form.Item>
            </Form>
        </div>
    )
}

export default AddSalesAmount
