import React, {useState, useEffect, useMemo, useRef} from 'react';
import dataBoard from './dataBoard.less'

import { useNavigate } from 'react-router-dom'

import { DataGrid } from "@mui/x-data-grid";
import { List, Button } from "antd-mobile";
import PeopleIcon from "@mui/icons-material/People";
import DoubleArrowIcon from "@mui/icons-material/DoubleArrow";
import AnalyticsIcon from "@mui/icons-material/Analytics";

import CategoryLayoutBoard from "../../../../../public/CategoryLayoutBoard/categoryLayoutBoard";

import { http1 } from '../../../../../../utils/network';
import dayjs from 'dayjs';
import { nanoid } from 'nanoid'

const DataBoard = () => {

    const [empname, setEmpname] = useState('') // 上报人
    // 纯销额
    const [salesList, setSalesList] = useState([])
    const [salesSum, setSalesSum] = useState({
        todaySum: 0,
        mtdSum: 0,
        mtdProcessSum: 0,
    })
    // 机构开发数
    const [institutionsList, setInstitutionsList] = useState([])
    // const [institutionsSum, setInstitutionsSum] = useState({
    //     weekSum: 0,
    //     mtdSum: 0,
    //     qtdSum: 0,
    //     qtdProcessSum: 0,
    // })

    useEffect(() => {
        // 填报人信息
        http1.post('/account/api/userInfo', {}).then(res => {
            setEmpname(res.empname)
        })
        const handleNum = (value) => { 
            if (value === 0) {
                return value
            } else { 
                let temp = String(value)
            if (temp.indexOf(".") !== -1) {
                temp = temp.slice(0, temp.indexOf(".") + 3);
            }
            return Number(temp)
                
            }
        }
        http1.post('/cosmetology/report/board/sales').then(res => { 
            let temp = res.map(item => { 
               return {
                 code: item.code,
                 mtd: item.mtd,
                 today: item.today,
                 value: item.value,
                 mtdProcess: handleNum(item.mtdProcess * 100) + '%',
                 id: item.code,
               };
            })
            setSalesList(temp);

            let d = temp.reduce((sum, item) => { 
                return sum + item.today
            }, 0)
            let m = temp.reduce((sum, item) => { 
                return sum + item.mtd
            }, 0)
            let p = temp.reduce((sum, item) => { 
                let temp = item.mtdProcess.slice(0, item.mtdProcess.indexOf('%'))
                return sum + Number(temp)
            }, 0)
            setSalesSum({...salesSum, todaySum: d.toFixed(2), mtdSum: m.toFixed(2), mtdProcessSum: p.toFixed(2) + '%'})
        })

        http1.post('/cosmetology/report/board/newOrgan').then(res => { 
            console.log(res);
            let id = nanoid()
            let temp = { 
                value: '新机构开发数',
                mtd: res.mtd,
                week: res.week,
                qtd: res.qtd,
                qtdProcess: handleNum(res.qtdProcess * 100) + '%',
                id: id,
            }
            setInstitutionsList([temp]);

            // let w = temp.reduce((sum, item) => { 
            //     return sum + item.week
            // }, 0)
            // let m = temp.reduce((sum, item) => { 
            //     return sum + item.mtd
            // }, 0)
            // let q = temp.reduce((sum, item) => { 
            //     return sum + item.qtd
            // }, 0)
            // let p = temp.reduce((sum, item) => { 
            //     return sum + item.qtdProcess
            // }, 0)
            // setInstitutionsSum({...institutionsSum, weekSum: w, mtdSum: m, qtdSum: q, qtdProcessSum: p})
        })
    }, [])
    
    //表单头
    const boardColumns = useMemo(
        () => [
           { field: 'value', headerName: ' '},
            {
                field: 'today', headerName: '今日', 
                width: 90
            },
            { field: 'mtd', headerName: 'MTD', width: 70},
            { field: 'mtdProcess', headerName: 'MTD进度', width: 90},
        ],
        [],
    ); 

    const institutionColumns = useMemo(
        () => [
           { field: 'value', headerName: ''},
            {
                field: 'week', headerName: '当周', 
                width: 90
            },
            { field: 'mtd', headerName: 'MTD', width: 60},
            { field: 'qtd', headerName: 'QTD', width: 50},
            { field: 'qtdProcess', headerName: 'QTD进度', width: 90},
        ],
        [],
    ); 

    const navigate = useNavigate()
    // 数据上报    
    const dataReport = () => { 
        navigate('/category/aestheticMedicine/aestheticMedicineDayReportUpload/dataCreate')
    }
    // 上报查询
    const dataQuery = () => { 
        navigate('/category/aestheticMedicine/aestheticMedicineDayReportUpload/dataQuery')
    }

    return (
        <div className={dataBoard.dataBoard}>
            <div className={dataBoard.top} style={{paddingLeft: '8px'}}>医美<DoubleArrowIcon fontSize={'small'}/>日战报<DoubleArrowIcon fontSize={'small'}/>数据看板</div>
            <div className={dataBoard.content}>
                <CategoryLayoutBoard>
                    <>
                        <div className={dataBoard.header}>
                            <div className={dataBoard.headerContext}>
                                {/* <PostAddIcon className={dataBoard.fontMain} fontSize={'large'}/> */}
                                <div>
                                    <span className={dataBoard.fontMain} style={{fontWeight: 'bold'}}>当前人:</span>
                                    <span className={dataBoard.fontSecond}>{empname}</span>
                                </div>
                            </div>
                            <div className={dataBoard.headerContext}>
                                <span className={dataBoard.fontMain} style={{fontWeight: 'bold'}}>当前日期:</span>
                                <span className={dataBoard.fontSecond}>{ dayjs(new Date()).format('YYYY-MM-DD')}</span>
                            </div>
                        </div>
                    </>
                    <>
                        <List>
                            <List.Item>
                                <div className={dataBoard.countFunc}>
                                    <div style={{display: 'flex', alignItems: 'center'}}>
                                        <AnalyticsIcon className={dataBoard.fontMain} fontSize={'large'}/>
                                        <span style={{fontWeight: 'bold', marginRight: '5px'}}>纯销额</span>
                                    </div>
                                    <div style={{display: 'flex', alignItems: 'center'}}>
                                        <span style={{ color: '#ababab', marginRight: '5px' }}>(单位: 万元)</span>
                                    </div>
                                </div>
                                <DataGrid columns={boardColumns} rows={salesList} hideFooter={true} style={{ height: '200px' }} disableColumnMenu />
                                <List className={dataBoard.sumRow_3}>
                                    <List.Item>合计: <span>{salesSum.todaySum}</span><span>{salesSum.mtdSum}</span><span>{salesSum.mtdProcessSum}</span></List.Item>
                                </List>
                            </List.Item>
                            <List.Item>
                                <div className={dataBoard.countFunc}>
                                    <div style={{display: 'flex', alignItems: 'center'}}>
                                        <PeopleIcon className={dataBoard.fontMain} fontSize={'large'}/>
                                        <span style={{fontWeight: 'bold', marginRight: '5px'}}>机构开发数</span>
                                    </div>
                                    <div style={{display: 'flex', alignItems: 'center'}}>
                                        <span style={{color: '#ababab', marginRight: '5px'}}>(单位: 个数)</span>
                                    </div>
                                </div>
                                <DataGrid columns={institutionColumns} rows={institutionsList} hideFooter={true} style={{ height: '200px' }} disableColumnMenu />
                                {/* <List className={dataBoard.sumRow_4}>
                                    <List.Item>合计: <span>{institutionsSum.weekSum}</span><span>{institutionsSum.mtdSum}</span><span>{institutionsSum.qtdSum}</span><span>{institutionsSum.qtdProcessSum}</span></List.Item>
                                </List> */}
                                <div style={{height: '50px'}}></div>
                            </List.Item>
                       </List>
                    </>
                    < >
                        <div className={dataBoard.footer}>
                            <div  style={{display: 'flex'} }>
                                <Button color='primary' fill='outline' size='middle' style={{ margin: '5px 20px 0 0 ', width: '130px' }} onClick={ dataReport}>
                                     数据上报
                                </Button>
                                <Button color='warning' fill='outline' size='middle' style={{ marginTop: '5px', width: '130px' }} onClick={ dataQuery}>
                                     上报查询
                                </Button>
                            </div>
                        </div>
                    </>
                </CategoryLayoutBoard>
            </div>
        </div>
    )
}

export default DataBoard
