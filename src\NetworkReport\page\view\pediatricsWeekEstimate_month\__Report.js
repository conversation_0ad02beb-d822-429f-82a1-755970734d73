import React, {useEffect, useRef, useState} from 'react';
import styled from 'styled-components';
import {useNavigate, useSearchParams} from 'react-router-dom'
import {Dialog, Divider, Picker, Space, Button} from "antd-mobile";
import {http1} from "../../../utils/network";
import ReportTable from "./ReportTable";
import dayjs from "dayjs";
import {RightOutline} from "antd-mobile-icons";

const Wrapper = styled.div`
	.title {
		text-align: center;
		font-size: 19px;
		font-weight: bold;
		padding-top: 10px;
	}
  .selector {
    padding: 0 16px;
    height: 54px;
    line-height: 54px;
	  display: flex;
	  justify-content: space-between;
	  border-bottom: 1px solid rgb(229, 230, 235);
    .label {
      color: rgb(134, 144, 156);
      font-size: 16px;
      font-weight: 400;
    }
    .date {
      color: rgb(29, 33, 41);
      font-size: 15px;
      font-weight: 400;
    }
  }
	.static {
		padding-top: 16px;
    max-height: calc(100vh - 260px);
    overflow-y: auto;
	}
`;

const __Report = () => {
	const navigate = useNavigate();
	const [searchParams] = useSearchParams();
	const [visible, setVisible] = useState(false);
	const [hVisible, setHVisible] = useState(false);
	const [currentWeek, setCurrentWeek] = useState({});
	const [currentHospital, setCurrentHospital] = useState({});
	const [weekColumns, setWeekColumns] = useState([]);
	const [hospitalColumns, setHospitalColumns] = useState([]);
	const [level, setLevel] = useState(null);
	const [hideVisible, setHideVisible] = useState(null);
	const reportTableRef = useRef(null);

	useEffect(() => {
		setHideVisible(!hideVisible);
	}, [visible, hVisible])

	useEffect(() => {
		getLevel();
	}, [])

	useEffect(() => {
		if(level) {
			getWeeks();
			getHospital();
		}
	}, [level])

	const init = () => {
		getWeeks(true);
		getHospital(true);
	}


	const getLevel = async () => {
		try {
			const res = await http1.post(searchParams.get('deptcode') ? '/pediatricsEstimate/query/level/dept' : '/pediatricsEstimate/query/level');
			setLevel(res);
		} catch (e) { /* empty */ }
	}

	const getWeeks = async (reload) => {
		try {
			const res = await http1.post('/pediatricsEstimate/query/date') || [];
			const _ = res.map(item => ({...item, label: item.estDate + (item.isEstDown ? '(已完成)' : '(未预估)'), value: item.estDate}));
			setWeekColumns(_);
			const _current = reload ? _.find(item => currentWeek.estDate === item.estDate) : _.find(item => item.isCurentWeek) || {};
			setCurrentWeek(_current);
			if(!reload) {
				if(searchParams.get('currentWeek')){
					const _queryCurrent = _.find(item => item.value === searchParams.get('currentWeek')) || {}
					setCurrentWeek(_queryCurrent);
				}
			}
		} catch (e) { /* empty */ }
	}

	const getHospital = async (reload) => {
		try {
			const res = await http1.post(level !== 6 ? '/pediatricsEstimate/query/label' : '/pediatricsEstimate/queryHospital') || [];
			const _ = res.map(item => {
				if(level !== 6) {
					item.hospitalid = item.labelCode;
					item.hospitalName = item.label;
				}
				return ({...item, label: item.hospitalName + `(${item.estedNum}/6)`, value: item.hospitalid})
			});
			setHospitalColumns(_);
			if(reload) {
				const _current = _.find(item => item.hospitalid === currentHospital.hospitalid) || {}
				setCurrentHospital(_current)
			}
			if(!reload) {
				if(searchParams.get('hospitalId')) {
					const _current = _.find(item => item.value === searchParams.get('hospitalId')) || {}
					setCurrentHospital(_current)
				}
			}
		} catch (e) { /* empty */ }
	}

	const formatDate = (date) => {
		if(!date) return;
		try {
			return date.split('~')[0] + '~' + dayjs(date.split('~')[1].replace(/\./g, '/')).format('MM.DD')
		} catch (e) {
			console.log(e);
		}
	}

	return (
		<Wrapper>
			{/*<div style={{ padding: '2px 10px'}} onClick={() => navigate(-1)}>返回</div>*/}
			{/*<Divider />*/}
			<Space direction='vertical' style={{ width: '100%' }}>
				<div
					className="selector"
					onClick={() => {
						// if(currentHospital.label) {
						// 	Dialog.confirm({
						// 		content: `是否保存当前数据?`,
						// 		onConfirm: () => {
						// 			reportTableRef.current?.handleSave(setVisible)
						// 		},
						// 		onCancel: () => {
						// 			setVisible(true)
						// 		}
						// 	})
						// }else {
						// 	setHVisible(true)
						// }
							setVisible(true)
					}}
				>
					<span className={'label'}>选择上报时间</span>
					<span className={'date'}>
						{ formatDate(currentWeek.estDate) }{currentWeek.isEstDown ? '(已完成)' : '(未预估)'}
						<RightOutline style={{ color: 'rgb(201, 205, 212)', marginLeft: 4, position: "relative", top: 1 }} fontSize={16} />
					</span>
				</div>
				<div
					className="selector"
					onClick={() => {
						setHVisible(true)
						// if(currentHospital.label) {
						// 	Dialog.confirm({
						// 		content: `是否保存当前数据?`,
						// 		onConfirm: () => {
						// 			reportTableRef.current?.handleSave(setHVisible)
						// 		},
						// 		onCancel: () => {
						// 			setHVisible(true)
						// 		}
						// 	})
						// }else {
						// 	setHVisible(true)
						// }
					}}
				>
					<span className={'label'}>{level === 6 ? '选择上报机构' : '选择上报市场类型'}</span>
					<span className={'date'}>
						{ currentHospital.label ? (currentHospital.label.length > 12 ? currentHospital.label.slice(0, 11) + '...' : currentHospital.label) : (level === 6 ? '请选择上报机构' : '请选择上报市场类型') }
						<RightOutline style={{ color: 'rgb(201, 205, 212)', marginLeft: 4, position: "relative", top: 1 }} fontSize={16} />
					</span>
				</div>
			</Space>
			<div className="static">
				<ReportTable estDate={currentWeek.value} hospitalId={currentHospital.value} init={init} level={level} refs={reportTableRef} hideVisible={hideVisible}/>
			</div>
			<Picker
				columns={[weekColumns].filter(Boolean)}
				visible={visible}
				onClose={() => {
					setVisible(false)
				}}
				value={[currentWeek.value]}
				onConfirm={v => {
					const _ = weekColumns.find(item => item.value === v[0]) || {}
					setCurrentWeek(_)
				}}
			/>
			<Picker
				columns={[hospitalColumns].filter(Boolean)}
				visible={hVisible}
				onClose={() => {
					setHVisible(false)
				}}
				value={[currentHospital.value]}
				onConfirm={v => {
					const _ = hospitalColumns.find(item => item.value === v[0]) || {}
					setCurrentHospital(_)
				}}
			/>
		</Wrapper>
	)
}
export default __Report;
