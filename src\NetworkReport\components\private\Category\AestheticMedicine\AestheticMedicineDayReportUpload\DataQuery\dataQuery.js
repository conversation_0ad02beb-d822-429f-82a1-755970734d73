import React, {useState, useEffect, useMemo, useRef} from 'react';
import dataQuery from './dataQuery.less'

import { useNavigate, useLocation } from 'react-router-dom'

import { Tabs, Collapse, DatePicker, Form, Tag, Space } from 'antd-mobile'
import dayjs from 'dayjs';
import DoubleArrowIcon from "@mui/icons-material/DoubleArrow";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import HomeIcon from "@mui/icons-material/Home";

import { http1 } from '../../../../../../utils/network';
  
const DataQuery = () => {

  const [form] = Form.useForm()
  const [subForm] = Form.useForm()
  const time1 = Form.useWatch('time1', form)
  const time2 = Form.useWatch('time2', subForm)
  const [junior, setJunior] = useState(false)
  const [showDate,setShowDate] = useState(false)

  const [reportersList, setReportersList] = useState([])
  const [subReportersList, setSubReportersList] = useState([]);
  const navigate = useNavigate();

  useEffect(() => { 
    http1.post("/dimOrgMedicalBeautyDept/info/hasBranch").then((res) => {
      setJunior(res);
    });
  }, [])

  useEffect(() => {
    let selectTime = time1 ? dayjs(time1).format("YYYY-MM-DD") + ' 00:00:00' : null

    http1.post("/CosmetologyReport/daily/report/query", { period: selectTime }).then((res) => {
      setReportersList(res);
      if (res.length === 0) {
        setShowDate(true)
      } else {
        setShowDate(false)
      }
    });
  }, [time1])

  useEffect(() => {
    let selectTime = time2 ? dayjs(time2).format("YYYY-MM-DD") + ' 00:00:00' : null

    http1.post("/CosmetologyReport/daily/report/query", { period: selectTime, branchEmpNo: true }).then((res) => {
        setSubReportersList(res);
        if (res.length === 0) {
          setShowDate(true);
        } else {
          setShowDate(false)
        }
      });
  }, [time2])

  const handleTarget = () => {
    history.back();
  };

  const handleHome = () => { 
    navigate("/category/aestheticMedicine/aestheticMedicineDayReportUpload/dataBoard");
  }

  return (
    <div className={dataQuery.dataQuery}>
      <div className={dataQuery.top}>
        <ChevronLeftIcon
          sx={{ fontSize: 50 }}
          onClick={() => {
            handleTarget();
          }}
        />
        <HomeIcon
          sx={{ fontSize: 30 }}
          style={{ marginRight: "5px" }}
          onClick={() => {
            handleHome();
          }}
        />
        医美
        <DoubleArrowIcon fontSize={"small"} />
        日战报
        <DoubleArrowIcon fontSize={"small"} />
        数据查询
      </div>
      <Tabs className={dataQuery.content}>
        <Tabs.Tab title={junior ? "本人上报" : null} key="one">
          <div className={dataQuery.tabContainer}>
            <div className={dataQuery.tabContainerTop}>
              <Form form={form}>
                <Form.Item
                  name="time1"
                  label="上报时间"
                  trigger="onConfirm"
                  onClick={(e, datePickerRef) => {
                    datePickerRef.current?.open();
                  }}
                >
                  <DatePicker max={new Date()}>
                    {(value) =>
                      value ? dayjs(value).format("YYYY-MM-DD") : null
                    }
                  </DatePicker>
                </Form.Item>
              </Form>
            </div>
            {/* <Space style={{ paddingLeft: "25px" }}>
              <Tag color="success">N人已提报</Tag>
              <Tag color="danger">N人未提报</Tag>
              <Tag color="primary">代理商沟通数: 58</Tag>
            </Space> */}
          </div>
          {reportersList.map((item) => {
            return <SalesCard data={item} key={item.id} />;
          })}
          {showDate ? <div className={dataQuery.showText}>暂无数据</div> : null}
        </Tabs.Tab>
        {junior ? (
          <Tabs.Tab title="下级上报" key="two">
            <Form form={subForm}>
              <Form.Item
                name="time2"
                label="上报时间"
                trigger="onConfirm"
                onClick={(e, datePickerRef) => {
                  datePickerRef.current?.open();
                }}
              >
                <DatePicker max={new Date()}>
                  {(value) =>
                    value ? dayjs(value).format("YYYY-MM-DD") : null
                  }
                </DatePicker>
              </Form.Item>
            </Form>
            {subReportersList.map((item) => {
              return <SubSalesCard data={item} key={item.id} />;
            })}
            {showDate ? (
              <div className={dataQuery.showText}>暂无数据</div>
            ) : null}
          </Tabs.Tab>
        ) : null}
      </Tabs>
    </div>
  );
}

const SalesCard = (props) => { 

  let { data } = props

  const navigate = useNavigate();

  const handleDetail = (id, time) => {
    navigate("/category/aestheticMedicine/aestheticMedicineDayReportUpload/dataUpdate", {
      state: { id, formType: 'update', time },
    });
  };
  const handleNum = (value) => { 
    let temp = String(value)
    console.log(temp);
        if (temp.indexOf(".") !== -1) {
          temp = temp.slice(0, temp.indexOf(".") + 5);
            }
            return temp
        }
  return (
        <div onClick={() => handleDetail(data.id, data.insertTime)} style={{borderBottom: '1px solid #ccc', padding: '10px'}}>
          {data.structureName ? data.structureName + ' - ' : null} {data.structureStatusName ? data.structureStatusName + '(机构状态) - ' : null} 纯销额{data.sales ? data.sales : 0}万元
          <div style={{ margin: "10px 0 0 240px" }}>
            {dayjs(data.insertTime).format("YYYY-MM-DD")}
          </div>
        </div>
  );
}
const SubSalesCard = (props) => {
  let { data } = props;

  const navigate = useNavigate();

  const handleDetail = (id, time) => {
    navigate("/category/aestheticMedicine/aestheticMedicineDayReportUpload/dataUpdate", {
      state: { id, formType: "update", time, reporter: "management" },
    });
  };

  const handleNum = (value) => { 
    let temp = String(value)
    console.log(temp);
        if (temp.indexOf(".") !== -1) {
          temp = temp.slice(0, temp.indexOf(".") + 5);
            }
            return temp
  }
  
  return (
    <Collapse>
      <Collapse.Panel key="1" title={"销售代表姓名：" + data.createdName}>
        <div onClick={() => handleDetail(data.id, data.insertTime)}>
          销售代表姓名: {data.createdName} -  
          {data.structureName ? data.structureName + ' - ' : null} {data.structureStatusName ? data.structureStatusName + '(机构状态) - ' : null} 纯销额{data.sales ? data.sales : 0}万元
          <div style={{ margin: "10px 0 0 240px" }}>
            {dayjs(data.insertTime).format("YYYY-MM-DD")}
          </div>
      </div>
      </Collapse.Panel>
    </Collapse>
  );
}


export default DataQuery
