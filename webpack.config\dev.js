const webpack = require('webpack')
const { resolve } = require('path');

const HtmlWebpackPlugin = require('html-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const os = require('os')
const ifaces = os.networkInterfaces()
console.log(ifaces)
const host = () => {
    let host = '127.0.0.1'
    let done = false
    for (const dev in ifaces) {
      ifaces[dev].forEach(function (details) {
        console.log(details)
        if (details.family === 'IPv4' && details.address.indexOf('10.12') > -1){
          host = details.address
          console.log(details.address)
          done = true
        } else if (details.family === 'IPv4' && !done) {
          host = details.address
          console.log(details.address)
          done = true
        }
      })
    }
    return host
  }

module.exports = {
    entry: {
        //单入口文件
        main:resolve(__dirname, '../src/NetworkReport/index/index.js')
    },

    output: {
        //解析后目录
        path: resolve(__dirname, '../dist'),
        //[name]为解析后原来的名字
        filename: '[name].bundle.js',
        chunkFilename: '[name].bundle.js',
        //生产环境改为完整的URL地址
        //publicPath:resolve(__dirname, '../dist')
        publicPath: '/'
    },

    //仅代表名字
    name: 'NetworkReport',

    //开发模式缓存,提高构建速度
    cache: {type:"memory"},

    //调试模式,开发环境下建议source-map(最慢,含原始原代码), cheap-source-map(较快,含转换过的代码（仅限行）)
    devtool: 'source-map',

    //开发模式
    mode: 'development',

    //控制 bundle 信息显示, 开发环境下建议: /'normal' 标准输出 或者/'minimal' 只在发生错误或新的编译开始时输出
    stats: 'normal',

    //优化,生产环境下再做配置
    optimization:{},

    //项目文件模块解析
    module:{
        rules:[
            //JS/JSX模块
            {
                test:/\.js|jsx$/,
                exclude: /node_modules/,
                use:[
                    //多线程编译LOADER
                    {
                        loader: "thread-loader",

                        // 有同样配置的 loader 会共享一个 worker 池
                        options: {
                            // 产生的 worker 的数量，默认是 (cpu 核心数 - 1)，或者，在 require('os').cpus() 是 undefined 时回退至 1
                            workers: 3,

                            // 一个 worker 进程中并行执行工作的数量,默认为 20
                            workerParallelJobs: 50,

                            // 额外的 node.js 参数
                            workerNodeArgs: ['--max-old-space-size=1024'],

                            // 允许重新生成一个僵死的 work 池,这个过程会降低整体编译速度,并且开发环境应该设置为 false
                            poolRespawn: false,

                            // 闲置时定时删除 worker 进程,默认为 500（ms）,可以设置为无穷大，这样在监视模式(--watch)下可以保持 worker 持续存在
                            poolTimeout: 2000,

                            // 池分配给 worker 的工作数量,默认为 200,降低这个数值会降低总体的效率，但是会提升工作分布更均匀
                            poolParallelJobs: 50,

                            // 池的名称,可以修改名称来创建其余选项都一样的池
                            name: "my-pool"
                        },
                    },
                    //JS转换LOADER
                    'babel-loader',
                ]
            },
            // CSS模块 全局部分，UI框架等
            {
                test: /\.css$/,
                use: [
                    // CSS由单独文件写入HTML内
                    {
                        loader: 'style-loader',
                    },

                    // CSS从组件中分离至单独文件
                    {
                        loader: 'css-loader',
                        options: {
                            importLoaders: 1,
                        }
                    },
                    // CSS语法转换配置模块
                    {
                        loader: 'postcss-loader',
                    },
                ],
                // include: /node_modules/,
            },
            // CSS模块 modules部分 less部分
            {
                test: /\.less$/,
                use: [
                    // CSS由单独文件写入HTML内
                    {
                        loader: 'style-loader',
                    },

                    // CSS从组件中分离至单独文件
                    {
                        loader: 'css-loader',
                        options: {
                            modules: true
                        }
                    },
                    // CSS语法转换配置模块
                    {
                        loader: 'postcss-loader',
                    },
                    {
                        loader: 'less-loader',
                    },
                ],
                exclude: /node_modules/,
            },

            //HTML模块
            {
                test: /\.html$/i,
                loader: 'html-loader',
            },

            //文件模块.
            {
                test: /\.(png|jpg|jpeg|gif|eot|ttf|woff|woff2|svg|svgz)(\?.+)?$/,
                type:'asset',
                parser:{
                    dataUrlCondition: {
                        maxSize:8192
                    },
                }
            },
        ],
    },

    // 本地服务配置,用于热更新
    devServer:{
        //访问打包后的JS
        host: host(),
        port: 8028,
        hot: true,
        open: true,
        historyApiFallback: true,
        compress: true,
        //代理,多用于解决跨域问题
        proxy:{},
        static: {
            directory: resolve(__dirname, '../src/NetworkReport/assets'),
            publicPath: '/assets'
        }
    },

		resolve: {
			// 要解析的文件的扩展名
			extensions: ['.js', '.jsx', '.json'],
			alias: {
				'@comp': resolve(__dirname, '../src/NetworkReport/components'),
				'@page': resolve(__dirname, '../src/NetworkReport/page'),
				'@utils': resolve(__dirname, '../src/NetworkReport/utils')
			}
		},

    //插件,用于额外补充功能
    plugins:[
        //清除的DIST目录下的生成文件
        new CleanWebpackPlugin(),

        //WEBPACK打包生成HTML模板
        new HtmlWebpackPlugin({
            //模版路径
            template:resolve(__dirname, '../src/NetworkReport/index/index.html')
        })
    ]

}

