import React, {useRef, useState} from 'react';
import styled from 'styled-components';
import {Button, Dialog} from "antd-mobile";
import {http1} from "../../../utils/network";
import {useNavigate} from "react-router-dom";
import { Modal, Input } from "antd";
const { TextArea } = Input;


const Wrapper = styled.div`
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translate(-50%, 0);
  .adm-button {
    margin-left: 20px;
  }
`;

const ApprovalBar = props => {
  const navigate = useNavigate();
  const { empNo, deptcode } = props;
  const handler = useRef();
  const handler2 = useRef();

  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [rejectReason, setRejectReason] = useState('');

  const handleBack = () => {
    navigate(-1)
  }

  const handlePass = () => {
    handler.current = Dialog.show({
      content: '确认是否审核通过',
      actions: [
        [
          {
            key: 'cancel',
            text: '取消',
            onClick: () => {
              handler.current?.close()
            }
          },
          {
            key: 'queren',
            text: '确认',
            onClick: async () => {
              await http1.post('/pediatrics/approval/approve', {
                empNoList: [empNo] ,
                respDeptCodeList: [deptcode]
              })
              handler.current?.close();
              navigate(-1)
            },
          }
        ],
      ]})
  }

  const handleReject = () => {
    setVisible(true)
  }

  const handleRejectOk = async () => {
    handler2.current = Dialog.show({
      content: '确认是否驳回',
      actions: [
        [
          {
            key: 'cancel',
            text: '取消',
            onClick: () => {
              handler2.current?.close()
            }
          },
          {
            key: 'queren',
            text: '确认',
            onClick: async () => {
              await http1.post('/pediatrics/approval/reject', {
                empNoList: [
                  empNo
                ],
                rejectContent: rejectReason,
                respDeptCodeList: [deptcode]
              });
              handler2.current?.close();
              navigate(-1)
            },
          }
        ],
      ]})

  }

  const handleCancel = () => {
    setRejectReason('')
    setVisible(false)
  }

  return (
    <Wrapper>
      <Button color="default" fill="solid" onClick={handleBack} loading={false}>返回</Button>
      <Button color="primary" fill="solid" onClick={handlePass} loading={false}>通过</Button>
      <Button color="danger" fill="solid" onClick={handleReject} loading={false}>驳回</Button>

      <Modal
        title="驳回原因"
        open={visible}
        onCancel={handleCancel}
        footer={[
          <Button key="back" onClick={handleCancel} style={{ marginRight: 16}}>
            取消
          </Button>,
          <Button key="submit" color="danger" loading={loading} onClick={handleRejectOk}>
            驳回
          </Button>,
        ]}
      >
        <TextArea rows={4} value={rejectReason} onChange={e => setRejectReason(e.target.value)} />
      </Modal>
    </Wrapper>
  )
}

export default ApprovalBar;
