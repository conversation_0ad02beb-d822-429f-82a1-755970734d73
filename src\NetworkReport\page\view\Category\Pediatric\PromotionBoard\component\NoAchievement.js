import React from "react";
import ItemWrapper from "./ItemWrapper";
import { Divider, Popover } from "antd-mobile";
import { ExclamationCircleOutline } from 'antd-mobile-icons';
import {BoldText, isEmpty} from "../index";

// eslint-disable-next-line react/prop-types
const NoAchievement = ({ data = {} }) => {
	const _data1 = data['拟晋升级别达标'] || {};
	const _data2 = data['拟晋升级别2达标'] || {};
	return (
		<ItemWrapper>
			<div className="standard-header">非业绩指标(实际任职时间：{_data1.curWorkTime}月)</div>
			<div className="content">
				<div className="item">
					<div className="title">拟晋升级别</div>
					<div className="value">{_data1.tagLevel || '-'}</div>
					<div className="sub-value">{_data1.tagPosition && _data1.tagLevel.includes('T') ? `(${_data1.tagPosition})` : ''}</div>
				</div>
				<div className="item">
					<div className="title">是否符合</div>
					<div className="value">
						<BoldText isBold isWarning={!_data1.timeCompliance}>
							{isEmpty(_data1.timeCompliance) ? '-' : _data1.timeCompliance ? '是' : '否'}
						</BoldText>
					</div>
				</div>
				<div className="item">
					<div className="title">晋升要求</div>
					<div className="value">
						{(_data1.tagLevelTime || '-').length > 4 ? (_data1.tagLevelTime || '-').slice(0, 4) + '...' : (_data1.tagLevelTime || '-')}
						{
							_data1.tagLevelTime && _data1.tagLevelTime.length > 4 && (
								<Popover
									content={
										<div style={{ padding: 10, textAlign: 'center' }}>
											<div style={{ fontWeight: 'bold', marginBottom: 10, fontSize: '16px' }}>晋升要求</div>
											<div>{_data1.tagLevelTime}</div>
										</div>
									}
									trigger='click'
									placement='left'
								>
									<ExclamationCircleOutline />
								</Popover>
							)
						}
					</div>
				</div>
			</div>
			{
				_data2.tagLevel && (
					<>
						<Divider style={{
							borderStyle: 'dashed',
							borderColor: '#bbb',
							margin: '8px 0'
						}} />
						<div className="content">
							<div className="item">
								<div className="value">{_data2.tagLevel || '-'}</div>
								<div className="sub-value">{_data2.tagPosition && _data2.tagLevel.includes('T') ? `(${_data2.tagPosition})` : ''}</div>
							</div>
							<div className="item">
								<div className="value">
									<BoldText isBold isWarning={!_data2.timeCompliance}>
										{isEmpty(_data2.timeCompliance) ? '-' : _data2.timeCompliance ? '是' : '否'}
									</BoldText>
								</div>
							</div>
							<div className="item">
								<div className="value">
									{(_data2.tagLevelTime || '-').length > 4 ? (_data2.tagLevelTime || '-').slice(0, 4) + '...' : (_data2.tagLevelTime || '-')}
									{
										_data2.tagLevelTime && _data2.tagLevelTime.length > 4 && (
											<Popover
												content={
													<div style={{ padding: 10, textAlign: 'center' }}>
														<div style={{ fontWeight: 'bold', marginBottom: 10, fontSize: '16px' }}>晋升要求</div>
														<div>{_data2.tagLevelTime}</div>
													</div>
												}
												trigger='click'
												placement='left'
											>
												<ExclamationCircleOutline />
											</Popover>
										)
									}
								</div>
							</div>
						</div>
					</>
				)
			}

		</ItemWrapper>
	)
}

export default NoAchievement;
