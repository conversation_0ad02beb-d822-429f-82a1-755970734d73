import axios from 'axios'
import { getToken, removeToken } from './auth'
import { URL } from './env'
import {Toast} from "antd-mobile";


//创建此项目的http1.1协议实例
const http1 = axios.create({
    baseURL: URL,
    timeout: 1200000
})

//请求拦截器
http1.interceptors.request.use(request => {
    const { url } = request
    let arr = url.split('/')
    if (
        arr[arr.length - 1] !== 'form-login'
    ) {
        // 非登录时添加请求头
        // request.headers.accessToken = JSON.stringify({auth: getToken(), authenticationMethod:"jwt"})
        request.headers['X-Token'] = `Bearer ${getToken()}`
				if(localStorage.getItem('channel')) {
					request.headers['channel'] = `${localStorage.getItem('channel')}`
				}else {
					request.headers['channel'] = ''
				}
        // request.headers['X-Token'] = `Bearer eyJhbGciOiJIUzUxMiJ9.eyJyb2xlIjoic2FsZXMiLCJlbXBObyI6IkdTMjc0NyIsImV4cCI6MTY2MzMwMDE2OX0.2E7LpgEY43DYl6gqrVkqu02ik-tQrn7p2WBhcUR8kHM1O8RNIPINUuGmCw6muV5K7KYXCknghS8gno1aSOsUvw`
    }
    return request
})

//后端自定义协议内的协议状态
const getData = (data) => {
    if (data.code === 0) {
        return data.data
    } else if (data.code !== 0) {
      return data.message
    } else {
        return data
    }
}

// 响应拦截器env
http1.interceptors.response.use(
    (response) => {
			if(response.request.responseType === 'blob') {
				return response.data;
			}
	    const { status } = response;
        if (status >= 100 && status < 200) {
            return response
        } else if (status >= 200 && status < 300) {
            if(response.data.code === 0){
                return getData(response.data)
            }else if(response.data.code !== 0){
							Toast.show(response.data.message);
							return Promise.reject(response.data)
            }
        } else if (status >= 300 && status < 400) {
            return response
        } else if (status >= 400 && status < 500) {
            // console.log(response)
            // removeToken()
					Toast.show(response.data.message);
					return response
        } else if (status >= 500) {
            // removeToken()
					return response
        } else {
            throw new Error('response err')
        }
    },
    (error) => {
			Toast.show(error.response && error.response.data && error.response.data.message)
			return error
		}
)

//http2协议
const http2 = {}

//websocket协议
const websocket = {}


export { http1 }
