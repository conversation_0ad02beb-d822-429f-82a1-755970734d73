import React, {useEffect, useRef, useState} from 'react';
import styled from 'styled-components';
import {<PERSON><PERSON>, <PERSON><PERSON>, SafeArea, Space, Toast} from "antd-mobile";
import Table from "./Table";
import {http1} from "@utils/network";
import dayjs from "dayjs";
import {useSearchParams} from "react-router-dom";

const Wrapper = styled.div`
	.tools {
		padding: 10px;
    text-align: right;
	}
	.title {
		text-align: center;
		font-size: 19px;
		font-weight: bold;
		padding-top: 10px;
	}
	.selector {
		width: 100%;
		padding: 0 16px;
    height: 54px;
		line-height: 54px;
		display: flex;
    justify-content: space-between;
		.label {
			color: rgb(134, 144, 156);
      font-size: 16px;
      font-weight: 400;
		}
		.date {
      color: rgb(29, 33, 41);
      font-size: 15px;
      font-weight: 400;
		}
	}
`;

const Board = () => {
	let [searchParams] = useSearchParams()
	const [visible, setVisible] = useState(false);
	const [userInfo, setUserInfo] = useState({});
	const [currentWeek, setCurrentWeek] = useState({});
	const [weekColumns, setWeekColumns] = useState([]);
	const [data, setData] = useState([]);
	const [TBAData, setTBAData] = useState([]);
	const [level, setLevel] = useState(null);
	const [selectedFile, setSelectedFile] = useState(null);
	const [loading, setLoading] = useState(false);
	const uploader = useRef(null);

	useEffect(() => {
		const fileInput = document.getElementById('fileInput');

		fileInput.addEventListener('change', function(event) {
			const selectedFile = event.target.files[0];

			if (selectedFile) {
				console.log('已选择文件:', selectedFile);
				// 这里可以执行上传文件的逻辑，例如使用XMLHttpRequest或fetch发送文件到服务器
				if(userInfo && userInfo.empno)
				handleUpload(selectedFile, userInfo)
			}
		});

		return () => {
			fileInput.removeEventListener('change', () => {});
		}
	}, [userInfo])

	const init = async () => {
		await getUserInfo();
		await getWeeks();
		await getLevel()
	}

	useEffect(() => {
		init()
	}, [])

	useEffect(() => {
		if(userInfo.empno && level) {
			getBoardData({
				empNo: userInfo.empno
			});
		}
	}, [userInfo, level])

	const getLevel = async () => {
		try {
			const res = await http1.post(searchParams.get('deptcode') ? '/pediatricsEstimate/query/level/dept' : '/pediatricsEstimate/query/level');
			setLevel(res);
		} catch (e) { /* empty */ }
	}

	const getUserInfo = async () => {
		const res = await http1.post(searchParams.get('deptcode') ? '/account/api/deptInfo/estimate' : '/account/api/userInfo/estimate', {}) || {};
		if(searchParams.get('deptcode')) {
			http1.defaults.headers = {
				...http1.defaults.headers,
				deptcode: res.finaleducation
			}
		}else {
			delete http1.defaults.headers.deptcode
		}
		setUserInfo(res);
	}

	const getWeeks = async () => {
		try {
			const res = await http1.post('/pediatricsEstimate/query/v2/date') || [];
			const _ = res.map(item => ({...item, label: formatDate(item.estDate) + (item.isEstDown ? '(已完成)' : '(未预估)'), value: item.estDate}));
			setWeekColumns(_);
			const _current = _.find(item => item.isCurentWeek) || {};
			if(searchParams.get('currentWeek')) {
				const __ = _.find(item => item.estDate === searchParams.get('currentWeek')) || {};
				setCurrentWeek(__);
			}else if(_current) {
				setCurrentWeek(_current);
			}
		} catch (e) { /* empty */ }
	}

	const getBoardData = async (params) => {
		try {
			Toast.show({
				content: '加载中...',
				duration: 0
			});
			const res = await http1.post('pediatricsEstimate/query/v2/board', params) || [];
			setData(res);
			if(level !== 6) {
				const _res = await http1.post(`pediatricsEstimate/query/v2/tbaCode?empNo=${userInfo.empno}`, {...params, empNo: userInfo.empno}) || [];
				if(_res[0]) {
					_res[0].showGap = true;

				}
				// setData([...res, ...(_res.map(d => ({...d, isTBA: true})))]);
				setTBAData(_res)
			}

			Toast.clear();
		} catch (e) {
			Toast.show(e.message)
		}
	}

	const formatDate = (date) => {
		if(!date) return;
		try {
			return date.split('~')[0] + '~' + dayjs(date.split('~')[1].replace(/\./g, '/')).format('MM.DD')
		} catch (e) {
			console.log(e);
		}
	}

	const downloadBinaryFile = (data, fileName, mimeType) => {
		const blob = new Blob([data], { type: mimeType });
		const url = window.URL.createObjectURL(blob);
		const a = document.createElement("a");
		a.href = url;
		a.download = fileName;
		document.body.appendChild(a);
		a.click();
		window.URL.revokeObjectURL(url);
	}

	const handleDownload = async () => {
		try {
			setLoading(true);
			Toast.show({
				content: '下载中...',
				icon: 'loading',
				duration: 0
			})

			const name = {
				6: '代表周新增模版',
				5: '地区周新增模版',
				4: '大区周新增模版',
				3: '区域周新增模版'
			}

			const fileName = `${name[level]}.xlsx`; // 指定下载文件的名称
			const mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"; // 指定MIME类型
			const binaryData = await http1.post('/pediatricsEstimate/excel/download',{}, {responseType: 'blob'})

			await downloadBinaryFile(binaryData, fileName, mimeType);
			setLoading(false);
			Toast.clear();
		} catch (e) {
			if(e && e.message) {
				Toast.show(e.message)
			}
			setLoading(false);
			Toast.clear();
		}
	}

	const handleFileChange = (event) => {
		const file = event.target.files[0];
		setSelectedFile(file);
	};

	const chooseFile = () => {
		uploader.current.click()
	}

	const handleUpload = async (file, userInfo) => {
		if (!file) {
			Toast.show('请选择一个文件');
			return;
		}

		// 创建FormData对象，用于包装文件
		const formData = new FormData();
		formData.append('file', file);

		try {
			setLoading(true);
			Toast.show({
				content: '上传中...',
				icon: 'loading',
				duration: 0
			})
			// 发送文件上传请求到服务器
			await http1.post('/pediatricsEstimate/excel/upload', formData, {
				headers: {
					'Content-Type': 'multipart/form-data'
				}
			});
			setLoading(false);
			Toast.show('上传成功！');
			await getBoardData({
				empNo: userInfo.empno
			});
		} catch (e) {
			Toast.show(e.message);
		} finally {
			setLoading(false);
		}
	};

	return (
		<Wrapper>
			{/*<div*/}
			{/*	className="selector"*/}
			{/*	onClick={() => {*/}
			{/*		setVisible(true)*/}
			{/*	}}*/}
			{/*>*/}
			{/*	<span className={'label'}>上报时间</span>*/}
			{/*	<span className={'date'}>*/}
			{/*		{formatDate(currentWeek.estDate)}{currentWeek.isEstDown ? '(已完成)' : '(未预估)'}*/}
			{/*		<RightOutline style={{ color: 'rgb(201, 205, 212)', marginLeft: 4, position: "relative", top: 1 }} fontSize={16} />*/}
			{/*	</span>*/}
			{/*</div>*/}
			{/*<Divider style={{ margin: '0 0 16px 0'}} />*/}
			<div className="tools">
				<Space>
					<Button color="primary" onClick={handleDownload} loading={loading}>下载模板</Button>
					<Button color="primary" loading={loading} onClick={chooseFile}>上传数据</Button>
				</Space>
			</div>
			<div className="static">
				<Table data={data} currentWeek={currentWeek.estDate} level={level} estDateData={weekColumns} TBAData={TBAData}/>
			</div>
			<Picker
				columns={[weekColumns].filter(Boolean)}
				visible={visible}
				onClose={() => {
					setVisible(false)
				}}
				value={[currentWeek.estDate]}
				onConfirm={v => {
					const _ = weekColumns.find(item => item.value === v[0]) || {}
					setCurrentWeek(_)
				}}
			/>
			<input type="file" onChange={handleFileChange} id={'fileInput'} ref={uploader} accept={'.xlsx'} multiple={false} style={{ opacity: 0, width: 0, height: 0 }} />
			<SafeArea position={'bottom'} />
		</Wrapper>
	)
}
export default Board;
