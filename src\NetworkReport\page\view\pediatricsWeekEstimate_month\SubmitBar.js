import React, {useRef, useState} from 'react';
import styled from 'styled-components';
import {Button, Dialog, Toast} from "antd-mobile";
import {http1} from "../../../utils/network";
import {useNavigate, useSearchParams} from "react-router-dom";
import { Modal, Input } from "antd";
const { TextArea } = Input;


const Wrapper = styled.div`
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translate(-50%, 0);
  .adm-button {
    margin-left: 20px;
  }
`;

const SubmitBar = props => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams()

  const { empNo, deptcode } = props;
  const handler = useRef();
  const handler2 = useRef();

  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [rejectReason, setRejectReason] = useState('');

  const roleInfo = props.roleInfo || {};

  const handleBack = () => {
    navigate(-1)
  }

  const handleSubmit = () => {
    handler.current = Dialog.show({
      content: !!searchParams.get('tbaDeptcode') ? '请确认已完成计划的上报，提交后，将无法更新。' : roleInfo.status === '1' ? '是否确认撤回' : '是否确认提交',
      actions: [
        [
          {
            key: 'cancel',
            text: !!searchParams.get('tbaDeptcode') ? '返回' : '取消',
            onClick: () => {
              handler.current?.close()
              if(searchParams.get('tbaDeptcode')) {
                navigate(-1)
              }
            }
          },
          {
            key: 'queren',
            text: '确认',
            onClick: async () => {
              await http1.post('/pediatrics/approval/represent/submit', {
                deptcode: roleInfo.deptcode,
                empNo: roleInfo.empNo,
                tba: !!searchParams.get('tbaDeptcode')
              })
              handler.current?.close();
              navigate(-1)
            },
          }
        ],
      ]})
  }

  const handleSubmit2 = () => {
    handler.current = Dialog.show({
      content: '是否确认撤回',
      actions: [
        [
          {
            key: 'cancel',
            text: '取消',
            onClick: () => {
              handler.current?.close()
            }
          },
          {
            key: 'queren',
            text: '确认',
            onClick: async () => {
              await http1.post('/pediatrics/approval/represent/submit', {
                deptcode: roleInfo.deptcode,
                empNo: roleInfo.empNo
              })
              handler.current?.close();
              navigate(-1)
            },
          }
        ],
      ]})
  }

  return (
    <Wrapper>
      <Button color="default" fill="solid" onClick={handleBack} loading={false}>返回</Button>
      {
        ['0', '-1'].includes(roleInfo.status) && (
          <Button color="primary" fill="solid" onClick={handleSubmit} loading={false}>提交审核</Button>
        )
      }
      {
        ['1'].includes(roleInfo.status) && (
          <Button color="primary" fill="solid" onClick={handleSubmit2} loading={false}>撤回</Button>
        )
      }
    </Wrapper>
  )
}

export default SubmitBar;
