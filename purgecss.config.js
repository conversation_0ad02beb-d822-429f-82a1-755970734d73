//CSS重复内容清理器
module.exports = {

    //你可以使用文件名或 globs 数组指定 Purgecss 需要分析的内容。文件类型可以是 HTML、Pug、Blade 等。
    //content: ['index_accountExecutive.html'],
    //css: ['style.css'],

    //PurgeCSS 可以根据你的需要进行调整。如果你注意到大量未被使用的 css 没有被删除，你可能需要使用自定义提取器了。
    //提取器可以应用于具有某些扩展名的文件。如果你希望对所有类型的文件使用相同的提取器，请通过 defaultExtractor 参数指定提取器。
    //defaultExtractor: content => content.match(/[\w-/:]+(?<!:)/g) || [],

    //fontFace（默认值：false）
    // 如果 css 代码中有任何未使用的 @font-face 规则，可以通过将 fontFace 参数设置为 true 来删除这些规则。
    fontFace: false,

    //关键帧（默认值：false）
    // 如果使用的时 CSS 动画库，例如 animate.css，你可以通过将 keyframes 参数设置为 true 来删除未使用的 keyframes。
    keyframes: false,

    //变量（默认值：false）
    // 如果您使用的是自定义属性（CSS 变量）或使用此项技术的库（例如 Bootstrap），则可以通过将 variables 参数设置为 true 来删除未使用的 CSS 变量
    variables: false,

    //拒绝（默认值：false）
    // 有时更实际的做法是扫描删除的列表，看看是否有明显的错误。 如果您想这样做，请开启 rejected 参数。
    rejected: false,

    //你可以将 CSS 选择器加入到白名单中来阻止 Purgecss 将其从 CSS 代码中删除。这可以通过 whitelist 和 whitelistPatterns 参数实现。
    //whitelist: ['random', 'yep', 'button'],

    //你可以通过 whitelistPatterns 参数来设置基于正则表达式的 CSS 选择器白名单。
    //whitelistPatterns: [/red$/],

    //白名单模式儿童
    //whitelistPatternsChildren: [/red$/],

}
