.custom-analysis {
  overflow: hidden;
}

.custom-analysis .top {
  height: 38px;
  background: var(--gensci-second);
  color: white;
  font-size: 15px;
  display: flex;
  align-items: center;
  padding: 0 8px;
  box-shadow: 0 2px 2px #d2d2d2;
}

.custom-analysis .content {
  height: calc(100% - 35px);
}

.custom-analysis .fontMain {
  color: var(--gensci-main);
}

.custom-analysis .fontSecond {
  color: var(--gensci-second);
}

.custom-analysis .header {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px 0 5px;
  background: #3644ac0f;
}

.custom-analysis .container {
  height: 100%;
  overflow: auto;
}

.custom-analysis .footer {
  width: 100%;
  height: 55px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  position: relative;
  bottom: 10px;
}

.custom-analysis .countFunc {
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 15px;
  color: black;
}

.custom-analysis .headerContext {
  display: flex;
  align-items: center;
  font-size: 15px;
}

.adm-auto-center-content {
  overflow-wrap: anywhere;
}
