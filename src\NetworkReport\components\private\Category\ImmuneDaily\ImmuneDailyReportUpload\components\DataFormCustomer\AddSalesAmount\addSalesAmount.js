import React, {
  useState,
  useEffect,
  RefObject,
  useImperativeHandle,
} from "react";
import addSalesAmount from "./addSalesAmount.less";

import {
  But<PERSON>,
  Popup,
  Form,
  Input,
  SearchBar,
  <PERSON><PERSON><PERSON>ie<PERSON>,
  <PERSON><PERSON><PERSON>,
  Picker,
  <PERSON>per,
  Toast,
} from "antd-mobile";
import dayjs from "dayjs";
import { http1 } from "../../../../../../../../utils/network";
import Big from "big.js";

const AddSalesAmount = (props) => {
  const {
    code3Info,
    setCode3Info,
    commonInfo,
    setCommonInfo,
    itemData = {},
    userInfo = {},
  } = props;
  console.log("props=========", props);

  const [tempCode3, setTempCode3] = useState({});
  const [tempCode3Old, setTempCode3Old] = useState({});

  const [form] = Form.useForm();
  const productType = Form.useWatch("productType", form);
  const secProductType = Form.useWatch("secProductType", form);
  const volume = Form.useWatch("volume", form);
  const priceUnit = Form.useWatch("priceUnit", form);
  const newPatientPrice = Form.useWatch("newPatientPrice", form);
  const npVolume = Form.useWatch("npVolume", form);
  const oldPatientPrice = Form.useWatch("oldPatientPrice", form);
  const opVolume = Form.useWatch("opVolume", form);
  const newPatNum = Form.useWatch("newPatNum", form);
  const oldPatNum = Form.useWatch("oldPatNum", form);

  useImperativeHandle(props.childRef, () => {
    return {
      validateForm,
      handleFormData,
      currentProducts,
    };
  });

  const currentProducts = () => {
    return products[0];

    // if(props.formDialog === "manager" || props.formDialog === 'money') {
    // 	return products[0]
    // }else {
    // 	return basicProduct[0]
    // }
  };

  const validateForm = async () => {
    await form.validateFields();
  };

  const handleFormData = () => {
    let values = form.getFieldsValue();
    return values;
  };

  const getPatient = async (threeLvlVarietCode) => {
    try {
      if (!threeLvlVarietCode) return false;
      const res =
        (await http1.get(`/meta/api/patient/type/${threeLvlVarietCode}`)) || [];
      setBasicType([
        res.map((d) => ({
          label: d,
          value: d,
        })),
      ]);
    } catch (e) {}
  };

  useEffect(() => {
    getPatient(productType && productType[0]);
  }, [productType]);

  const queryDoctor = () => {
    const hospitalId = props.hospitalId;
    http1
      .post("/immuneDaily/immuneDaily/doctor/query", { hospitalId: hospitalId })
      .then((res) => {
        console.log(res, "resresres");
        let temp = [];
        temp =
          res.length > 0 &&
          res.map((item) => {
            return { label: item.doctorCode, value: item.doctorCode };
          });
        if (temp.length > 0) {
          setBasicColumns([temp]);
        } else {
          setBasicColumns([[{ label: "", value: "" }]]);
        }
      });
  };

  const queryDoctorOld = () => {
    const hospitalId = props.hospitalId;
    http1
      .post("/immuneDaily/immuneDaily/doctor/query", { hospitalId: hospitalId })
      .then((res) => {
        let temp = [];
        temp =
          res.length > 0 &&
          res.map((item) => {
            return { label: item.doctorCode, value: item.doctorCode };
          });
        if (temp.length > 0) {
          setBasicColumnsOld([temp]);
        } else {
          setBasicColumnsOld([[{ label: "", value: "" }]]);
        }
      });
  };

  useEffect(() => {
    const hospitalId = props.hospitalId;
    if (hospitalId) {
      queryDoctor();
      queryDoctorOld();
    }
    if (itemData.productType !== "0JSM") {
      onSearchPCode3(itemData.productType);
    }

    http1.post("/meta/select", { tag: "NerveProduct" }).then((res) => {
      let temp = [];
      temp =
        res.length > 0 &&
        res.map((item) => {
          return { label: item.description, value: item.code };
        });
      if (temp.length > 0) {
        setBasicProduct([temp]);
      } else {
        setBasicProduct([[{ label: "", value: "" }]]);
      }
    });
  }, []);

  const [basicColumns, setBasicColumns] = useState([]); //编码3请求数据

  const [basicColumnsOld, setBasicColumnsOld] = useState([]); //编码3请求数据

  const [basicProduct, setBasicProduct] = useState([]); //产品请求数据

  const [basicType, setBasicType] = useState([]); //患者类型请求数据

  const [basicMoneyType, setBasicMoneyType] = useState([]); //剂型

  const handleVolume = (value) => {
    let str = String(value * priceUnit);
    console.log(str);
    if (str.indexOf(".") !== -1) {
      str = str.slice(0, str.indexOf(".") + 3);
      console.log(str);
    }
    form.setFieldsValue({ price: str });
  };

  const handlePriceUnit = (value) => {
    if (value.indexOf(".") !== -1) {
      value = value.slice(0, value.indexOf(".") + 3);
    }
    let x = new Big(value);
    let str = x.times(volume);
    form.setFieldsValue({ price: str });
  };

  const [code3PopupShow, setCode3PopupShow] = useState(false);
  const [code3PopupShowOld, setCode3PopupShowOld] = useState(false);

  const [code3Show, setCode3Show] = useState(true);
  const [code3ShowOld, setCode3ShowOld] = useState(true);

  const [code3Name, setCode3Name] = useState("");
  const [code3NameOld, setCode3NameOld] = useState("");

  const handleCode3Name = (val) => {
    setCode3Name(val);
  };
  const handleCode3NameOld = (val) => {
    setCode3NameOld(val);
  };

  const onSearchPCode3 = (brand = null) => {
    const _brand =
      brand ||
      (productType && productType[0]) ||
      commonInfo.productName ||
      itemData.productThreeName;
    if (_brand) {
      http1
        .post("/doctor/info/queryBy", {
          empNo: userInfo.empno,
          buCode: "neuroendocrine",
          hospitalId: props.hospitalId,
          brand: _brand,
          page: 1,
          size: 10000,
          doctorName: code3Name,
          doctorCode: "",
        })

        .then((res) => {
          let temp = [];
          temp =
            res.list.length > 0 &&
            res.list.map((item) => {
              return { label: item.doctorCode, value: item.doctorCode };
            });
          if (temp.length > 0) {
            setCode3Show(true);
            setBasicColumns([temp]);
          } else {
            setCode3Show(false);
            setBasicColumns([[{ label: "", value: "" }]]);
          }
        });
    }
  };
  const onSearchPCode3Old = (brand = null) => {
    if (
      brand ||
      commonInfo.name ||
      itemData.secProductThreeName ||
      (secProductType && secProductType[0])
    ) {
      http1
        .post("/doctor/info/queryBy", {
          empNo: userInfo.empno,
          buCode: "neuroendocrine",
          hospitalId: props.hospitalId,
          brand:
            brand ||
            commonInfo.name ||
            itemData.secProductThreeName ||
            (secProductType && secProductType[0]),
          page: 1,
          size: 10000,
          doctorName: code3NameOld,
          doctorCode: "",
        })

        .then((res) => {
          let temp = [];
          temp =
            res.list.length > 0 &&
            res.list.map((item) => {
              return { label: item.doctorCode, value: item.doctorCode };
            });
          if (temp.length > 0) {
            setCode3ShowOld(true);
            setBasicColumnsOld([temp]);
          } else {
            setCode3ShowOld(false);
            setBasicColumnsOld([[{ label: "", value: "" }]]);
          }
        });
    }
  };

  const handleConfirmCode3 = () => {
    setCode3Info({
      ...code3Info,
      doctorCode: tempCode3.value,
      doctorName: tempCode3.label,
    });
    form.setFields([
      {
        name: "doctorCode",
        value: tempCode3.value,
        errors: null,
      },
    ]);
    form.setFieldValue("doctorCode", tempCode3.value);
    setCode3PopupShow(false);
  };
  const handleConfirmCode3Old = () => {
    setCode3Info({
      ...code3Info,
      doctorSecCode: tempCode3Old.value,
      doctorSecName: tempCode3Old.label,
    });
    form.setFields([
      {
        name: "doctorSecCode",
        value: tempCode3Old.value,
        errors: null,
      },
    ]);
    form.setFieldValue("doctorSecCode", tempCode3Old.value);
    setCode3PopupShowOld(false);
  };

  const products = props.products || [];
  const fullProducts = props.fullProducts || [];

  useEffect(() => {
    if (products && products[0].length === 1) {
      form.setFieldValue("productType", [products[0][0].value]);
      const _specificationColumns = specificationColumns(products[0][0].value);
      if (_specificationColumns.length === 1) {
        form.setFieldValue("specification", [_specificationColumns[0].value]);
      }
    }
  }, [products, props.visible]);

  useEffect(() => {
    if (itemData.id) {
      onSearchPCode3();
      onSearchPCode3Old();
    }
  }, [itemData.id]);

  const specificationColumns = (productType) => {
    return fullProducts
      .filter((d) => d.threeLvlVarietCode === productType)
      .map((d) => ({ label: d.spec, value: d.specCode }));
  };

  return (
    <div>
      {props.formDialog === "manager" && (
        <div className={addSalesAmount}>
          <Form form={form}>
            <Form.Item>
              <div>新增数据</div>
            </Form.Item>
            <Form.Item
              name="newPatNum"
              label="新增数"
              rules={[
                { required: true },
                {
                  pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
                  message: "新增数只能为整数",
                },
              ]}
              initialValue={itemData.newPatNum}
            >
              <Input type={"number"} placeholder="请填写" max={99999} />
            </Form.Item>
            {!(newPatNum == 0) && (
              <>
                <Form.Item
                  name="productType"
                  label="产品"
                  trigger="onConfirm"
                  rules={[{ required: true }]}
                  onClick={(e, pickref) => {
                    pickref.current?.open();
                  }}
                  initialValue={[itemData.productType].filter(Boolean)}
                >
                  <Picker
                    columns={products}
                    onConfirm={(v, extend) => {
                      const [item] = extend.items;
                      if (item?.value !== "0JSM") {
                        onSearchPCode3(item?.label);
                      } else {
                        queryDoctor();
                      }
                      setCommonInfo({
                        ...commonInfo,
                        productName: extend.items[0].label,
                      });
                      setCode3Show(true);
                      form.setFieldValue("doctorCode", []);
                      form.setFieldValue("patientType", []);
                      form.setFieldValue("specification", []);
                    }}
                  >
                    {(e) => {
                      return (
                        <div>
                          {(e && e[0] && e[0].label) || (
                            <span style={{ color: "#ccc" }}>请选择</span>
                          )}
                        </div>
                      );
                    }}
                  </Picker>
                </Form.Item>

                <Form.Item
                  name="doctorCode"
                  label="编码3"
                  trigger="onConfirm"
                  rules={[{ required: true }]}
                  onClick={() => {
                    if (productType && !productType.length) {
                      Toast.show("请先选择产品！");
                    } else {
                      setCode3PopupShow(true);
                    }
                  }}
                  initialValue={itemData.doctorCode}
                >
                  {code3Info.doctorCode ? (
                    <Input value={code3Info.doctorCode} />
                  ) : (
                    <span style={{ color: "#ccc" }}>请选择</span>
                  )}
                </Form.Item>

                <Form.Item
                  name="specification"
                  disabled={!productType}
                  label="剂型"
                  trigger="onConfirm"
                  rules={[{ required: true }]}
                  onClick={(e, pickref) => {
                    if (productType && !productType.length) {
                      Toast.show("请先选择产品！");
                    } else {
                      pickref.current?.open();
                    }
                  }}
                  initialValue={[itemData.productCode].filter(Boolean)}
                >
                  <Picker
                    columns={[
                      specificationColumns(productType && productType[0]),
                    ]}
                    onConfirm={(v, extend) => {
                      setCommonInfo({
                        ...commonInfo,
                        specificationName: extend.items[0].label,
                      });
                    }}
                  >
                    {(e) => {
                      return (
                        <div>
                          {(e && e[0] && e[0].label) || (
                            <span style={{ color: "#ccc" }}>请选择</span>
                          )}
                        </div>
                      );
                    }}
                  </Picker>
                </Form.Item>
                <Form.Item
                  name="patientType"
                  disabled={!productType}
                  label="适应症"
                  trigger="onConfirm"
                  rules={[{ required: true }]}
                  onClick={(e, pickref) => {
                    if (productType && !productType.length) {
                      Toast.show("请先选择产品！");
                    } else {
                      pickref.current?.open();
                    }
                  }}
                  initialValue={[itemData.patientType].filter(Boolean)}
                >
                  <Picker
                    columns={basicType}
                    onConfirm={(v, extend) => {
                      setCommonInfo({
                        ...commonInfo,
                        patientName: extend.items[0].label,
                      });
                    }}
                  >
                    {(e) => {
                      return (
                        <div>
                          {(e && e[0] && e[0].label) || (
                            <span style={{ color: "#ccc" }}>请选择</span>
                          )}
                        </div>
                      );
                    }}
                  </Picker>
                </Form.Item>
                <Form.Item
                  name="newPatientPrice"
                  label="新增单价"
                  rules={[{ required: true }]}
                  getValueFromEvent={(value) => {
                    return value.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); // 只能输入两位小数
                  }}
                  initialValue={itemData.newPatientPrice}
                >
                  <Input type={"number"} placeholder="请填写" max={99999} />
                </Form.Item>
                <Form.Item
                  name="npVolume"
                  label="处方数量"
                  rules={[
                    { required: true },
                    {
                      pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
                      message: "处方数量只能为整数",
                    },
                  ]}
                  initialValue={itemData.npVolume}
                >
                  <Input type={"number"} placeholder="请填写" max={99999} />
                </Form.Item>
                <Form.Item
                  name="newPatientAmount"
                  label="金额"
                  value={
                    newPatientPrice !== undefined &&
                    npVolume !== undefined &&
                    newPatientPrice * npVolume
                  }
                >
                  {newPatientPrice !== undefined &&
                    npVolume !== undefined &&
                    Number(newPatientPrice * npVolume).toFixed(2)}
                </Form.Item>
              </>
            )}
            <Form.Item>
              <div>存量数据</div>
            </Form.Item>
            <Form.Item
              name="oldPatNum"
              label="存量数"
              rules={[
                { required: true },
                {
                  pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
                  message: "存量数只能为整数",
                },
              ]}
              initialValue={itemData.oldPatNum}
            >
              <Input type={"number"} placeholder="请填写" max={99999} />
            </Form.Item>
            {!(oldPatNum == 0) && (
              <>
                <Form.Item
                  name="secProductType"
                  label="产品"
                  trigger="onConfirm"
                  rules={[{ required: true }]}
                  onClick={(e, pickref) => {
                    pickref.current?.open();
                  }}
                  initialValue={[itemData.secProductType].filter(Boolean)}
                >
                  <Picker
                    columns={products}
                    onConfirm={(v, extend) => {
                      const [item] = extend.items;
                      if (item?.value !== "0JSM") {
                        onSearchPCode3Old(item?.label);
                      } else {
                        queryDoctorOld();
                      }
                      setCommonInfo({
                        ...commonInfo,
                        productName: extend.items[0].label,
                      });
                      setCode3ShowOld(true);
                      form.setFieldValue("doctorSecCode", []);
                      form.setFieldValue("source", []);
                      form.setFieldValue("secSpecification", []);
                    }}
                  >
                    {(e) => {
                      return (
                        <div>
                          {(e && e[0] && e[0].label) || (
                            <span style={{ color: "#ccc" }}>请选择</span>
                          )}
                        </div>
                      );
                    }}
                  </Picker>
                </Form.Item>

                <Form.Item
                  name="doctorSecCode"
                  label="编码3"
                  trigger="onConfirm"
                  rules={[{ required: true }]}
                  onClick={() => {
                    if (secProductType && !secProductType.length) {
                      Toast.show("请先选择产品！");
                    } else {
                      setCode3PopupShowOld(true);
                    }
                  }}
                  initialValue={itemData.doctorSecCode}
                >
                  {code3Info.doctorSecCode ? (
                    <Input value={code3Info.doctorSecCode} />
                  ) : (
                    <span style={{ color: "#ccc" }}>请选择</span>
                  )}
                </Form.Item>

                <Form.Item
                  name="secSpecification"
                  disabled={!secProductType}
                  label="剂型"
                  trigger="onConfirm"
                  rules={[{ required: true }]}
                  onClick={(e, pickref) => {
                    if (secProductType && !secProductType.length) {
                      Toast.show("请先选择产品！");
                    } else {
                      pickref.current?.open();
                    }
                  }}
                  initialValue={[itemData.secProductCode].filter(Boolean)}
                >
                  <Picker
                    columns={[
                      specificationColumns(secProductType && secProductType[0]),
                    ]}
                    onConfirm={(v, extend) => {
                      setCommonInfo({
                        ...commonInfo,
                        specificationName: extend.items[0].label,
                      });
                    }}
                  >
                    {(e) => {
                      return (
                        <div>
                          {(e && e[0] && e[0].label) || (
                            <span style={{ color: "#ccc" }}>请选择</span>
                          )}
                        </div>
                      );
                    }}
                  </Picker>
                </Form.Item>
                {/*<Form.Item*/}
                {/*  name="source"*/}
                {/*  label="来源"*/}
                {/*  rules={[{ required: true }]}*/}
                {/*  initialValue={[itemData.source].filter(Boolean)}*/}
                {/*  onClick={(e, pickerRef) => {*/}
                {/*    pickerRef.current?.open()*/}
                {/*  }}*/}
                {/*  trigger="onConfirm"*/}
                {/*>*/}
                {/*  <Picker*/}
                {/*    columns={[*/}
                {/*      [*/}
                {/*        {*/}
                {/*          label: '当月确定复购存量',*/}
                {/*          value: '0',*/}
                {/*        },*/}
                {/*        {*/}
                {/*          label: '当月停药恢复存量',*/}
                {/*          value: '1',*/}
                {/*        },*/}
                {/*        {*/}
                {/*          label: '历史停药恢复存量',*/}
                {/*          value: '2',*/}
                {/*        },*/}
                {/*      ],*/}
                {/*    ]}*/}
                {/*  >*/}
                {/*    {(e) => {*/}
                {/*      return (*/}
                {/*        <div>*/}
                {/*          {(e && e[0] && e[0].label) || (*/}
                {/*            <span style={{ color: '#ccc' }}>请选择</span>*/}
                {/*          )}*/}
                {/*        </div>*/}
                {/*      )*/}
                {/*    }}*/}
                {/*  </Picker>*/}
                {/*</Form.Item>*/}
                <Form.Item
                  name="oldPatientPrice"
                  label="存量单价"
                  rules={[{ required: true }]}
                  getValueFromEvent={(value) => {
                    return value.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); // 只能输入两位小数
                  }}
                  initialValue={itemData.oldPatientPrice}
                >
                  <Input type={"number"} placeholder="请填写" max={99999} />
                </Form.Item>
                <Form.Item
                  name="opVolume"
                  label="处方数量"
                  rules={[
                    { required: true },
                    {
                      pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
                      message: "处方数量只能为整数",
                    },
                  ]}
                  initialValue={itemData.opVolume}
                >
                  <Input type={"number"} placeholder="请填写" max={99999} />
                </Form.Item>
                <Form.Item
                  name="oldPatientAmount"
                  label="金额"
                  value={
                    oldPatientPrice !== undefined &&
                    opVolume !== undefined &&
                    oldPatientPrice * opVolume
                  }
                >
                  {oldPatientPrice !== undefined &&
                    opVolume !== undefined &&
                    Number(oldPatientPrice * opVolume).toFixed(2)}
                </Form.Item>
              </>
            )}

            <Popup
              visible={code3PopupShow}
              onMaskClick={() => {
                setCode3PopupShow(false);
              }}
              bodyStyle={{
                borderTopLeftRadius: "8px",
                borderTopRightRadius: "8px",
                minHeight: "40vh",
              }}
            >
              <div
                className={"adm-picker-header"}
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  height: "42px",
                }}
              >
                <span
                  className={"adm-picker-header-button"}
                  style={{
                    fontSize: "15px",
                    color: "var(--gensci-main)",
                  }}
                  onClick={() => setCode3PopupShow(false)}
                >
                  取消
                </span>
                <span
                  className={"adm-picker-header-button"}
                  style={{
                    fontSize: "15px",
                    color: "var(--gensci-main)",
                  }}
                  onClick={handleConfirmCode3}
                >
                  确认
                </span>
              </div>
              <div className={addSalesAmount.addSalesAmount__searchContainer}>
                <SearchBar
                  placeholder="请输入内容"
                  onChange={(e) => handleCode3Name(e)}
                  style={{ marginRight: "10px", flex: "1" }}
                />
                <Button
                  size={"small"}
                  color={"primary"}
                  onClick={() => {
                    if (productType && productType[0] === "0JSM") {
                      queryDoctor();
                    } else {
                      onSearchPCode3();
                    }
                  }}
                >
                  查询
                </Button>
              </div>
              {code3Show ? (
                <PickerView
                  columns={basicColumns}
                  onChange={(val, extend) => {
                    setTempCode3(extend.items[0] && extend.items[0]);
                  }}
                ></PickerView>
              ) : (
                <div
                  style={{
                    width: "60px",
                    margin: "80px auto",
                    color: "#ccc",
                  }}
                >
                  暂无数据
                </div>
              )}
            </Popup>

            <Popup
              visible={code3PopupShowOld}
              onMaskClick={() => {
                setCode3PopupShowOld(false);
              }}
              bodyStyle={{
                borderTopLeftRadius: "8px",
                borderTopRightRadius: "8px",
                minHeight: "40vh",
              }}
            >
              <div
                className={"adm-picker-header"}
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  height: "42px",
                }}
              >
                <span
                  className={"adm-picker-header-button"}
                  style={{
                    fontSize: "15px",
                    color: "var(--gensci-main)",
                  }}
                  onClick={() => setCode3PopupShowOld(false)}
                >
                  取消
                </span>
                <span
                  className={"adm-picker-header-button"}
                  style={{
                    fontSize: "15px",
                    color: "var(--gensci-main)",
                  }}
                  onClick={handleConfirmCode3Old}
                >
                  确认
                </span>
              </div>
              <div className={addSalesAmount.addSalesAmount__searchContainer}>
                <SearchBar
                  placeholder="请输入内容"
                  onChange={(e) => handleCode3NameOld(e)}
                  style={{ marginRight: "10px", flex: "1" }}
                />
                <Button
                  size={"small"}
                  color={"primary"}
                  onClick={() => {
                    if (productType && productType[0] === "0JSM") {
                      queryDoctorOld();
                    } else {
                      onSearchPCode3Old();
                    }
                  }}
                >
                  查询
                </Button>
              </div>
              {code3ShowOld ? (
                <PickerView
                  columns={basicColumnsOld}
                  onChange={(val, extend) => {
                    setTempCode3Old(extend.items[0] && extend.items[0]);
                  }}
                ></PickerView>
              ) : (
                <div
                  style={{
                    width: "60px",
                    margin: "80px auto",
                    color: "#ccc",
                  }}
                >
                  暂无数据
                </div>
              )}
            </Popup>
          </Form>
        </div>
      )}

      {props.formDialog === "type" && (
        <div className={addSalesAmount}>
          <Form form={form}>
            <Form.Item
              name="doctorCode"
              label="编码3"
              trigger="onConfirm"
              rules={[{ required: true }]}
              onClick={() => setCode3PopupShow(true)}
              initialValue={itemData.doctorCode}
            >
              {code3Info.doctorCode ? (
                <Input value={code3Info.doctorCode} />
              ) : null}
              <Popup
                visible={code3PopupShow}
                onMaskClick={() => {
                  setCode3PopupShow(false);
                }}
                bodyStyle={{
                  borderTopLeftRadius: "8px",
                  borderTopRightRadius: "8px",
                  minHeight: "40vh",
                }}
              >
                <div
                  className={"adm-picker-header"}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    height: "42px",
                  }}
                >
                  <span
                    className={"adm-picker-header-button"}
                    style={{
                      fontSize: "15px",
                      color: "var(--gensci-main)",
                    }}
                    onClick={() => setCode3PopupShow(false)}
                  >
                    取消
                  </span>
                  <span
                    className={"adm-picker-header-button"}
                    style={{
                      fontSize: "15px",
                      color: "var(--gensci-main)",
                    }}
                    onClick={handleConfirmCode3}
                  >
                    确认
                  </span>
                </div>
                <div className={addSalesAmount.addSalesAmount__searchContainer}>
                  <SearchBar
                    placeholder="请输入内容"
                    onChange={(e) => handleCode3Name(e)}
                    style={{ marginRight: "10px", flex: "1" }}
                  />
                  <Button
                    size={"small"}
                    color={"primary"}
                    onClick={() => onSearchPCode3()}
                  >
                    查询
                  </Button>
                </div>
                {code3Show ? (
                  <PickerView
                    columns={basicColumns}
                    onChange={(val, extend) => {
                      setTempCode3(extend.items[0] && extend.items[0]);
                    }}
                  ></PickerView>
                ) : (
                  <div
                    style={{
                      width: "60px",
                      margin: "80px auto",
                      color: "#ccc",
                    }}
                  >
                    暂无数据
                  </div>
                )}
              </Popup>
            </Form.Item>
            <Form.Item
              name="productType"
              label="产品"
              trigger="onConfirm"
              rules={[{ required: true }]}
              onClick={(e, pickref) => {
                pickref.current?.open();
              }}
              initialValue={[itemData.productType].filter(Boolean)}
            >
              <Picker
                columns={products}
                onConfirm={(v, extend) => {
                  setCommonInfo({
                    ...commonInfo,
                    productName: extend.items[0] && extend.items[0].label,
                  });
                }}
              >
                {(items) => {
                  if (items.every((item) => item === null)) {
                    return null;
                  } else {
                    return items.map((item) => item?.label ?? "未选择");
                  }
                }}
              </Picker>
            </Form.Item>
            <Form.Item
              name="upVolume"
              label="用药患者数"
              rules={[
                { required: true },
                {
                  pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
                  message: "用药患者数只能为整数",
                },
              ]}
              initialValue={itemData.upVolume}
            >
              <Input type={"number"} placeholder="请填写" max={99999} />
            </Form.Item>
            <Form.Item
              name="spVolume"
              label="停药患者数"
              rules={[
                { required: true },
                {
                  pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
                  message: "停药患者数只能为整数",
                },
              ]}
              initialValue={itemData.spVolume}
            >
              <Input max={99999} type={"number"} placeholder="请填写" />
            </Form.Item>
          </Form>
        </div>
      )}
      {props.formDialog === "money" && (
        <div className={addSalesAmount}>
          <Form
            form={form}
            // initialValues={{volume: 0}}
          >
            <Form.Item
              name="doctorCode"
              label="编码3"
              trigger="onConfirm"
              rules={[{ required: true }]}
              onClick={() => setCode3PopupShow(true)}
              initialValue={itemData.doctorCode}
            >
              {code3Info.doctorCode ? (
                <Input value={code3Info.doctorCode} />
              ) : null}
              <Popup
                visible={code3PopupShow}
                onMaskClick={() => {
                  setCode3PopupShow(false);
                }}
                bodyStyle={{
                  borderTopLeftRadius: "8px",
                  borderTopRightRadius: "8px",
                  minHeight: "40vh",
                }}
              >
                <div
                  className={"adm-picker-header"}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    height: "42px",
                  }}
                >
                  <span
                    className={"adm-picker-header-button"}
                    style={{
                      fontSize: "15px",
                      color: "var(--gensci-main)",
                    }}
                    onClick={() => setCode3PopupShow(false)}
                  >
                    取消
                  </span>
                  <span
                    className={"adm-picker-header-button"}
                    style={{
                      fontSize: "15px",
                      color: "var(--gensci-main)",
                    }}
                    onClick={handleConfirmCode3}
                  >
                    确认
                  </span>
                </div>
                <div className={addSalesAmount.addSalesAmount__searchContainer}>
                  <SearchBar
                    placeholder="请输入内容"
                    onChange={(e) => handleCode3Name(e)}
                    style={{ marginRight: "10px", flex: "1" }}
                  />
                  <Button
                    size={"small"}
                    color={"primary"}
                    onClick={() => onSearchPCode3()}
                  >
                    查询
                  </Button>
                </div>
                {code3Show ? (
                  <PickerView
                    columns={basicColumns}
                    onChange={(val, extend) => {
                      setTempCode3(extend.items[0] && extend.items[0]);
                    }}
                  ></PickerView>
                ) : (
                  <div
                    style={{
                      width: "60px",
                      margin: "80px auto",
                      color: "#ccc",
                    }}
                  >
                    暂无数据
                  </div>
                )}
              </Popup>
            </Form.Item>

            <Form.Item
              name="productType"
              label="产品"
              trigger="onConfirm"
              rules={[{ required: true }]}
              onClick={(e, pickref) => {
                pickref.current?.open();
              }}
              initialValue={[itemData.productType].filter(Boolean)}
            >
              <Picker
                columns={products}
                onConfirm={(v, extend) => {
                  setCommonInfo({
                    ...commonInfo,
                    productName: extend.items[0].label,
                  });
                  const _specificationColumns = specificationColumns(v[0]);
                  if (_specificationColumns.length === 1) {
                    form.setFieldValue("specification", [
                      _specificationColumns[0].value,
                    ]);
                  } else {
                    form.setFieldValue("specification", []);
                  }
                }}
              >
                {(items) => {
                  if (items.every((item) => item === null)) {
                    return null;
                  } else {
                    return items.map((item) => item?.label ?? "未选择");
                  }
                }}
              </Picker>
            </Form.Item>
            <Form.Item
              disabled={!productType}
              name="specification"
              label="规格"
              trigger="onConfirm"
              rules={[{ required: true }]}
              onClick={(e, pickref) => {
                if (productType && !productType.length) {
                  Toast.show("请先选择产品！");
                } else {
                  pickref.current?.open();
                }
              }}
              initialValue={[itemData.productCode].filter(Boolean)}
            >
              <Picker
                columns={[specificationColumns(productType && productType[0])]}
                onConfirm={(v, extend) => {
                  setCommonInfo({
                    ...commonInfo,
                    specificationName: extend.items[0].label,
                  });
                }}
              >
                {(items) => {
                  if (items.every((item) => item === null)) {
                    return null;
                  } else {
                    return items.map((item) => item?.label ?? "未选择");
                  }
                }}
              </Picker>
            </Form.Item>

            <Form.Item
              name="volume"
              rules={[{ type: "number" }, { required: true }]}
              label="处方数量"
              initialValue={itemData.volume || 0}
            >
              <Stepper min={0} digits={0} onChange={handleVolume} />
            </Form.Item>
            <Form.Item
              name="priceUnit"
              label="单价(元)"
              rules={[{ required: true }]}
              getValueFromEvent={(value) => {
                return value.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); // 只能输入两位小数
              }}
              initialValue={itemData.priceUnit}
            >
              <Input
                type="number"
                min={0}
                onChange={handlePriceUnit}
                max={99999}
              />
            </Form.Item>
            <Form.Item
              name="price"
              label="金额(元)"
              rules={[{ required: true }]}
              initialValue={Number(priceUnit * volume)}
            >
              {/*<Input disabled type="number" min={0} />*/}
              <div>
                {Number(priceUnit * volume)
                  .toFixed(2)
                  .replace("NaN", "")}
              </div>
            </Form.Item>
          </Form>
        </div>
      )}
      {props.formDialog === "person" && (
        <div className={addSalesAmount}>
          <Form form={form}>
            <Form.Item
              name="productType"
              label="产品"
              trigger="onConfirm"
              rules={[{ required: true }]}
              onClick={(e, datePickerRef) => {
                datePickerRef.current?.open();
              }}
              initialValue={[itemData.productType].filter(Boolean)}
            >
              <Picker
                columns={products}
                onConfirm={(v, extend) => {
                  setCommonInfo({
                    ...commonInfo,
                    productName: extend.items[0].label,
                  });
                  form.setFieldValue("doctorCode", []);
                  setCode3Info({});
                  setCode3Show(true);
                  const [item] = extend.items;
                  if (item?.value !== "0JSM") {
                    onSearchPCode3(item?.label);
                  } else {
                    queryDoctor();
                  }
                }}
              >
                {(items) => {
                  if (items.every((item) => item === null)) {
                    return null;
                  } else {
                    return items.map((item) => item?.label ?? "未选择");
                  }
                }}
              </Picker>
            </Form.Item>
            <Form.Item
              name="doctorCode"
              label="编码3"
              trigger="onConfirm"
              rules={[{ required: true }]}
              onClick={() => {
                if (productType && !productType.length) {
                  Toast.show("请先选择产品！");
                } else {
                  setCode3PopupShow(true);
                }
              }}
              initialValue={itemData.doctorCode}
            >
              {code3Info.doctorCode ? (
                <Input value={code3Info.doctorCode} />
              ) : null}
              <Popup
                visible={code3PopupShow}
                onMaskClick={() => {
                  setCode3PopupShow(false);
                }}
                bodyStyle={{
                  borderTopLeftRadius: "8px",
                  borderTopRightRadius: "8px",
                  minHeight: "40vh",
                }}
              >
                <div
                  className={"adm-picker-header"}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    height: "42px",
                  }}
                >
                  <span
                    className={"adm-picker-header-button"}
                    style={{
                      fontSize: "15px",
                      color: "var(--gensci-main)",
                    }}
                    onClick={() => setCode3PopupShow(false)}
                  >
                    取消
                  </span>
                  <span
                    className={"adm-picker-header-button"}
                    style={{
                      fontSize: "15px",
                      color: "var(--gensci-main)",
                    }}
                    onClick={handleConfirmCode3}
                  >
                    确认
                  </span>
                </div>
                <div className={addSalesAmount.addSalesAmount__searchContainer}>
                  <SearchBar
                    placeholder="请输入内容"
                    onChange={(e) => handleCode3Name(e)}
                    style={{ marginRight: "10px", flex: "1" }}
                  />
                  <Button
                    size={"small"}
                    color={"primary"}
                    onClick={() => {
                      if (productType && productType[0] === "0JSM") {
                        queryDoctor();
                      } else {
                        onSearchPCode3();
                      }
                    }}
                  >
                    查询
                  </Button>
                </div>
                {code3Show ? (
                  <PickerView
                    columns={basicColumns}
                    onChange={(val, extend) => {
                      setTempCode3(extend.items[0] && extend.items[0]);
                    }}
                  ></PickerView>
                ) : (
                  <div
                    style={{
                      width: "60px",
                      margin: "80px auto",
                      color: "#ccc",
                    }}
                  >
                    暂无数据
                  </div>
                )}
              </Popup>
            </Form.Item>

            <Form.Item
              name="insertTime"
              label="新客户产生时间"
              trigger="onConfirm"
              rules={[{ required: true }]}
              onClick={(e, datePickerRef) => {
                datePickerRef.current?.open();
              }}
              initialValue={
                itemData.insertTime ? new Date(itemData.insertTime) : undefined
              }
            >
              <DatePicker>
                {(value) =>
                  value ? (
                    dayjs(value).format("YYYY-MM-DD")
                  ) : (
                    <div style={{ color: "#ccc" }}>请选择日期</div>
                  )
                }
              </DatePicker>
            </Form.Item>
            <Form.Item
              name="newPatNum"
              label="处方新增总数"
              rules={[
                { required: true },
                {
                  pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
                  message: "处方新增总数只能为整数",
                },
              ]}
              initialValue={itemData.newPatNum}
            >
              <Input type={"number"} placeholder="请填写" max={99999} />
            </Form.Item>
          </Form>
        </div>
      )}
    </div>
  );
};

export default AddSalesAmount;
