//JS语法转换配置模块
module.exports = {
    //从下往上运行
    presets: [
        [
            "@babel/preset-env",
            {
                "modules": false, //对ES6的模块文件不做转化，以便使用tree shaking、sideEffects等 ,默认auto

                // browserslist环境不支持的所有垫片都导入
                //"entry"会根据浏览器版本的支持，将 polyfill 按需引入 ,值为false时不对 polyfill 做操作。
                //"usage" 会根据配置的浏览器兼容，以及你代码中用到的 API 来进行 polyfill，实现了按需添加
                "useBuiltIns": "entry",

                "corejs": {
                    "version": 3, // 使用core-js@3
                    "proposals": true,
                },

                //宽松模式
                // "loose":true,
                //为此预设中支持它们的任何插件启用更多符合规范的，但可能更慢的转换。
                //"spec":false,

                //"bugfixes":false,

                //"debug":false

                //制定浏览器兼容
                //"targets": {
                //     "chrome": '60',
                //     "firefox": '60',
                //     "ie": '8',
                //     "safari": '10',
                //     "edge": '17'
                // }
            }
        ],
        //react jsx编译
        "@babel/preset-react"
    ],
    //从上往下运行
    plugins: [
        [
            //装饰器语法编译
            "@babel/plugin-proposal-decorators",
            { "legacy": true }
        ],
        [
            //class语法编译
            "@babel/plugin-proposal-class-properties",
            // { "loose": true }
        ],
        // [
        //     //antd ui组件的相关转换
        //     "import",
        //     {"libraryName": "antd", "libraryDirectory": "es", "style": "css"}
        // ],
        [
            //antd 移动ui组件的相关转换
            "import",
            { "libraryName": "antd-mobile", "libraryDirectory": "es/components", "style": false}
        ],
        [
            "@babel/plugin-transform-runtime",
            {"corejs": 3, "helpers": true, "regenerator": true, "useESModules": false, "absoluteRuntime":false}
        ]

    ],

    exclude: [/node_modules/], // 不要编译node_modules，不然会出一些奇奇怪怪的问题

    //include:[]
};
