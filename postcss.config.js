//CSS语法转换配置模块
// const autoprefixer = require('autoprefixer');
const PostcssPresetEnv = require('postcss-preset-env');
const PostcssSafeParser = require('postcss-safe-parser');

module.exports = {

    //CSS容错解析器，它将发现并修复语法错误，能够解析任何输入。
    parser: PostcssSafeParser,

    //Plugins for PostCSS
    plugins: [

        //Autoprefixer会根据当前浏览器的流行程度和属性支持使用数据为您应用前缀。
        // autoprefixer,

        //PostCSS Preset Env使您可以将现代CSS转换为大多数浏览器可以理解的内容，并根据目标浏览器或运行时环境确定所需的polyfill。
        PostcssPresetEnv,
        require("tailwindcss"),
        require("autoprefixer"),
    ],

};
