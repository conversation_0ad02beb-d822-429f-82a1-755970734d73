import React, {useEffect, useState} from 'react';
import styled from 'styled-components';
import Tabs from "./Tabs";
import {http1} from "../../../utils/network";
import {useNavigate, useSearchParams} from "react-router-dom";
import {Toast} from 'antd-mobile';

const Wrapper = styled.div`
  .merge-div {
    display: inline-block;
    border-bottom: 1px solid rgb(148, 163, 184);
  }

  table td {
    padding: 7px 16px;
    text-align: center;
  }

  table thead {
    background-color: #F3F4F5;
  }

  table thead tr th {
    padding: 7px 16px;
    font-size: 14px;
  }

  .zhou {
    color: #2551F2;
    /* 16/CN-Medium */
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    margin: 32px 0 16px 0;
  }

  .dept {
    color: #2551F2;
    /* 16/CN-Medium */
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    margin-bottom: 16px;
  }
`;

const Board = props => {
  const navigate = useNavigate();

  const [searchParams] = useSearchParams()

  const readLevelQuery = +searchParams.get('readLevel') || ''
  const empNoQuery = searchParams.get('empNo') || ''

  const {roleInfo, readLevel} = props;

  const {deptcode, estDate, empNo, level} = roleInfo || {};

  const [tabKey, setTabKey] = useState('');
  const [planData, setPlanData] = useState({}); // 地区经理 周计划
  const [hospitalBoardData, setHospitalBoardData] = useState([]); // 地区经理 机构看板
  const [deptBoardData, setDeptBoardData] = useState([]); // 地区经理 部门看板
  const [deptBoardTr, setDeptBoardTr] = useState([]); // 大区、区总 =》 动态部门看板 表头
  const [userBoardData, setUserBoardData] = useState([]); // 大区、区总 =》 动态部门看板 表头

  useEffect(() => {
    if (roleInfo && roleInfo.deptcode) {
      if (tabKey === '1') {
        getHospitalData();
      } else if (tabKey === '2') {
        getuserBoardData();
      } else if (tabKey === '3') {
        getDeptData();
      }
    }
  }, [roleInfo, tabKey])

  // 获取科室看板数据
  const getDeptData = async () => {
    try {
      Toast.show({
        icon: 'loading',
        duration: 0
      })
      const res = await http1.post('/estimate/query/board/dept', {
        empNo,
        estDate,
        deptcode
      }) || [];

      let data = {};
      const rowsData = [];
      if (level === 6) {
        data = res[0] || {};
        // 科室列表
        setDeptBoardData(data.boardVos || []);
        // 周计划
        setPlanData(data.planVo || {})
      } else {
        const rows = res[0] && res[0].boardVos || [];
        rows.forEach(item => {
          rowsData.push(
            {
              ...item,
              extra: res
            }
          )
        })

        console.log(rowsData, 'rowsData')
        // 科室列表
        setDeptBoardData(rowsData);
        // 地区、大区 设置 科室看板动态表头
        setDeptBoardTr(res)
        // 周计划
        // setPlanData(data.planVo || {})
      }

      Toast.clear();
    } catch (e) {
      Toast.clear();
    }

  }

  // 获取机构看板数据
  const getHospitalData = async () => {
    try {
      Toast.show({
        icon: 'loading',
        duration: 0
      })
      const res = await http1.post('/estimate/query/board/hospital', {
        empNo,
        estDate,
        deptcode
      }) || {};

      // 周计划
      const stringhosTypeVoMap = res.stringhosTypeVoMap || {};
      // 机构列表数据
      const planVo = res.planVo || {};

      const arr = [];
      for (const key in stringhosTypeVoMap) {
        arr.push({
          ...stringhosTypeVoMap[key],
          name: key
        })
      }
      setPlanData(planVo);
      setHospitalBoardData(arr)
      Toast.clear()
    } catch (e) {
      Toast.clear();
    }
  }

  // 获取人员视图 看板数据
  const getuserBoardData = async () => {
    try {
      Toast.show({
        icon: 'loading',
        duration: 0
      })
      const res = await http1.post('/estimate/query/board/user', {
        empNo,
        estDate,
        deptcode
      })
      setUserBoardData(res || []);
      Toast.clear()
    } catch (e) {
      Toast.clear()
    }
  }

  const tabOnChange = tabKey => {
    setTabKey(tabKey)
  }

  const deptBoardTrTdRender = (type) => {
    return (
      deptBoardTr.map((item, colIndex) => {
        const planVo = item.planVo || {};
        return (
          <td key={colIndex + item.deptcode + type} className="border border-slate-300">
            {planVo[type]}
          </td>
        )
      })
    )
  }

  const deptBoardTrTdRender2 = (type) => {
    return (
      userBoardData.map((item, colIndex) => {
        return (
          <td key={colIndex + item.deptcode + type} className="border border-slate-300">
            {item[type]}
          </td>
        )
      })
    )
  }

  // 合计
  const total = (data) => {
    let newPat = 0;
    let longPat = 0;
    let longRate = '0%';

    data.forEach(item => {
      newPat = +item.newPat + newPat;
      longPat = +item.longPat + longPat;
    })
    if(newPat) {
      longRate = ((longPat / newPat) * 100).toFixed(2) + '%';
    }

    return {
      newPat,
      longPat,
      longRate
    }
  }


  const planTable = () => {
    if ((level === 5 || level === 4) && tabKey === '3') {
      return (
        <div className="table-2">
          <div className="zhou">
            周度业务计划
          </div>
          <table className="border-collapse border border-slate-400 w-full">
            <thead>
            <tr>
              <th className="border border-slate-300 w-2/12"></th>
              <th className="border border-slate-300 w-5/12">分类</th>
              {
                deptBoardTr.map((item) => {
                  return (
                    <th key={item.deptcode} className="border border-slate-300">{item.deptname}</th>
                  )
                })
              }
            </tr>
            </thead>
            <tbody>
            <tr>
              <td className="border border-slate-300" rowSpan={3}>KH改善</td>
              <td className="border border-slate-300">观念谨慎型</td>
              {deptBoardTrTdRender('improveConceptNum')}
            </tr>
            <tr>
              <td className="border border-slate-300">合作改善型</td>
              {deptBoardTrTdRender('improveCooperateNum')}
            </tr>
            <tr>
              <td className="border border-slate-300">长效和KH改善型</td>
              {deptBoardTrTdRender('improveLongNum')}
            </tr>

            <tr>
              <td className="border border-slate-300" rowSpan={2}>行为</td>
              <td className="border border-slate-300">大区经理拜访</td>
              {deptBoardTrTdRender('distVisitNum')}
            </tr>
            <tr>
              <td className="border border-slate-300">大区经理协访</td>
              {deptBoardTrTdRender('distAidVisitNum')}
            </tr>

            <tr>
              <td className="border border-slate-300" rowSpan={3}>活动计划</td>
              <td className="border border-slate-300">增流活动目标数</td>
              {deptBoardTrTdRender('flowActivityNum')}
            </tr>
            <tr>
              <td className="border border-slate-300">帮扶/义诊目标数</td>
              {deptBoardTrTdRender('helpActivityNum')}
            </tr>
            <tr>
              <td className="border border-slate-300">学术活动目标数</td>
              {deptBoardTrTdRender('academicActivityNum')}
            </tr>

            <tr>
              <td className="border border-slate-300" rowSpan={2}>YZHZ</td>
              <td className="border border-slate-300">YZHZ储备数</td>
              {deptBoardTrTdRender('reserveNum')}
            </tr>
            <tr>
              <td className="border border-slate-300">新增宝宝数</td>
              {deptBoardTrTdRender('newBabyNum')}
            </tr>
            </tbody>
          </table>
        </div>
      )
    } else if (tabKey === '3') {
      return (
        <div className="table-2">
          <div className="zhou">
            周度业务计划
          </div>
          <table className="border-collapse border border-slate-400 w-full">
            <thead>
            <tr>
              <th className="border border-slate-300 w-2/12"></th>
              <th className="border border-slate-300 w-5/12">分类</th>
              <th className="border border-slate-300 w-5/12">下周({estDate})</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <td className="border border-slate-300" rowSpan={3}>KH改善</td>
              <td className="border border-slate-300">观念谨慎型</td>
              <td className="border border-slate-300">
                {planData.improveConceptNum}
              </td>
            </tr>
            <tr>
              <td className="border border-slate-300">合作改善型</td>
              <td className="border border-slate-300">
                {planData.improveCooperateNum}
              </td>
            </tr>
            <tr>
              <td className="border border-slate-300">长效和KH改善型</td>
              <td className="border border-slate-300">
                {planData.improveLongNum}
              </td>
            </tr>

            <tr>
              <td className="border border-slate-300" rowSpan={2}>行为</td>
              <td className="border border-slate-300">大区经理拜访</td>
              <td className="border border-slate-300">
                {planData.distVisitNum}
              </td>
            </tr>
            <tr>
              <td className="border border-slate-300">大区经理协访</td>
              <td className="border border-slate-300">
                {planData.distAidVisitNum}
              </td>
            </tr>

            <tr>
              <td className="border border-slate-300" rowSpan={3}>活动计划</td>
              <td className="border border-slate-300">增流活动目标数</td>
              <td className="border border-slate-300">
                {planData.flowActivityNum}
              </td>
            </tr>
            <tr>
              <td className="border border-slate-300">帮扶/义诊目标数</td>
              <td className="border border-slate-300">
                {planData.helpActivityNum}
              </td>
            </tr>
            <tr>
              <td className="border border-slate-300">学术活动目标数</td>
              <td className="border border-slate-300">
                {planData.academicActivityNum}
              </td>
            </tr>

            <tr>
              <td className="border border-slate-300" rowSpan={2}>YZHZ</td>
              <td className="border border-slate-300">YZHZ储备数</td>
              <td className="border border-slate-300">
                {planData.reserveNum}
              </td>
            </tr>
            <tr>
              <td className="border border-slate-300">新增宝宝数</td>
              <td className="border border-slate-300">
                {planData.newBabyNum}
              </td>
            </tr>

              {/*<tr>*/}
              {/*  <td className="border border-slate-300" colSpan={2}>其他核心管理举措和行动计划</td>*/}
              {/*  <td className="border border-slate-300">*/}
              {/*    {planData.actionPlan}*/}
              {/*  </td>*/}
              {/*</tr>*/}
            </tbody>
          </table>
        </div>
      )
    } else if (tabKey === '2' && (level === 5 || level === 4)) {
      return (
        <div className="table-2">
          <div className="zhou">
            周度业务计划
          </div>
          <table className="border-collapse border border-slate-400 w-full">
            <thead>
            <tr>
              <th className="border border-slate-300 w-2/12"></th>
              <th className="border border-slate-300 w-5/12">分类</th>
              {
                userBoardData.map((item) => {
                  return (
                    <th key={item.deptcode} className="border border-slate-300">{item.deptname}</th>
                  )
                })
              }
            </tr>
            </thead>
            <tbody>
            <tr>
              <td className="border border-slate-300" rowSpan={3}>KH改善</td>
              <td className="border border-slate-300">观念谨慎型</td>
              {deptBoardTrTdRender2('improveConceptNum')}
            </tr>
            <tr>
              <td className="border border-slate-300">合作改善型</td>
              {deptBoardTrTdRender2('improveCooperateNum')}
            </tr>
            <tr>
              <td className="border border-slate-300">长效和KH改善型</td>
              {deptBoardTrTdRender2('improveLongNum')}
            </tr>

            <tr>
              <td className="border border-slate-300" rowSpan={2}>行为</td>
              <td className="border border-slate-300">大区经理拜访</td>
              {deptBoardTrTdRender2('distVisitNum')}
            </tr>
            <tr>
              <td className="border border-slate-300">大区经理协访</td>
              {deptBoardTrTdRender2('distAidVisitNum')}
            </tr>
            {/*<tr>*/}
            {/*  <td className="border border-slate-300" colSpan={2}>其他核心管理举措和行动计划</td>*/}
            {/*  {deptBoardTrTdRender2('actionPlan')}*/}
            {/*</tr>*/}
            </tbody>
          </table>
        </div>
      )
    } else if (tabKey === '1' || tabKey === '2') {
      return (
        <div className="table-2">
          <div className="zhou">
            周度业务计划
          </div>
          <table className="border-collapse border border-slate-400 w-full">
            <thead>
            <tr>
              <th className="border border-slate-300 w-2/12"></th>
              <th className="border border-slate-300 w-5/12">分类</th>
              <th className="border border-slate-300 w-5/12">下周({estDate})</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <td className="border border-slate-300" rowSpan={3}>KH改善</td>
              <td className="border border-slate-300">观念谨慎型</td>
              <td className="border border-slate-300">
                {planData.improveConceptNum}
              </td>
            </tr>
            <tr>
              <td className="border border-slate-300">合作改善型</td>
              <td className="border border-slate-300">
                {planData.improveCooperateNum}
              </td>
            </tr>
            <tr>
              <td className="border border-slate-300">长效和KH改善型</td>
              <td className="border border-slate-300">
                {planData.improveLongNum}
              </td>
            </tr>

            <tr>
              <td className="border border-slate-300" rowSpan={2}>行为</td>
              <td className="border border-slate-300">大区经理拜访</td>
              <td className="border border-slate-300">
                {planData.distVisitNum}
              </td>
            </tr>
            <tr>
              <td className="border border-slate-300">大区经理协访</td>
              <td className="border border-slate-300">
                {planData.distAidVisitNum}
              </td>
            </tr>
            {/*{*/}
            {/*  level === 6 && (*/}
            {/*    <tr>*/}
            {/*      <td className="border border-slate-300" colSpan={2}>其他核心管理举措和行动计划</td>*/}
            {/*      <td className="border border-slate-300">*/}
            {/*        {planData.actionPlan}*/}
            {/*      </td>*/}
            {/*    </tr>*/}
            {/*  )*/}
            {/*}*/}
            </tbody>
          </table>
        </div>
      )
    }
  }

  return (
    <Wrapper>
      <Tabs roleInfo={roleInfo} onChange={tabOnChange} readLevel={readLevel}/>
      {
        tabKey === '1' && (
          <>
            <table className="border-collapse border border-slate-400 w-full">
              <thead>
              <tr>
                <th className="border border-slate-300" rowSpan={2}>机构分类</th>
                <th className="border border-slate-300" rowSpan={2}>有预估新增的机构数</th>
                <th className="border border-slate-300" rowSpan={2}>有预估新增的机构占比</th>
                <th className="border border-slate-300" colSpan={3}>预估数-下周（{estDate}）</th>
                <th className="border border-slate-300" colSpan={3}>活动计划</th>
                <th className="border border-slate-300" colSpan={2}>YZHZ</th>
              </tr>
              <tr>
                <td className="border border-slate-300">新增数</td>
                <td className="border border-slate-300">长效数</td>
                <td className="border border-slate-300">长效占比</td>
                <td className="border border-slate-300">增流目标数</td>
                <td className="border border-slate-300">帮扶/义诊目标数</td>
                <td className="border border-slate-300">学术目标数</td>
                <td className="border border-slate-300">储备数</td>
                <td className="border border-slate-300">新增宝宝数</td>
              </tr>
              </thead>
              <tbody>
              {
                hospitalBoardData.map(item => {
                  return (
                    <tr key={item.name}>
                      <td className="border border-slate-300">{item.name}</td>
                      <td className="border border-slate-300">{item.newPatHos}</td>
                      <td className="border border-slate-300">{Number(item.newPatHosRate * 100).toFixed(2)}%</td>
                      <td className="border border-slate-300">{item.newPat}</td>
                      <td className="border border-slate-300">{item.longPat}</td>
                      <td className="border border-slate-300">{Number(item.longRate * 100).toFixed(2)}%</td>
                      <td className="border border-slate-300">{item.flowActivityNum}</td>
                      <td className="border border-slate-300">{item.helpActivityNum}</td>
                      <td className="border border-slate-300">{item.academicActivityNum}</td>
                      <td className="border border-slate-300">{item.reserveNum}</td>
                      <td className="border border-slate-300">{item.newBabyNum}</td>
                    </tr>
                  )
                })
              }
              </tbody>
            </table>
            {planTable()}
          </>
        )
      }

      {
        // 人员视图
        tabKey === '2' && (
          <>
            <table className="border-collapse border border-slate-400 w-full">
              <thead>
              <tr>
                <th className="border border-slate-300" rowSpan={2}>人员分类</th>
                <th className="border border-slate-300" rowSpan={2}>有预估新增的机构数</th>
                <th className="border border-slate-300" rowSpan={2}>有预估新增的机构占比</th>
                <th className="border border-slate-300" colSpan={3}>预估数-下周（{estDate}）</th>
                <th className="border border-slate-300" colSpan={3}>活动计划</th>
                <th className="border border-slate-300" colSpan={2}>YZHZ</th>
              </tr>
              <tr>
                <td className="border border-slate-300">新增数</td>
                <td className="border border-slate-300">长效数</td>
                <td className="border border-slate-300">长效占比</td>
                <td className="border border-slate-300">增流目标数</td>
                <td className="border border-slate-300">帮扶/义诊目标数</td>
                <td className="border border-slate-300">学术目标数</td>
                <td className="border border-slate-300">储备数</td>
                <td className="border border-slate-300">新增宝宝数</td>
              </tr>
              </thead>
              <tbody>
              {
                userBoardData.map(item => {
                  return (
                    <tr key={item.deptcode}>
                      <td className="border border-slate-300">
                        {/*<a href="javascript:;" onClick={() => navigate()}>
													{item.deptname}
												</a>*/}
                        {
                          (item.deptname === '合计') ? item.deptname :
                            <a onClick={() => {
                              // 大区 看地区tba的情况 需要大区经理进去上报 区总不用进行上报
                              if ((!item.empNo && level === 5) || (!item.empNo && readLevel === 5) || (!item.empNo && readLevel === 4 && level === 4 && empNoQuery === '')) {
                                // const res = await http1.post('/account/api/public/code2tokenNoSign/dept', {
                                //   empNo: empNo,
                                //   deptcode: item.deptcode
                                // }) || {};
                                // window.location.href = `/pediatricsWeekEstimate/board?&token=${res.token}`;
                                window.location.href = `/pediatricsWeekEstimate/board?&tbaDeptcode=${item.deptcode}`;
                              } else {
                                window.location.href = `/pediatricsWeekEstimate/board?&deptcode=${item.deptcode}&readLevel=${readLevelQuery ? readLevelQuery + 1 : level}&deptname=${item.deptname}&empNo=${item.empNo || ''}&empName=${item.empName}`;
                              }

                            }} style={{color: '#2551F2'}}>
                              {!item.empNo ? 'TBA-' : ''}{item.deptname}
                            </a>
                        }
                      </td>
                      <td className="border border-slate-300">{item.newPatHos}</td>
                      <td className="border border-slate-300">{Number(item.newPatHosRate * 100).toFixed(2)}%</td>
                      <td className="border border-slate-300">{item.newPat}</td>
                      <td className="border border-slate-300">{item.longPat}</td>
                      <td className="border border-slate-300">{Number(item.longRate * 100).toFixed(2)}%</td>
                      <td className="border border-slate-300">{item.flowActivityNum}</td>
                      <td className="border border-slate-300">{item.helpActivityNum}</td>
                      <td className="border border-slate-300">{item.academicActivityNum}</td>
                      <td className="border border-slate-300">{item.reserveNum}</td>
                      <td className="border border-slate-300">{item.newBabyNum}</td>
                    </tr>
                  )
                })
              }
              </tbody>
            </table>
            {planTable()}
          </>
        )
      }

      {
        // 地区 科室 看板
        tabKey === '3' && level === 6 && (
          <>
            <div className="dept">科室视图</div>
            <table className="border-collapse border border-slate-400 w-full">
              <thead>
              <tr>
                <th className="border border-slate-300 w-2/12">科室</th>
                <th className="border border-slate-300 w-5/12">分类</th>
                <th className="border border-slate-300 w-5/12">下周({estDate})</th>
              </tr>
              </thead>
              <tbody>
              {/* 合计 */}
              <tr>
                <td className="border border-slate-300" rowSpan={3}>合计</td>
                <td className="border border-slate-300">新增数</td>
                <td className="border border-slate-300">{total(deptBoardData).newPat}</td>
              </tr>
              <tr>
                {/*<td></td>*/}
                <td className="border border-slate-300">长效数</td>
                <td className="border border-slate-300">{total(deptBoardData).longPat}</td>
              </tr>
              <tr>
                {/*<td></td>*/}
                <td className="border border-slate-300">长效占比</td>
                <td className="border border-slate-300">{total(deptBoardData).longRate}</td>
              </tr>

              {
                deptBoardData.map(item => {
                  return (
                    <React.Fragment key={item.code}>
                      <tr>
                        <td className="border border-slate-300" rowSpan={2}>{item.code}</td>
                        <td className="border border-slate-300">新增数</td>
                        <td className="border border-slate-300">{item.newPat}</td>
                      </tr>
                      <tr>
                        {/*<td></td>*/}
                        <td className="border border-slate-300">长效数</td>
                        <td className="border border-slate-300">{item.longPat}</td>
                      </tr>
                    </React.Fragment>
                  )
                })
              }
              </tbody>
            </table>
            {planTable()}
          </>
        )
      }

      {
        // 大区 科室 看板
        tabKey === '3' && (level === 5 || level === 4) && (
          <>
            <div className="dept">科室视图</div>
            <table className="border-collapse border border-slate-400 w-full">
              <thead>
              <tr>
                <th className="border border-slate-300">科室</th>
                <th className="border border-slate-300">分类</th>
                {
                  deptBoardTr.map((item) => {
                    return (
                      <th key={item.deptcode} className="border border-slate-300">{item.deptname}</th>
                    )
                  })
                }
              </tr>
              </thead>
              <tbody>
              {/* 合计 */}
              {/*<tr>*/}
              {/*	<td className="border border-slate-300" rowSpan={3}>合计</td>*/}
              {/*	<td className="border border-slate-300">新增数</td>*/}
              {/*	{*/}
              {/*		deptBoardTr.map((item, index) => {*/}
              {/*			return (*/}
              {/*				<td key={index + item.deptcode + 'newPat'}  className="border border-slate-300">{index}</td>*/}
              {/*			)*/}
              {/*		})*/}
              {/*	}*/}
              {/*</tr>*/}
              {/*<tr>*/}
              {/*	/!*<td></td>*!/*/}
              {/*	<td className="border border-slate-300">长效数</td>*/}
              {/*	{*/}
              {/*		deptBoardTr.map((item, index) => {*/}
              {/*			return (*/}
              {/*				<td key={index + item.deptcode + 'longPat'} className="border border-slate-300">{index}</td>*/}
              {/*			)*/}
              {/*		})*/}
              {/*	}*/}
              {/*</tr>*/}
              {/*<tr>*/}
              {/*	/!*<td></td>*!/*/}
              {/*	<td className="border border-slate-300">长效占比</td>*/}
              {/*	{*/}
              {/*		deptBoardTr.map((item, index) => {*/}
              {/*			return (*/}
              {/*				<td key={index + item.deptcode + 'longRate'} className="border border-slate-300">{index}</td>*/}
              {/*			)*/}
              {/*		})*/}
              {/*	}*/}
              {/*</tr>*/}

              {
                deptBoardData.map((item, rowIndex) => {
                  return (
                    <React.Fragment key={item.code}>
                      <tr>
                        <td className="border border-slate-300" rowSpan={2}>{item.code}</td>
                        <td className="border border-slate-300">新增数</td>
                        {
                          deptBoardTr.map((trItem, colIndex) => {
                            return (
                              <td key={colIndex + trItem.deptcode + 'newPat'} className="border border-slate-300">
                                {item.extra[colIndex].boardVos[rowIndex].newPat}
                              </td>
                            )
                          })
                        }
                      </tr>
                      <tr>
                        {/*<td></td>*/}
                        <td className="border border-slate-300">长效数</td>
                        {
                          deptBoardTr.map((trItem, colIndex) => {
                            return (
                              <td key={colIndex + trItem.deptcode + 'longPat'} className="border border-slate-300">
                                {item.extra[colIndex].boardVos[rowIndex].longPat}
                              </td>
                            )
                          })
                        }
                      </tr>
                    </React.Fragment>
                  )
                })
              }
              </tbody>
            </table>
            {planTable()}
          </>
        )
      }
    </Wrapper>
  )
}

export default Board;
