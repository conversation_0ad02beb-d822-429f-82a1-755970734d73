.dataQuery {
    height: 100%;
}

.dataQuery .top {
    height: 38px;
    background: var(--gensci-second);
    color: white;
    font-size: 16px;
    display: flex;
    align-items: center;
    // padding: 0 8px;
    box-shadow: 0 2px 2px #d2d2d2;
}

.dataQuery .content {
    height: calc(100% - 25px);
}

.dataQuery .tabContainer {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.dataQuery .tabContainerTop {
    flex: none;
}

.dataQuery .tabContainerContent {
    flex: 1;
    overflow-y: auto;
}

.dataQuery .timeArea {
    width: 150%;
    border: 2px solid red;
}

.dataQuery .showText {
    color: #ccc;
    width: 60px;
    margin: 200px auto;
}