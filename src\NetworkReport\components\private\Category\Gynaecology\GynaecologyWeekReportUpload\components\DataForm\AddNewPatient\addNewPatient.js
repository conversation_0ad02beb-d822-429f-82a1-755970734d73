import React, {useState, forwardRef, useImperative<PERSON>andle, useEffect} from 'react';
import addNewPatient from './addNewPatient.less'

import {Form, Input, Picker, Popup, Button, SearchBar, PickerView} from "antd-mobile";

import { http1} from '../../../../../../../../utils/network'

const AddNewPatient = forwardRef(({hospitalId, newPatientVisible, addPatientRef, newPatientInfo, setNewPatientInfo, code3Info, setCode3Info}) => {

    const [form] = Form.useForm()

    useImperativeHandle((addPatientRef), () => {
        return {
            childHandleAdd,
            handleValidate,
            resetData
        }
    })

    const childHandleAdd = () => { 
        (async () => {
            await form.validateFields()
        })()
        let values = form.getFieldsValue()
        return values
    }

    const handleValidate = async () => { 
        await form.validateFields()
    }

    const resetData = () => { 
        form.resetFields()
    }

    const [code3List, setCode3List] = useState([])
    const [productList, setProductList] = useState([])
    const signList = [
        [
            { label: '是', value: true },
            { label: '否', value: false }
        ]
    ]

    useEffect(() => {
        http1.post('/doctor/info/query', {hospitalId: hospitalId}).then((res) => { 
            let temp = []
            temp = res.length > 0 && res.map(item => { 
                return { label: item.name, value: item.doctorCode}
            })
            if (temp.length > 0) {
                setCode3List([temp])
            } else { 
                setCode3List([[{ label: '', value: '' }]])
            }
        })
        
    }, [hospitalId, newPatientVisible])
    
    useEffect(() => {
      http1.post('/meta/select', {tag: 'GynecologyProduct'}).then((res) => { 
        let temp = []
        temp = res.length > 0 && res.map(item => { 
            return { label: item.description, value: item.code}
        })
        console.log(temp);
        if (temp.length > 0) {
            setProductList([temp])
        } else { 
            setProductList([[{ label: '', value: '' }]])
        }
    })
    }, [])

    const [code3PopupShow, setCode3PopupShow] = useState(false) 
    const [code3Show, setCode3Show] = useState(true) 

    const [code3Name, setCode3Name] = useState('')

    const handleCode3Name = (val) => {
        setCode3Name(val)
    }

    const onSearchPCode3 = () => {
        http1.post('/doctor/info/query', { hospitalId: hospitalId, name: code3Name, query: {name: "like"}}).then((res) => { 
            let temp = []
            temp = res.length > 0 && res.map(item => { 
                return { label: item.name, value: item.doctorCode}
            })
          if (temp.length > 0) {
            setCode3Show(true)
            setCode3List([temp])
          } else { 
            setCode3Show(false)
            setCode3List([[{ label: '', value: '' }]])
          }
        })
    }

    const handleConfirmCode3 = () => {
        form.setFieldValue('doctorCode', code3Info.doctorCode)
        setCode3PopupShow(false)
    }

    return (
        <div className={addNewPatient.addNewPatient}>
            <Form
                form={form}
            >
                <Form.Item
                        name="doctorCode"
                        label="编码3"
                        trigger="onConfirm"
                        rules={[{ required: true }]}
                        onClick={() => setCode3PopupShow(true)}
                      >
                        {code3Info.doctorName ? (
                          <Input
                            value={code3Info.doctorName}
                          />
                        ) : null}
                        <Popup
                          visible={code3PopupShow}
                          onMaskClick={() => {
                            setCode3PopupShow(false);
                          }}
                          bodyStyle={{
                            borderTopLeftRadius: "8px",
                            borderTopRightRadius: "8px",
                            minHeight: "40vh",
                          }}
                        >
                          <div
                            className={"adm-picker-header"}
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                              height: "42px",
                            }}
                          >
                            <span
                              className={"adm-picker-header-button"}
                              style={{
                                fontSize: "15px",
                                color: "var(--gensci-main)",
                              }}
                              onClick={() => setCode3PopupShow(false)}
                            >
                              取消
                            </span>
                            <span
                              className={"adm-picker-header-button"}
                              style={{
                                fontSize: "15px",
                                color: "var(--gensci-main)",
                              }}
                              onClick={handleConfirmCode3}
                            >
                              确认
                            </span>
                          </div>
                          <div className={addNewPatient.gynaecologyWeekReportUpload__searchContainer}>
                            <SearchBar
                              placeholder="请输入内容"
                              onChange={(e) => handleCode3Name(e)}
                              style={{ marginRight: "10px", flex: "1" }}
                            />
                            <Button
                              size={"small"}
                              color={"primary"}
                              onClick={() => onSearchPCode3()}
                            >
                              查询
                            </Button>
                          </div>
                          {code3Show ? (
                            <PickerView
                              columns={code3List}
                              onChange={(val, extend) => {
                                setCode3Info({
                                  ...code3Info,
                                  doctorCode: extend.items[0] && extend.items[0].value,
                                  doctorName: extend.items[0] && extend.items[0].label,
                                });
                              }}
                            ></PickerView>
                          ) : (<div style={{width: "60px", margin: "80px auto", color: "#ccc",}}>暂无数据</div>)}
                        </Popup>
                      </Form.Item>
                <Form.Item
                    name='isPrescription'
                    label='是否处方新客户'
                    trigger='onConfirm'
                    rules={[{ required: true }]}
                    onClick={(e, pickerRef) => {
                        pickerRef.current?.open()
                    }}
                >
                    <Picker
                        columns={signList}
                    >
                        {items => {
                            if (items.every(item => item === null)) {
                                return null
                            } else {
                                return items.map(item => item?.label ?? '未选择')
                            }
                        }}
                    </Picker>
                </Form.Item>
                <Form.Item
                    name='drugId'
                    label='产品'
                    trigger='onConfirm'
                    rules={[{ required: true }]}
                    onClick={(e, pickerRef) => {
                        pickerRef.current?.open()
                    }}
                >
                    <Picker
                        columns={productList}
                        onConfirm={(value, extend) => {
                            setNewPatientInfo({...newPatientInfo, drugName: extend.items[0].label})
                        }}
                    >
                        {items => {
                            if (items.every(item => item === null)) {
                                return null
                            } else {
                                return items.map(item => item?.label ?? '未选择')
                            }
                        }}
                    </Picker>
                </Form.Item>
                <Form.Item
                    name='num'
                    label='新患数'
                    rules={[
                    { required: true },
                    {
                      pattern: new RegExp(
                          /^[0-9]*[0-9][0-9]*$/
                      ),
                      message: "新患数只能为整数",
                    },
                  ]}
                >
                    <Input type={'number'} placeholder='请输入'/>
                </Form.Item>
            </Form>

        </div>
    )
})

export default AddNewPatient
