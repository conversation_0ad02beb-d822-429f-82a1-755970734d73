import React, {useEffect, useState, useRef} from 'react';
import styled from 'styled-components';
import {Button, Toast, Dialog} from "antd-mobile";
import {RightOutline, LeftOutline, CheckCircleFill, DownOutline, UpOutline, Rol} from 'antd-mobile-icons'
import {useNavigate, useSearchParams} from "react-router-dom";
import {http1} from "../../../utils/network";
import empty from './empty.png';
import ApprovalBar from "./ApprovalBar";

const Wrapper = styled.div`
  .light {
    color: #2551F2;
  }
  table td {
    padding: 5px 16px;
    text-align: center;
  }
  table thead {
    background-color: #F3F4F5;
  }
  table thead tr th {
    padding: 5px 16px;
    font-size: 14px;
  }

  input:focus-visible, textarea:focus-visible {
    border: 1px solid #F3F4F5;
    padding: 2px 6px;/*清除自带的padding间距*/
    outline: none;/*清除input点击之后的黑色边框*/
  }
  input, textarea {
    border: 1px solid #F3F4F5;
    padding: 2px 6px;/*清除自带的padding间距*/
    outline: none;/*清除input点击之后的黑色边框*/
  }


  .hospital-tag {
    color: #F54A45;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px; /* 157.143% */
    border-radius: 2px;
    background: #FEEDEC;
    padding: 2px 8px;
  }
  .hospital-tag-1 {
    color: #FF8800;
    background: #FFFCE8;
  }
  .hospital-tag-2{
    color: #F7BA1E;
    background: #FFFCE8;
  }
  .hospital-tag-3{
    color: #33D1C9;
    background: #E8FFFB;
  }
  .hospital-tag-4{
    color: #722ED1;
    background: #F5E8FF;
  }
  .hospital-tag-5{
    color: #1D212B;
    background: #F3F4F5;
  }
  .title {
    color: var(--color-text-1, #1D2129);
    /* 16/CN-Medium */
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px; /* 150% */
    margin-bottom: 22px;
  }
  .tags-bar {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    .tags-wrapper {
      display: flex;
      div {
        padding: 4px 16px;
        color: #4E595E;
        cursor: pointer;
        height: 30px;
      }
    }
    .est-btns {
      display: flex;
      background-color: #F3F4F5;
      color: #4E595E;
      padding: 4px;
      .est-btn {
        height: 30px;
        line-height: 30px;
        padding: 2px 16px;
        cursor: pointer;
      }
      .est-btn-checked {
        background-color: #fff;
        color: #2551F2;
      }
    }
  }
  .hospital-list-wrapper {
    position: relative;
    height: 72px;
    overflow: hidden;
    .left-icon {
      position: absolute;
      left: 20px;
      top: 50%;
      transform: translateY(-50%);
    }
    .right-icon {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);
    }
    .hospital-list {
      display: flex;
      width: calc(100% - 200px);
      margin: 0 auto;
      flex-wrap: wrap;
      min-height: 60px;
      .item {
        width: calc(20% - 18px);
        padding: 12px 16px;
        flex-direction: column;
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        background: #F5F6F8;
        margin-right: 16px;
        cursor: pointer;
        margin-bottom: 16px;
        &:last-of-type {
          margin-right: 0;
        }
        .top {
          color: #1D212B;
          font-size: 16px;
          font-weight: 500;
          line-height: 24px;
          height: 24px;
        }
        .bot {
          font-size: 14px;
          font-weight: 500;
          line-height: 22px;
          height: 22px;
          color: #96A1AA;
        }
        .done {
          color: #34C724;
        }
      }
      .item-selected {
        border: 1px solid var(--unnamed, #2551F2) !important;
        background: var(--unnamed, #E8F0FF) !important;
        .top {
          color: #2551F2 !important;
        }
        .bot {
          color: #2551F2;
        }
      }
    }
  }
  .hospital-full-wrapper {
    margin: 25px 16px;
    border: 1px solid #E5E6EB;
    .hospital-info-wrapper {
      border-radius: 8px 8px 0 0;
      background: #E8F0FF;
      padding: 16px 24px;
      .name-header {
        margin-bottom: 15px;
        .name {
          font-size: 24px;
          font-style: normal;
          font-weight: 500;
          line-height: 32px;
          height: 32px;
          margin-right: 10px;
          color: #2551F2;
        }
        
      }
      .sub-info {
        display: flex;
        .sub-info-item {
          margin-right: 50px;
          &>div {
            display: inline-block;
          }
          .label {
            color: #96A1AA;
            /* 14/CN-Regular */
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
            height: 22px;
          }
          .value {
            color: #1D212B;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            height: 22px;
          }
        }
      }
    }
    .inner {
      padding: 16px 32px;
      .action-bar {
        display: flex;
        justify-content: space-between;
        margin-bottom: 18px;
      }
    }
    .txt {
      color: var(--unnamed, #2551F2);
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
  }
  
  .footer {
    text-align: right;
    position: fixed;
    bottom: 16px;
    right: 16px;
    button {
      margin-left: 16px;
    }
  }
`;

const tags = [
  {
    key: 'all',
    label: '全部'
  },
  {
    key: 'top',
    label: 'TOP医院'
  },
  {
    key: 'highPotential',
    label: '高潜医院'
  },
  {
    key: 'potential',
    label: '潜力医院'
  },
  {
    key: 'publicHos',
    label: '新增-公立医院'
  },
  {
    key: 'nonPublicHos',
    label: '新增-非公医院'
  },
  {
    key: 'other',
    label: '其他'
  }
]

const tags2 = [
  {
    key: 'top',
    label: 'TOP医院'
  },
  {
    key: 'highPotential',
    label: '高潜医院'
  },
  {
    key: 'potential',
    label: '潜力医院'
  },
  {
    key: 'publicHos',
    label: '新增-公立医院'
  },
  {
    key: 'nonPublicHos',
    label: '新增-非公医院'
  },
  {
    key: 'other',
    label: '其他'
  }
]

const Report = props => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams()

  const deptcode = searchParams.get('deptcode')
  const estDate = searchParams.get('estDate');
  const estDateList = estDate.split(',');

  const empNo = searchParams.get('empNo')
  const hideBack = searchParams.get('hideBack')

  const [loading, setLoading] = useState(false);
  const [currentHospital, setCurrentHospital] = useState({});
  const [hospitalList, setHospitalList] = useState([]);
  const [formValue, setFormValue] = useState([]);
  const [open, setOpen] = useState(false);
  const [currentTag, setCurrentTag] = useState('all');
  const [currentIsEst, setCurrentIsEst] = useState('');
  const handler = useRef();
  const handler2 = useRef();
  const handler3 = useRef();

  useEffect(() => {
    if(deptcode) {
      getHospitalList(deptcode);
    }
  }, [deptcode])

  useEffect(() => {
    if(currentHospital.hospitalid) {
      getForm();
    }
  }, [currentHospital.hospitalid])

  const toggleOpen = () => {
    setOpen(!open)
  }

  const getHospitalList = async (deptcode) => {
    try {
      Toast.show({
        icon: 'loading',
        duration: 0
      })
      const res = await http1.get(`/estimate/query/hospital?deptcode=${deptcode}&estDate=${estDateList[estDateList.length - 1]}`)
      setHospitalList(res || []);
      if(res.length) {
        if(searchParams.get('name')) {
          const _ = tags2.find(item => {
            return searchParams.get('name').includes(item.label)
          });
          console.log(_, '____')
          if(_.key) {
            const _currentHospital = res.filter(d => d[_.key]) && res.filter(d => d[_.key])[0] || {};
            setCurrentHospital(_currentHospital)
            setCurrentTag(_.key)
          }else {
            setCurrentHospital(res[0])
          }
        }else {
          setCurrentHospital(res[0])
        }
      }
      Toast.clear();
    } catch (e) {
      Toast.clear();
    }
  }

  const handleSelectHospital = (hospital) => {
    if(currentHospital.hospitalid) {
      handler3.current = Dialog.show({
        content: '当前填写未保存，确认跳切换机构吗？',
        actions: [
          [
            {
              key: 'cancel',
              text: '取消',
              onClick: () => {
                handler3.current?.close()
              }
            },
            {
              key: 'queren',
              text: '确认',
              onClick: () => {
                handler3.current?.close();
                setCurrentHospital(hospital)
              },
            }
          ],
        ]})
    }else {
      setCurrentHospital(hospital)

    }
  }

  const getForm = async () => {
    try {
      Toast.show({
        icon: 'loading',
        duration: 0
      })
      const res = await http1.post(`/estimate/query/form`, {
        deptcode: deptcode,
        estDateList,
        estDateType: 'month',
        hospitalId: currentHospital.hospitalid,
        empNo
      }) || [];
      // setFormValue(res)
      /*
      * {
        ...res,
        planVo: {
          ...res.planVo,
          actionPlan: res.planVo.actionPlan || ''
        }
      }
      * */
      setFormValue(res.map(item => {
        item.planVo.actionPlan = item.planVo.actionPlan || ''
        return item;
      }))
      Toast.clear();
    } catch (e) {
      Toast.clear();
    }
  }

  const planVoNumberChange = (event, type, estDate) => {
    const inputValue = event.target.value;
    const maxValue = 9999; // 设置最大值
    const minValue = 0; // 设置最小值
    const _formValue = JSON.parse(JSON.stringify(formValue));
    const _index = _formValue.findIndex(item => item.estDate === estDate);

    if (parseInt(inputValue) > maxValue) {
      _formValue[_index].planVo[type] = maxValue.toString()
      setFormValue(_formValue)
    }else if (parseInt(inputValue) < minValue) {
      _formValue[_index].planVo[type] = minValue.toString()
      setFormValue(_formValue)
    } else {
      _formValue[_index].planVo[type] = inputValue
      setFormValue(_formValue)
    }
  }

  const currentTdValue = (item, type, monthIndex, deptIndex) => {
    return formValue[monthIndex].boardVos[deptIndex][type] || 0
  }

  const currentPlanTdValue = (estDate, type) => {
    const _index = formValue.findIndex(item => item.estDate === estDate);

    if(type === 'actionPlan') {
      return formValue[_index] && formValue[_index].planVo[type] || '';
    }

    return formValue[_index] && formValue[_index].planVo[type] || 0;
  }

  // 获取多月 plan 合计
  const getPlanTotal = (type) => {
    let total = 0;
    formValue.forEach(monthItem => {
      const planVo = monthItem.planVo || {};
      total = total + (+planVo[type])
    })

    return total
  }

  const depNumberChange = (event, item, type, monthIndex, deptIndex) => {
    const _formValue = JSON.parse(JSON.stringify(formValue));
    const inputValue = event.target.value;
    const maxValue = 9999; // 设置最大值
    const minValue = 0; // 设置最小值
    const longPat = currentTdValue(item, 'longPat', monthIndex, deptIndex)
    const newPat = currentTdValue(item, 'newPat', monthIndex, deptIndex)

    if (parseInt(inputValue) > maxValue) {
      _formValue[monthIndex].boardVos[deptIndex][type] = maxValue.toString()
      setFormValue(_formValue)
    }else if (parseInt(inputValue) < minValue) {
      _formValue[monthIndex].boardVos[deptIndex][type] = minValue.toString()
      setFormValue(_formValue)
    } else {
      _formValue[monthIndex].boardVos[deptIndex][type] = inputValue;
      setFormValue(_formValue)
    }

    if(type === 'newPat') {
      if(Number(inputValue) < Number(longPat)) {
        Toast.show('长效预估数需 <= 预估数')
        _formValue[monthIndex].boardVos[deptIndex][type] = longPat
        setFormValue(_formValue)
      }
    }else if(type === 'longPat') {
      if(Number(inputValue) > Number(newPat)) {
        Toast.show('长效预估数需 <= 预估数')
        _formValue[monthIndex].boardVos[deptIndex][type] = newPat
        setFormValue(_formValue)
      }
    }
  }

  const actionPlanChange = (event, estDate) => {
    const _formValue = JSON.parse(JSON.stringify(formValue));
    const _index = _formValue.findIndex(item => item.estDate === estDate);

    const inputValue = event.target.value;

    _formValue[_index].planVo.actionPlan = inputValue
    setFormValue(_formValue)
  }

  const handlePage = (type) => {
    const currentIndex = currentTagHospitalList(currentTag).findIndex(d => d.hospitalid === currentHospital.hospitalid);
    if(currentIndex === -1 ) {
      return false
    }
    if(currentIndex === currentTagHospitalList(currentTag).length - 1 || (currentIndex === 0 && type === '<')) {
      return false;
    }
    if(type === '>') {
      setCurrentHospital(currentTagHospitalList(currentTag)[currentIndex + 1])
    }else {
      setCurrentHospital(currentTagHospitalList(currentTag)[currentIndex - 1])
    }
  }

  const handleSave = async () => {
     try {
       // if(!formValue.planVo.actionPlan) {
       //   Toast.show('请填写其他核心管理举措和行动计划！');
       //   return false;
       // }
       setLoading(true);
       await http1.post(`/estimate/save`,
         formValue.map(item => ({
           ...item,
           deptcode: deptcode,
           hospitalid: currentHospital.hospitalid,
           empNo,
           estDateType: 'month'
         }))
       );
       await getHospitalList(deptcode)
       Toast.show('操作成功！')
       setLoading(false);

       const currentIndex = currentTagHospitalList(currentTag).findIndex(d => d.hospitalid === currentHospital.hospitalid);

       if(currentIndex < currentTagHospitalList(currentTag).length - 1) {
         setCurrentHospital(currentTagHospitalList(currentTag)[currentIndex + 1])
       }else {
         navigate(-1)
       }
     } catch (e) {
       setLoading(false);
     }

  }

  const monthTotal = (estDate, type) => {
    const _ = {
      newPat: 0,
      longPat: 0,
      longRate: '0%'
    }
    const data = formValue.find(d => d.estDate === estDate) || {};
    const bordVos = data.boardVos || [];
    bordVos.forEach(item => {
      _.newPat = +(item.newPat || 0) + _.newPat;
      _.longPat = +(item.longPat || 0) + _.longPat;
    });
    if(_.newPat) {
      _.longRate = ((_.longPat / _.newPat) * 100).toFixed(2) + '%';
    }
    return _[type];
  }

  const allTotal = () => {

  }

  const getDeptBoardMonthTotal = (monthIndex) => {
    let newPat = 0;
    let longPat = 0;
    let longRate = 0;
    const monthCol = formValue[monthIndex] && formValue[monthIndex].boardVos || [];

    monthCol.forEach(item => {
      newPat = +item.newPat + newPat;
      longPat = +item.longPat + longPat;
    })
    if(newPat) {
      longRate = ((longPat / newPat) * 100).toFixed(2) + '%';
    }

    return {
      newPat,
      longPat,
      longRate
    }
  }

  // 多月合计的 合计
  const getDeptBoardMonthTotalTotal = () => {
    let newPat = 0;
    let longPat = 0;
    let longRate = 0;
    formValue.forEach(monthItem => {
      monthItem.boardVos.forEach(deptItem => {
        newPat = +deptItem.newPat + newPat;
        longPat = +deptItem.longPat + longPat;
      })
    })
    if(newPat) {
      longRate = ((longPat / newPat) * 100).toFixed(2) + '%';
    }

    return {
      newPat,
      longPat,
      longRate
    }
  }

  // 多月单行的合计
  const getDeptBoardMonthRowTotal = (deptIndex) => {
    let newPat = 0;
    let longPat = 0;
    let longRate = 0;
    formValue.forEach(monthItem => {
      const deptItem = monthItem.boardVos[deptIndex];
      newPat = +deptItem.newPat + newPat;
      longPat = +deptItem.longPat + longPat;
    })
    if(newPat) {
      longRate = ((longPat / newPat) * 100).toFixed(2) + '%';
    }

    return {
      newPat,
      longPat,
      longRate
    }
  }

  const {
    hospitalName,
    province,
    city,
    district,
    level,
    type,
    grade,
    top,
    potential,
    highPotential,
    publicHos,
    nonPublicHos,
    other
  } = currentHospital;

  const currentTagHospitalList = (tag) => {
    if(tag === 'all') {
      if(currentIsEst === '') {
        return hospitalList
      }else {
        return hospitalList.filter(d => d.isEst === currentIsEst)
      }
    }else {
      if(currentIsEst === '') {
        return hospitalList.filter(d => d[tag])
      }else {
        return hospitalList.filter(d => d[tag]).filter(d => d.isEst === currentIsEst)
      }
    }
  }

  const changeTag = (tag) => {
    setCurrentTag(tag.key);
    const _ = currentTagHospitalList(tag.key);
    if(_ && _.length) {
      setCurrentHospital(_[0])
    }else {
      setCurrentHospital({})
    }
  }

  useEffect(() => {
    const _ = currentTagHospitalList(currentTag);
    if(_ && _.length) {
      setCurrentHospital(_[0])
    }else {
      setCurrentHospital({})
    }
  }, [currentIsEst])

  const disabled = searchParams.get('level') !== '6';

  return (
    <Wrapper>
      {
        !hideBack && (
          <div style={{ cursor: 'pointer', marginBottom: 12 }} onClick={() => navigate(-1)}>
            <LeftOutline style={{ display: 'inline-block' }} />返回
          </div>
        )
      }
      <div className="title">
        我的机构列表
      </div>
      <div className="tags-bar">
        <div className="tags-wrapper">
          {
            tags.map(tag => {
              return <div
                key={tag.key}
                onClick={() => changeTag(tag)}
                style={tag.key === currentTag ? {
                  'border-radius': '100px',
                  background: '#E8F0FF',
                  color: '#2551F2',
                  fontWeight: '500'
                } : {

                }}
              >
                {tag.label}
              </div>
            })
          }
        </div>
        <div className="est-btns">
          <div onClick={() => setCurrentIsEst(true)} className={`est-btn ${currentIsEst === true && 'est-btn-checked'}`}>已填写</div>
          <div onClick={() => setCurrentIsEst(false)} className={`est-btn ${currentIsEst === false && 'est-btn-checked'}`}>未填写</div>
        </div>
      </div>
      {
        !!currentTagHospitalList(currentTag).length ? (
          <>
            <div
              style={
                open ?
                  {
                    height: 'auto'
                  } :
                  {}
              }
              className="hospital-list-wrapper"
            >
              <Button className={'right-icon'} onClick={() => {
                handler.current = Dialog.show({
                  content: '当前填写未保存，确认跳转下一个机构吗？',
                  actions: [
                    [
                      {
                        key: 'cancel',
                        text: '取消',
                        onClick: () => {
                          handler.current?.close()
                        }
                      },
                      {
                        key: 'queren',
                        text: '确认',
                        onClick: () => {
                          handler.current?.close();
                          handlePage('>')
                        },
                      }
                    ],
                  ]})
              }}>
                <RightOutline />
              </Button>
              <Button className={'left-icon'} onClick={() => {
                handler2.current = Dialog.show({
                  content: '当前填写未保存，确认跳转上一个机构吗？',
                  actions: [
                    [
                      {
                        key: 'cancel',
                        text: '取消',
                        onClick: () => {
                          handler2.current?.close()
                        }
                      },
                      {
                        key: 'queren',
                        text: '确认',
                        onClick: () => {
                          handler2.current?.close();
                          handlePage('<')
                        },
                      }
                    ],
                  ]})
              }}>
                <LeftOutline />
              </Button>
              <div className="hospital-list">
                {
                  currentTagHospitalList(currentTag).map(item => {
                    return (
                      <div
                        key={item.hospitalid}
                        className={`item truncate ${item.hospitalid === currentHospital.hospitalid && 'item-selected'}`}
                        onClick={() => handleSelectHospital(item)}
                        style={item.isEst ? {
                          border: '1px solid #CCF1C8',
                          background: '#EBF9E9',
                          position: "relative"
                        } : {
                          position: "relative"
                        }}
                        title={item.hospitalName}
                      >
                        <div className="top">{item.hospitalName}</div>

                        {
                          item.hospitalid === currentHospital.hospitalid ? <div className={'bot'}>当前</div> : (
                            item.isEst ? <div className="bot done"><CheckCircleFill style={{ color: '#34C724', display: 'inline-block', marginRight: 8 }} />预估完成</div>
                              :
                              <div className="bot">未填写</div>
                          )
                        }
                        {
                          item.top && <span className={'hospital-tag'} style={{ position: 'absolute', top: 0, right: 0 }}>Top医院</span>
                        }
                        {
                          item.potential && <span className={'hospital-tag hospital-tag-1'} style={{ position: 'absolute', top: 0, right: 0 }}>潜力医院</span>
                        }
                        {
                          item.highPotential && <span className={'hospital-tag hospital-tag-2'} style={{ position: 'absolute', top: 0, right: 0 }}>高潜医院</span>
                        }
                        {
                          item.publicHos && <span className={'hospital-tag hospital-tag-3'} style={{ position: 'absolute', top: 0, right: 0 }}>公立医院</span>
                        }
                        {
                          item.nonPublicHos && <span className={'hospital-tag hospital-tag-4'} style={{ position: 'absolute', top: 0, right: 0 }}>非公医院</span>
                        }
                        {
                          item.other && <span className={'hospital-tag hospital-tag-5'} style={{ position: 'absolute', top: 0, right: 0 }}>其他</span>
                        }
                      </div>
                    )
                  })
                }
              </div>
            </div>
            {
              currentTagHospitalList(currentTag).length > 5 && (
                <div style={{
                  textAlign: 'center',
                  padding: '12px 0',
                  cursor: 'pointer'
                }}
                     onClick={toggleOpen}
                >
                  {
                    open ? <UpOutline style={{ color: '#2551F2', display: 'inline-block' }} /> : <DownOutline style={{ color: '#2551F2', display: 'inline-block' }} />
                  }
                  {
                    open ? <span style={{ color: '#2551F2' }}>收起</span> : <span style={{ color: '#2551F2' }}>展开</span>
                  }
                </div>
              )
            }
          </>
        ) : <div style={{ fontSize: '20px', fontWeight: 'bolder' }}>暂无数据</div>
      }

      {
        // 选中后再渲染下面的内容
        currentHospital.hospitalid && (
          <>
            <div className="hospital-full-wrapper">
              <div className="hospital-info-wrapper">
                <div className="name-header truncate">
                  <span className="name">{hospitalName}</span>
                  {
                    top && <span className={'hospital-tag'}>Top医院</span>
                  }
                  {
                    potential && <span className={'hospital-tag hospital-tag-1'}>潜力医院</span>
                  }
                  {
                    highPotential && <span className={'hospital-tag hospital-tag-2'}>高潜医院</span>
                  }
                  {
                    publicHos && <span className={'hospital-tag hospital-tag-3'}>公立医院</span>
                  }
                  {
                    nonPublicHos && <span className={'hospital-tag hospital-tag-4'}>非公医院</span>
                  }
                  {
                    other && <span className={'hospital-tag hospital-tag-5'}>其他</span>
                  }
                </div>
                <div className="sub-info">
                  <div className="sub-info-item">
                    <div className="label">
                      省/市/区县:
                    </div>
                    <div className="value">
                      {province}/{city}/{district}
                    </div>
                  </div>
                  <div className="sub-info-item">
                    <div className="label">
                      医院级别:
                    </div>
                    <div className="value">
                      {level || '--'}
                    </div>
                  </div>
                  <div className="sub-info-item">
                    <div className="label">
                      医院等次:
                    </div>
                    <div className="value">
                      {grade || '--'}
                    </div>
                  </div>
                  <div className="sub-info-item">
                    <div className="label">
                      经济类型:
                    </div>
                    <div className="value">
                      {type || '--'}
                    </div>
                  </div>
                </div>
              </div>
              <div className="inner">
                <div className="action-bar">
                  <div className="txt">新增预估</div>
                  {/*<Button color="primary" fill='outline'>编辑</Button>*/}
                  <div></div>
                </div>

                <div className="table-1">
                  <table className="border-collapse border border-slate-400 w-full">
                    <thead>
                    <tr>
                      <th className="border border-slate-300 w-2/12">科室</th>
                      <th className="border border-slate-300 w-2/12">分类</th>
                      {
                        estDateList.map(item => {
                          return (
                            <th key={item} className="border border-slate-300 w-2/12">{item}</th>
                          )
                        })
                      }
                      <th className="border border-slate-300 w-2/12 light">合计</th>
                    </tr>
                    </thead>
                    <tbody>
                    {/* 合计*/}
                    <tr>
                      <td className="border border-slate-300" rowSpan={3}>合计</td>
                      <td className="border border-slate-300 light">预估数</td>
                      {
                        estDateList.map((item, monthIndex) => {
                          return (
                            <td key={item} className="border border-slate-300 light">{getDeptBoardMonthTotal(monthIndex).newPat}</td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">{getDeptBoardMonthTotalTotal().newPat}</td>
                    </tr>
                    <tr>
                      {/*<td></td>*/}
                      <td className="border border-slate-300 light">长效预估数</td>
                      {
                        estDateList.map((item,monthIndex) => {
                          return (
                            <td key={item} className="border border-slate-300 light">{getDeptBoardMonthTotal(monthIndex).longPat}</td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">{getDeptBoardMonthTotalTotal().longPat}</td>
                    </tr>
                    <tr>
                      {/*<td></td>*/}
                      <td className="border border-slate-300 light">长效占比</td>
                      {
                        estDateList.map((item, monthIndex) => {
                          return (
                            <td key={item} className="border border-slate-300 light">{getDeptBoardMonthTotal(monthIndex).longRate}</td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">{getDeptBoardMonthTotalTotal().longRate}</td>
                    </tr>
                    {
                      formValue[0] &&　formValue[0].boardVos && formValue[0].boardVos.map((item, deptIndex) => {
                        return (
                          <React.Fragment key={item.code + deptIndex}>
                            {/* 一组 科室数据 */}
                            <tr>
                              <td className="border border-slate-300" rowSpan={2}>{item.code}</td>
                                <td className="border border-slate-300">预估数</td>
                              {
                                estDateList.map((monthItem, monthIndex) => {
                                  return (
                                    <td key={monthItem + monthIndex} className="border border-slate-300">
                                      <input
                                        disabled={disabled}
                                        type="number"
                                        max={9999}
                                        min={0}
                                        value={currentTdValue(item, 'newPat', monthIndex, deptIndex)}
                                        onChange={
                                          e => depNumberChange(
                                            e,
                                            item,
                                            'newPat',
                                            monthIndex,
                                            deptIndex
                                          )
                                        }
                                      />
                                    </td>
                                  )
                                })
                              }
                              <td className="border border-slate-300 light">{getDeptBoardMonthRowTotal(deptIndex).newPat}</td>
                            </tr>
                            <tr>
                              {/*<td></td>*/}
                              <td className="border border-slate-300">长效预估数</td>
                              {
                                estDateList.map((monthItem, monthIndex) => {
                                  return (
                                    <td key={monthItem + monthIndex} className="border border-slate-300">
                                      <input
                                        disabled={disabled}
                                        type="number"
                                        max={9999}
                                        min={0}
                                        value={currentTdValue(item, 'longPat', monthIndex, deptIndex)}
                                        onChange={
                                          e => depNumberChange(
                                            e,
                                            item,
                                            'longPat',
                                            monthIndex,
                                            deptIndex
                                          )
                                        }
                                      />
                                    </td>
                                  )
                                })
                              }
                              <td className="border border-slate-300 light">{getDeptBoardMonthRowTotal(deptIndex).longPat}</td>
                            </tr>
                          </React.Fragment>
                        )
                      })
                    }
                    </tbody>
                  </table>
                </div>

                <div className="table-2">
                  <div className="txt" style={{ margin: '18px 0' }}>
                    业务计划
                  </div>
                  <table className="border-collapse border border-slate-400 w-full">
                    <thead>
                    <tr>
                      <th className="border border-slate-300 w-2/12" colSpan={2}></th>
                      <th className="border border-slate-300 w-2/12">分类</th>
                      {
                        estDateList.map(item => {
                          return (
                            <th key={item} className="border border-slate-300 w-2/12">{item}</th>
                          )
                        })
                      }
                      <th className="border border-slate-300 w-2/12 light">合计</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                      <td className="border border-slate-300" rowSpan={7}>活动计划</td>
                      <td className="border border-slate-300" rowSpan={3}>宣传类</td>
                      <td className="border border-slate-300">增流活动</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <input disabled={disabled} type="number" max={9999} min={0} value={currentPlanTdValue(item, 'flowActivityNum')} onChange={e => planVoNumberChange(e, 'flowActivityNum', item)}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">
                        {getPlanTotal('flowActivityNum')}
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">帮扶活动</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <input disabled={disabled} type="number" max={9999} min={0} value={currentPlanTdValue(item, 'helpActivityNum')} onChange={e => planVoNumberChange(e, 'helpActivityNum', item)}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">
                        {getPlanTotal('helpActivityNum')}
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">品牌活动</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <input disabled={disabled} type="number" max={9999} min={0} value={currentPlanTdValue(item, 'brandActivityNum')} onChange={e => planVoNumberChange(e, 'brandActivityNum', item)}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">
                        {getPlanTotal('brandActivityNum')}
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300" rowSpan={4}>学术类</td>
                      <td className="border border-slate-300">科室会</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <input disabled={disabled} type="number" max={9999} min={0} value={currentPlanTdValue(item, 'deptMeetingNum')} onChange={e => planVoNumberChange(e, 'deptMeetingNum', item)}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">
                        {getPlanTotal('deptMeetingNum')}
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">品牌会议</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <input disabled={disabled} type="number" max={9999} min={0} value={currentPlanTdValue(item, 'regionMeetingNum')} onChange={e => planVoNumberChange(e, 'regionMeetingNum', item)}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">
                        {getPlanTotal('regionMeetingNum')}
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">第三方会议</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <input disabled={disabled} type="number" max={9999} min={0} value={currentPlanTdValue(item, 'otherMeetingNum')} onChange={e => planVoNumberChange(e, 'otherMeetingNum', item)}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">
                        {getPlanTotal('otherMeetingNum')}
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">KA活动</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <input disabled={disabled} type="number" max={9999} min={0} value={currentPlanTdValue(item, 'kaActivityNum')} onChange={e => planVoNumberChange(e, 'kaActivityNum', item)}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">
                        {getPlanTotal('kaActivityNum')}
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300" rowSpan={2} colSpan={2}>用户引流</td>
                      <td className="border border-slate-300">新增宝宝数</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <input disabled={disabled} type="number" max={9999} min={0} value={currentPlanTdValue(item, 'newBabyNum')} onChange={e => planVoNumberChange(e, 'newBabyNum', item)}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">
                        {getPlanTotal('newBabyNum')}
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">有效覆盖数(YZ)</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <input disabled={disabled} type="number" max={9999} min={0} value={currentPlanTdValue(item, 'reserveNum')} onChange={e => planVoNumberChange(e, 'reserveNum', item)}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">
                        {getPlanTotal('reserveNum')}
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300" rowSpan={3} colSpan={2}>HCP改善</td>
                      <td className="border border-slate-300">观念谨慎型</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <input disabled={disabled} type="number" max={9999} min={0} value={currentPlanTdValue(item, 'improveConceptNum')} onChange={e => planVoNumberChange(e, 'improveConceptNum', item)}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">
                        {getPlanTotal('improveConceptNum')}
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">合作改善型</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <input disabled={disabled} type="number" max={9999} min={0} value={currentPlanTdValue(item, 'improveCooperateNum')} onChange={e => planVoNumberChange(e, 'improveCooperateNum', item)}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">
                        {getPlanTotal('improveCooperateNum')}
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">长效和KH改善型</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <input disabled={disabled} type="number" max={9999} min={0} value={currentPlanTdValue(item, 'improveLongNum')} onChange={e => planVoNumberChange(e, 'improveLongNum', item)}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">
                        {getPlanTotal('improveLongNum')}
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300" rowSpan={2} colSpan={2}>行为</td>
                      <td className="border border-slate-300">大区经理拜访</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <input disabled={disabled} type="number" max={9999} min={0} value={currentPlanTdValue(item, 'distVisitNum')} onChange={e => planVoNumberChange(e, 'distVisitNum', item)}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">
                        {getPlanTotal('distVisitNum')}
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">大区经理协访</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <input disabled={disabled} type="number" max={9999} min={0} value={currentPlanTdValue(item, 'distAidVisitNum')} onChange={e => planVoNumberChange(e, 'distAidVisitNum', item)}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">
                        {getPlanTotal('distAidVisitNum')}
                      </td>
                    </tr>

                    <tr>

                    </tr>

                    <tr>
                      <td className="border border-slate-300" colSpan={3}>其他核心管理举措和行动计划</td>
                      {
                        estDateList.map(item => {
                          return (
                            <td key={item} className="border border-slate-300">
                              <textarea disabled={disabled} maxLength={500} value={currentPlanTdValue(item, 'actionPlan')} onChange={e => actionPlanChange(e, item)} style={{ width: '100%' }}/>
                            </td>
                          )
                        })
                      }
                      <td className="border border-slate-300">
                        /
                      </td>
                    </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <div style={{ height: '100px' }} />
            {
              searchParams.get('level') === '6' && (
                <div className="footer">
                  <Button onClick={() => navigate(-1) }>返回合计</Button>
                  <Button color="primary" fill="solid" onClick={handleSave} loading={loading}>
                    {
                      currentTagHospitalList(currentTag).findIndex(item => item.hospitalid === currentHospital.hospitalid) === currentTagHospitalList(currentTag).length - 1 ?
                        '保存' :
                        '保存上报并填写下一个'
                    }
                  </Button>
                </div>
              )
            }

          </>
        )
      }
    </Wrapper>
  )
}

export default Report;
