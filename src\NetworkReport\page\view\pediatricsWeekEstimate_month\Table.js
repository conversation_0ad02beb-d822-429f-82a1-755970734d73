import React from 'react';
import styled from 'styled-components';
import {useNavigate, useSearchParams} from "react-router-dom";
import dayjs from "dayjs";
import {SafeArea} from "antd-mobile";
import {http1} from "../../../utils/network";

const Wrapper = styled.div`
	color: rgb(29, 33, 41);
	.high-light {
		background: rgb(232, 243, 255) !important;
	}
	.under_line-text {
    color: rgb(22, 93, 255);
    text-decoration-line: underline;
	}
	.header {
		display: flex;
    .cell {
      height: 72px;
			width: 25%;
			text-align: center;
			border: 0.5px solid rgb(229, 230, 235);
      align-items: center;
	    background: rgb(247, 248, 250);
      color: rgb(134, 144, 156);
      font-size: 12px;
      font-weight: 400;
	    .cell-title {
        .inner {
          line-height: 72px;
        }
	    }
	    .sub-cell-title {
        line-height: 34px;
      }
	    .sub-row {
	    }
			.sub-row{
				display: flex;
				line-height: normal;
        .sub-cell {
          line-height: 36px;
	        width: 50%;
          border: 0.5px solid rgb(229, 230, 235);
        }
			}
		}
	}
	.body {
		.tba-row {
			padding: 4px 10px;
			background: rgb(235 235 235);
		}
		.b-row {
			height: 72px;
			display: flex;
      .b-cell-1 {
	      //line-height: 100px;
	      display: flex;
        justify-content: center;
        align-items: center;
        height: 72px;
	      .inner {
		      line-height: 22px;
	      }
      }
			.b-row-right {
				display: flex;
				width: 50%;
			}
			.b-cell,.b-cell-1 {
				width: 25%;
				text-align: center;
				border: 0.5px solid rgb(229, 230, 235);
        .b-cell-row {
          height: 36px;
          line-height: 36px;
          border-top: 0.5px solid rgb(229, 230, 235);
        }
        .b-cell-row:first-child {
	        border-top: none;
        }
			}
		}
	}
`;

// eslint-disable-next-line react/prop-types
const Table = ({ data = [], currentWeek, level }) => {
	const navigate = useNavigate();
	let [searchParams] = useSearchParams()

	const handleToReport = async (hospitalId, identify, deptcode) => {
		if(!hospitalId) return false;
		if(identify && identify.indexOf('TBA') > -1 && level < 6) {
			const res = await http1.post('/account/api/public/code2tokenNoSign/dept', { empNo: hospitalId, deptcode }) || {};
			// navigate(`/pediatricsWeekEstimate/boardTBA?hospitalId=${hospitalId}&currentWeek=${currentWeek}&identify=${identify}&token=${res.token}`)
			window.location.href = `/pediatricsWeekEstimate/boardTBA?hospitalId=${hospitalId}&currentWeek=${currentWeek}&identify=${identify}&token=${res.token}&deptcode=${deptcode || searchParams.get('deptcode') || ''}`
		}else {
			navigate(`/pediatricsWeekEstimate/report?hospitalId=${hospitalId}&currentWeek=${currentWeek}&deptcode=${deptcode || searchParams.get('deptcode') || ''}`)
		}

	}

	const valueRender = v => v.isEst ? v.value : '未预估';

	return (
		<Wrapper>
			<div className="header">
				<div className="cell">
					<div className='cell-title'>
						<div className="inner">
							{level === 6 ? '机构' : '市场类型'}
						</div>
					</div>
				</div>
				<div className="cell">
					<div className="cell-title">
						<div className="inner">
							分类
						</div>
					</div>
				</div>
				<div className="cell high-light">
					<div className='cell-title sub-cell-title'>
						{currentWeek && currentWeek.split('~')[0] && dayjs(currentWeek.split('~')[0].replace(/\./g, '/')).format('MM/DD')}
						~
						{currentWeek && currentWeek.split('~')[1] && dayjs(currentWeek.split('~')[1].replace(/\./g, '/')).format('MM/DD')}
					</div>
					<div className="sub-row">
						<div className="sub-cell">底线</div>
						<div className="sub-cell">增长数</div>
					</div>
				</div>
				<div className="cell">
					<div className='cell-title sub-cell-title'>下周</div>
					<div className="sub-row">
						<div className="sub-cell">底线</div>
						<div className="sub-cell">增长数</div>
					</div>
				</div>
			</div>
			<div className="body">
				{
					data.map((item, index) => {
						return (
							<React.Fragment key={item.hospitalId + `${index}`}>
								{
									item.showGap && (
										<div className={'tba-row'}>TBA代表</div>
									)
								}
								<div key={index} className="b-row" onClick={() => handleToReport(item.hospitalId, item.identify, item.deptcode)}>
									<div className="b-cell-1">
										<div className="inner">
											{item.identify}
										</div>
									</div>
									<div className="b-cell">
										<div className="b-cell-row">
											预估数
										</div>
										<div className="b-cell-row">
											长效预估数
										</div>
									</div>
									<div className="b-row-right">
										<div className="b-cell high-light">
											<div className="b-cell-row under_line-text">
												{valueRender({value: item.minTotalTarget, isEst: item.isEst })}
											</div>
											<div className="b-cell-row under_line-text">
												{valueRender({value: item.minLongTarget, isEst: item.isEst })}
											</div>
										</div>
										<div className="b-cell high-light">
											<div className="b-cell-row under_line-text">
												{valueRender({value: item.estTotalNum, isEst: item.isEst })}
											</div>
											<div className="b-cell-row under_line-text">
												{valueRender({value: item.estLongNum, isEst: item.isEst })}
											</div>
										</div>
										<div className="b-cell">
											<div className="b-cell-row">
												{valueRender({value: item.minTotalTargetNext, isEst: item.isEstNext })}
											</div>
											<div className="b-cell-row">
												{valueRender({value: item.minLongTargetNext, isEst: item.isEstNext })}
											</div>
										</div>
										<div className="b-cell">
											<div className="b-cell-row">
												{valueRender({value: item.estTotalNumNext, isEst: item.isEstNext })}
											</div>
											<div className="b-cell-row">
												{valueRender({value: item.estLongNumNext, isEst: item.isEstNext })}
											</div>
										</div>
									</div>
								</div>
							</React.Fragment>
						)
					})
				}
			</div>
			<SafeArea position={'bottom'} />
			<div style={{ height: 40 }} />
		</Wrapper>
	)
}

export default Table;
