import React, {useEffect, useState, useImperative<PERSON>andle} from 'react';
import styled from 'styled-components';
import {useNavigate, useSearchParams} from "react-router-dom";
import {Button, NumberKeyboard, SafeArea, Space, Toast, Dialog} from 'antd-mobile';
import {http1} from "@utils/network";
import dayjs from "dayjs";
import PopForm from "./PopForm";

const Wrapper = styled.div`
  @keyframes breathe {
    0%   {box-shadow: 0 0 2px rgb(22, 93, 255);}
    50%  {box-shadow: 0 0 4px rgb(22, 93, 255), 0 0 10px rgb(22, 93, 255);}
    100% {box-shadow: 0 0 2px rgb(22, 93, 255);}
  }
	color: rgb(29, 33, 41); 
	font-weight: 400;
	.table-container {
		
	}
	.header {
		display: flex;
    .cell {
			width: 25%;
			text-align: center;
			border: 0.5px solid rgb(229, 230, 235);
      align-items: center;
	    background: rgb(247, 248, 250);
      color: rgb(134, 144, 156);
      font-size: 12px;
      font-weight: 400;
	    border-right: none;
	    .cell-title {
		    //padding-top: 20px;
		    line-height: 36px;
		    height: 36px;
	    }
	    .sub-row {
		    margin-top: 5px;
	    }
			.sub-row{
				display: flex;
				line-height: normal;
        .sub-cell {
          line-height: 30px;
	        width: 50%;
          border: 0.5px solid rgb(229, 230, 235);
          border-right: none;
        }
			}
		}
	}
	.body {
		.b-row {
			display: flex;
      .b-cell-1 {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 72px;
	      width: 12.5%;
        .inner {
          line-height: 22px;
        }
      }
			.b-row-right {
				display: flex;
        width: 12.5%;
      }
			.cel-group {
        width: 12.5%;
        display: flex;
			}
			.b-cell,.b-cell-1 {
        width: 12.5%;
				text-align: center;
				border: 0.5px solid  rgb(229, 230, 235);
        .b-cell-row {
          height: 35px;
          line-height: 35px;
          border-bottom: 0.5px solid rgb(204, 204, 204);
	        .input {
						border: 0.5px solid  rgb(229, 230, 235);		    
		        line-height: 18px;
		        padding: 2px 0;
	        }
        }
        .b-cell-row:last-child {
	        border-bottom: none;
        }
				.b-cell-row-edit {
          .adm-input {
	          border: 1px solid  rgb(229, 230, 235);
            width: calc(100% - 8px);
	          text-align: center;
	          display: inline-block;
	          height: 24px;
	          line-height: 24px;
	          margin-top: 5px;
          }
          .adm-input-editing {
            //animation: breathe 2s infinite ease-in-out;
            border: 2px solid rgb(22, 93, 255);
          }
				}
			}
		}
	}
	.footer {
		text-align: center;
		padding-top: 20px;
		position: fixed;
		width: 100vw;
		bottom: 0;
		left: 0;
    height: 76px;
		border-top: 1px solid rgb(242, 243, 245);;
		.adm-button {
			width: 100px;
		}
	}
  .table-footer {
    display: flex;
    .footer-cell {
      width: 12.5%;
      height: 70px;
	    //line-height: 35px;
      border: 0.5px solid rgb(229, 230, 235);
	    text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .topic {
	  margin-top: 50px;
    color: red;
    padding: 10px;
    line-height: 22px;
  }
`;

// const boardVO = {
// 	estLongNum: 'estLongNum',
// 	estWaterNum: 'estWaterNum',
// 	estPowderNum: 'estPowderNum'
// }

const ReportTable = (props) => {
	const navigate = useNavigate();
	const [data, setData] = useState([]);
	const [preData, setPreData] = useState([]);
	const [visible, setVisible] = useState(false);
	const [currentItem, setCurrentItem] = useState({});
	const [loading, setLoading] = useState(false);
	const [xyLine, setXyLine] = useState([]);
	const [searchParams] = useSearchParams()

	const userInfo = window.localStorage.getItem('__userInfo__') && JSON.parse(window.localStorage.getItem('__userInfo__')) || {};

	// eslint-disable-next-line react/prop-types
	useImperativeHandle(props.refs,() => {
		return {
			handleSave
		}
	})

	useEffect(() => {
		setVisible(false)
	}, [props.hideVisible])

	useEffect(() => {
		// if(!visible) {
		// 	setCurrentItem({});
		// }
	}, [visible])

	useEffect(() => {
		const handleKeyDown = (event) => {
			const allowArr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
			if(event.key === 'Tab' || event.key === 'Enter') {
				onConfirm(currentItem);
			}else if(event.key === 'Backspace'){
				handleDelete(currentItem)
			} else if(allowArr.includes(event.key)) {
				handleChange(event.key, currentItem);
			}else {
				Toast.show('只允许输入数字！')
			}
			// 执行你想要的逻辑
		};
		if(visible) {
			document.addEventListener('keydown', handleKeyDown);
		}else {
			document.removeEventListener('keydown', handleKeyDown);
		}
		return () => {
			document.removeEventListener('keydown', handleKeyDown);
		};
	}, [visible, currentItem]);

	useEffect(() => {
		const xMinLine = ['minTotalTarget', 'estTotalNum'];
		const xEstLine = ['minLongTarget', 'estLongNum'];

		const _arr = [];
		data.forEach(dataItem => {
			props.estDateData.forEach(item => {
				_arr.push(item.estDate + '*' + xMinLine[0] + '*' + dataItem.doctorCode);
				_arr.push(item.estDate + '*' + xMinLine[1] + '*' + dataItem.doctorCode);
			});
			props.estDateData.forEach(item => {
				_arr.push(item.estDate + '*' + xEstLine[0] + '*' + dataItem.doctorCode);
				_arr.push(item.estDate + '*' + xEstLine[1] + '*' + dataItem.doctorCode);
			});
		})
		setXyLine(_arr);
	},[props.estDateData, data])

	useEffect(() => {
		if(props.hospitalId) {
			getData();
		}
	}, [props.hospitalId])

	const handleChange = (value, current) => {
		const { doctorCode, type, estDate } = current;
		// setPreData(_data);
		// setCurrent(value);
		setData(data.map(item => {
			item.boardVO.map(form => {
				if(item.doctorCode === doctorCode) {
					if(form.estDate === estDate) {
						if((type === 'minLongTarget' && Number(form[type] == '0' ? value : (form[type] || '')  + value) > Number(form.minTotalTarget)) || (type === 'estLongNum' && Number(form[type] == '0' ? value : (form[type] || '')  + value) > Number(form.estTotalNum))) {
							Toast.show('长效预估数不可以大于预估数');
						}else {
							let _ = form[type] == '0' ? value : (form[type] || '')  + value;
							if(Number(_) >= 999) {
								_ = '999';
							}
							form[type] = _;
						}
					}
				}
				return form;
			})
			return item
		}));
		//
		// data.forEach(item => {
		// 	item.boardVO.forEach(form => {
		// 		console.log(form, 'form');
		// 		if(Number(form.minLongTarget) > Number(form.minTotalTarget)) {
		// 			showToast = true
		// 		}else if(Number(form.estLongNum) > Number(form.estTotalNum)) {
		// 			showToast = true
		// 		}
		// 	})
		// });
		// if(showToast) {
		// 	Toast.show('长效预估数不可以大于预估数');
		// 	setData(JSON.parse(_preData))
		// }
	}

	const handleDelete = (current) => {
		const { doctorCode, type, estDate } = current;
		setData(data.map(item => {
			item.boardVO.map(form => {
				if(item.doctorCode === doctorCode) {
					if(form.estDate === estDate) {
						form[type] = form[type].toString().slice(0, form[type].toString().length - 1) || 0;
					}
				}
				return form;
			});
			return item;
		}))
	}

	const handlePopSave = (value, estDate) => {
		console.log(value, estDate);
		setData(data.map(item => {
			item.boardVO.map(form => {
				if(form.estDate === estDate) {
					form.coreActions = value.coreActions;
					form.pointOfGrowth = value.pointOfGrowth;
				}
				return form;
			});
			return item;
		}))
		console.log(data, 'data')
	}

	const getData = async () => {
		try {
			Toast.show('加载中...');
			const res = await http1.post('/pediatricsEstimate/query/v2/form',{
				empNo: userInfo.empno,
				estDate: props.estDate,
				hospitalId: props.hospitalId
			}) || {};
			setData(res.map(item => ({...item, boardVO: item.boardVO || [], doctorCode: item.label || item.doctorCode})));

			// if(res && res[0]) {
			// 	setCoreActions(res[0].coreActions);
			// 	setPointOfGrowth(res[0].pointOfGrowth);
			// }
			Toast.clear();
		} catch (e) {
			Toast.show(e.message)
		}
	}

	const refactorData = arr => {
		const _ = [];
		arr.forEach(item => {
			item.boardVO.forEach(form => {
				const _form = {};
				form.doctorCode = item.doctorCode;
				form.hospitalId = item.hospitalId;
				form.identify = item.identify;

				_form.boardVO = form;
				_form.empNo = item.empNo || userInfo.empno;
				_form.estDate = form.estDate;
				_form.label = item.label;
				_form.labelCode = item.labelCode;
				_form.hospitalId = item.hospitalId || searchParams.get('hospitalId');
				_form.doctorCode = item.doctorCode;
				_form.pointOfGrowth = form.pointOfGrowth;
				_form.coreActions = form.coreActions;
				_.push(_form);
			})
		})
		return _;
	}

	const handleSave = async (cb) => {
		try {
			// let shouldCheck = false;
			// data.forEach(item => {
			// 	for(const key in boardVO) {
			// 		if(item.boardVO[key] !== '0' && item.boardVO[key] !== 0) {
			// 			shouldCheck = true;
			// 		}
			// 	}
			// })
			// if((props.level === 4 || props.level === 3) && (!pointOfGrowth || !coreActions) && shouldCheck) {
			// 	Toast.show('请完整填写内容！');
			// 	return false
			// }
			Toast.show({
				content: '保存中...',
				duration: 0
			});
			setLoading(true);
			console.log(data, 'datadata')
			await http1.post('/pediatricsEstimate/save', refactorData(data));
			setLoading(false);
			Toast.show('操作成功！');
			navigate(-1)
			// if(cb) {
			// 	await cb(true);
			// }
			// await props.init();
			// await getData();
		} catch (e) {
			console.log(e);
			setLoading(false);
		}
			finally {
				setLoading(false);
			}
	}

	const cleanUp = () => {
		setData(data.map(item => {
			item.boardVO.map(form => {
				form.minTotalTarget ='0'
				form.minLongTarget ='0'
				form.minWaterTarget ='0'
				form.minPowderTarget ='0'
				form.estTotalNum ='0'
				form.estLongNum ='0'
				form.estWaterNum ='0'
				form.estPowderNum ='0'
				form.pointOfGrowth = null
				form.coreActions = null
				return form;
			})
			return item
		}))
	}

	const sleep = (interval) => {
		return new Promise(resolve => {
			setTimeout(resolve, interval)
		})
	}

	const onConfirm = async (currentItem) => {
		const index = xyLine.findIndex(value => value === currentItem.xy);
		if(index === xyLine.length - 1) {
			setCurrentItem({});
			setVisible(false);
			return false;
		}
		const next = xyLine[index + 1];
		console.log(next, 'next');
		const [estDate, type, doctorCode] = next.split('*');
		const currentDataItem = data.find(_ => _.doctorCode === doctorCode) || {};
		const nextItem = currentDataItem.boardVO && currentDataItem.boardVO.find(item => item.estDate === estDate);
		console.log(nextItem, 'nextItem')
		const _item = {
			doctorCode,
			type,
			estDate,
			xy: estDate + '*' + type + '*' + doctorCode
		};
		await sleep(100)
		setCurrentItem(_item);
		await sleep(100)
		setVisible(true);
	}

	const total = (arr, type, estDate) => {
		let minTotal, estTotal = 0;
		const _item = (estDate, boardVO) => boardVO.find(d => d.estDate === estDate) || {};
		minTotal = arr.reduce((sum, item) => sum + Number(_item(estDate, item.boardVO)[type]), 0);
		estTotal = arr.reduce((sum, item) => sum + Number(_item(estDate, item.boardVO)[type]), 0);

		return {
			minTotal,
			estTotal
		}
	}

	const isCurrentItem = (currentItem, item, dateItem, type) => {
		return (currentItem.doctorCode === item.doctorCode && currentItem.type === type && currentItem.estDate === dateItem.estDate);
	}

	const isCurrentXYItem = (currentItem, currentXY) => {
		console.log(currentItem, currentXY)
		const { xy } = currentItem || {};
		return xy === currentXY
	}

	const valueRender = ({item, estItem, key}) => {
		const boardVO = item.boardVO || [];
		const _current = boardVO.find(_ => _.estDate === estItem.estDate) || {};
		return _current[key]
	};

	const hasValue = v => v !== "" && v !== undefined && v !== null;

	const coreActionsRender = (estDate) => {
		const _current = data[0] && data[0].boardVO && data[0].boardVO.find(item => item.estDate === estDate) || {}
		return {
			hasValue: (hasValue(_current.coreActions) && hasValue(_current.pointOfGrowth)),
			coreActions: _current.coreActions,
			pointOfGrowth: _current.pointOfGrowth
		}
	}

	return (
		<Wrapper>
			<div className="table-container">
				<div className="header">
					<div className="cell">
						<div className='cell-title'>
							{props.level === 6 ? 'HCP' : '客户类型'}
						</div>
					</div>
					<div className="cell">
						<div className="cell-title">
							分类
						</div>
					</div>
					{/*<div className="cell">
						<div className='cell-title'>底线</div>
					</div>
					<div className="cell">
						<div className='cell-title'>增长数</div>
					</div>*/}
					{
						props.estDateData && props.estDateData.map(item => {
							return (
								<div className="cell" key={item.estDate}>
									<div className='cell-title sub-cell-title'>
										{item.estDate.split('~')[0] && dayjs(item.estDate.split('~')[0].replace(/\./g, '/')).format('MM/DD')}
										~
										{item.estDate.split('~')[1] && dayjs(item.estDate.split('~')[1].replace(/\./g, '/')).format('MM/DD')}
									</div>
									<div className="sub-row">
										<div className="sub-cell">底线</div>
										<div className="sub-cell">增长数</div>
									</div>
								</div>
							)
						})
					}
				</div>
				<div className="body">
					{
						data.map((item) => {
							return (
								<div className="b-row" key={item.doctorCode}>
									<div className="b-cell-1">
										{item.doctorCode}
									</div>
									<div className="b-cell">
										<div className="b-cell-row">
											预估数
										</div>
										<div className="b-cell-row">
											长效预估数
										</div>
									</div>
									{
										props.estDateData && props.estDateData.map(estItem => {
											return (
												<div className="cel-group" key={estItem.estDate}>
													<div className="b-cell" style={{ width: '100%' }}>
														<div className="b-cell-row b-cell-row-edit" style={{ width: '100%' }}>
															<div
																// className={`adm-input ${isCurrentItem(currentItem, item, estItem, 'minTotalTarget') ? 'adm-input-editing' : ''}`}
																className={`adm-input ${isCurrentXYItem(currentItem, estItem.estDate + '*' + 'minTotalTarget' + '*' + item.doctorCode) ? 'adm-input-editing' : ''}`}
																onClick={() => {
																	setVisible(true);
																	setCurrentItem({
																		doctorCode: item.doctorCode,
																		type: 'minTotalTarget',
																		estDate: estItem.estDate,
																		xy: estItem.estDate + '*' + 'minTotalTarget' + '*' + item.doctorCode
																	})
																}}
															>
																{/*{item.boardVO.minTotalTarget}*/}
																{valueRender({item, estItem, key: 'minTotalTarget'})}
															</div>
														</div>
														<div className="b-cell-row b-cell-row-edit" style={{ width: '100%' }}>
															<div
																// className={`adm-input ${isCurrentItem(currentItem, item, estItem, 'minLongTarget') ? 'adm-input-editing' : ''}`}
																className={`adm-input ${isCurrentXYItem(currentItem, estItem.estDate + '*' + 'minLongTarget' + '*' + item.doctorCode) ? 'adm-input-editing' : ''}`}
																onClick={() => {
																	setVisible(true);
																	setCurrentItem({
																		doctorCode: item.doctorCode,
																		type: 'minLongTarget',
																		estDate: estItem.estDate,
																		xy: estItem.estDate + '*' + 'minLongTarget' + '*' + item.doctorCode
																	})
																}}
															>
																{/*{item.boardVO.minLongTarget}*/}
																{valueRender({item, estItem, key: 'minLongTarget'})}
															</div>
														</div>
													</div>
													<div className="b-cell" style={{ width: '100%' }}>
														<div className="b-cell-row b-cell-row-edit" style={{ width: '100%' }}>
															<div
																// className={`adm-input ${isCurrentItem(currentItem, item, estItem, 'estTotalNum') ? 'adm-input-editing' : ''}`}
																className={`adm-input ${isCurrentXYItem(currentItem, estItem.estDate + '*' + 'estTotalNum' + '*' + item.doctorCode) ? 'adm-input-editing' : ''}`}
																onClick={() => {
																	setVisible(true);
																	setCurrentItem({
																		doctorCode: item.doctorCode,
																		type: 'estTotalNum',
																		estDate: estItem.estDate,
																		xy: estItem.estDate + '*' + 'estTotalNum' + '*' + item.doctorCode
																	})
																}}
															>
																{/*{item.boardVO.estTotalNum}*/}
																{valueRender({item, estItem, key: 'estTotalNum'})}
															</div>
														</div>
														<div className="b-cell-row b-cell-row-edit" style={{ width: '100%' }}>
															<div
																// className={`adm-input ${isCurrentItem(currentItem, item, estItem, 'estLongNum') ? 'adm-input-editing' : ''}`}
																className={`adm-input ${isCurrentXYItem(currentItem, estItem.estDate + '*' + 'estLongNum' + '*' + item.doctorCode) ? 'adm-input-editing' : ''}`}
																onClick={() => {
																	setVisible(true);
																	setCurrentItem({
																		doctorCode: item.doctorCode,
																		type: 'estLongNum',
																		estDate: estItem.estDate,
																		xy: estItem.estDate + '*' + 'estLongNum' + '*' + item.doctorCode
																	})
																}}
															>
																{/*{item.boardVO.estLongNum}*/}
																{valueRender({item, estItem, key: 'estLongNum'})}
															</div>
														</div>
													</div>
												</div>
											)
										})
									}

									<NumberKeyboard
										visible={visible}
										onClose={() => {
											setVisible(false);
											setCurrentItem({});
										}}
										onInput={v => handleChange(v, currentItem)}
										onDelete={() => handleDelete(currentItem)}
										confirmText='确定'
										onConfirm={() => onConfirm(currentItem)}
									/>
								</div>
							)
						})
					}
					{
						props.level === 6 && (
							<div className="b-row" style={{ background: 'rgb(232, 243, 255)'}}>
								<div className="b-cell-1">
									合计
								</div>
								<div className="b-cell">
									<div className="b-cell-row">
										预估数
									</div>
									<div className="b-cell-row">
										长效预估数
									</div>
								</div>
								{
									props.estDateData && props.estDateData.map((dateItem, index) => {
										return (
											<div className={'cel-group'} key={dateItem.estDate + index}>
												<div className="b-cell" style={{ width: '100%'}}>
													<div className="b-cell-row">
														<div>{total(data, 'minTotalTarget', dateItem.estDate).minTotal}</div>
													</div>
													<div className="b-cell-row">
														<div>{total(data, 'minLongTarget', dateItem.estDate).minTotal}</div>
													</div>
												</div>
												<div className="b-cell" style={{ width: '100%'}}>
													<div className="b-cell-row">
														<div>{total(data, 'estTotalNum', dateItem.estDate).estTotal}</div>
													</div>
													<div className="b-cell-row">
														<div>{total(data, 'estLongNum', dateItem.estDate).estTotal}</div>
													</div>
												</div>
											</div>
										)
									})
								}

							</div>
						)
					}
				</div>
			</div>
			{
				// 大区 区域展示
				(props.level === 4 || props.level === 3) && (
					<div className="table-footer">
						<div className="footer-cell"></div>
						<div className="footer-cell"></div>
						{
							props.estDateData && props.estDateData.map((item, index) => {
								return (
									<PopForm key={item.estDate} value={coreActionsRender(item.estDate)} estDate={item.estDate} handleSave={(value) => handlePopSave(value, item.estDate)}>
										{
											_setVisible => {
												return (
													<div
														className="footer-cell"
														onClick={() => {
															setVisible(false);
															_setVisible(true);
														}}
														style={{
															background: (!coreActionsRender(item.estDate).hasValue ? 'rgb(22, 93, 255)' : '#A4ADB3'),
															color: '#fff'
														}}
													>
														<div className="inner">
															<>
																增长点和行动计划
																<br />
																{
																	coreActionsRender(item.estDate).hasValue ?  '(已填写)' : '(未填写)'
																}
															</>
														</div>
													</div>
												)
											}
										}
									</PopForm>
								)
							})
						}
					</div>
				)
			}
			{
				// 大区 区域展示
				(props.level === 4 || props.level === 3) && (data && !!data.length) && (
					<div>
						<div style={{ textAlign: 'center', fontWeight: 'bold', padding: '10px', color: '#6C6C6C'}}>图例：数字为红色，请及时补充填写工作计划</div>
						<div className="topic">
							填写目标：
							<br />
							*工作计划至少填写最近2周
							<br />
							*目标至少6周
						</div>
					</div>
				)
			}
			{
				data && !!data.length && (
					<div className="footer">
						<Space>
							<Button
								onClick={() =>
									Dialog.confirm({
										content: `是否${(props.level === 4 || props.level === 3) ? '重置' : '清零'}?`,
										onConfirm: async () => {
											cleanUp()
										},
									})
								}
								style={{ width: 'calc(30vw)', background: 'rgb(232, 243, 255)', border: 'none', color: 'rgb(51, 112, 255)'}}
							>
								{(props.level === 4 || props.level === 3) ? '重置' : '清零'}
							</Button>
							<Button
								color='primary'
								fill='solid'
								onClick={handleSave}
								loading={loading}
								style={{ width: 'calc(50vw)', background: 'rgb(22, 93, 255)', color: '#fff' }}
							>
								提交
							</Button>
						</Space>
					</div>
				)
			}
			<SafeArea position={'bottom'} />
			<div style={{ height: 20 }} />
		</Wrapper>
	)
}

export default ReportTable;
