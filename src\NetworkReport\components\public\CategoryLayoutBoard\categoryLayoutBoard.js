import React from 'react';
import './categoryLayoutBoard.css'


const CategoryLayoutBoard = (props) => {

    return (
        <div className={'categoryLayoutBoard'}>
            <div className={'categoryLayoutBoard__header'}>
                {props.children[0]}
            </div>
            <div className={'categoryLayoutBoard__container'}>
                {props.children[1]}
            </div>
            <div className={'categoryLayoutBoard__footer'}>
                {props.children[2]}
            </div>
        </div>
    )
}

export default CategoryLayoutBoard
