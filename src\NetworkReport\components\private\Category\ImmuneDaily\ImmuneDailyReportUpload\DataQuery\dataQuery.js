import React, { useState, useEffect, useMemo, useRef } from "react";
import dataQuery from "./dataQuery.less";

import { useNavigate, useLocation } from "react-router-dom";

const now = new Date();
import { Collapse, DatePicker, InfiniteScroll } from "antd-mobile";
import DoubleArrowIcon from "@mui/icons-material/DoubleArrow";
import { Tabs, Form } from "antd-mobile";
import "./reproductionWeekReportUploadDataQuery.css";
import dayjs from "dayjs";
import { RightOutline } from "antd-mobile-icons";
import { http1 } from "../../../../../../utils/network";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import HomeIcon from "@mui/icons-material/Home";

const DataQuery = (props) => {
  const [hasMore, setHasMore] = useState(true);
  const [hasMore2, setHasMore2] = useState(true);
  const [form] = Form.useForm();
  const [subForm] = Form.useForm();
  const time1 = Form.useWatch("time1", form);
  const time2 = Form.useWatch("time2", subForm);
  const [step, setStep] = useState(1);
  const [step2, setStep2] = useState(1);

  const [showDate, setShowDate] = useState(false);

  const [reportersList, setReportersList] = useState([]);
  const [subReportersList, setSubReportersList] = useState([]);

  const navigate = useNavigate();

  const loadMore = async () => {
    console.log("load more");
    try {
      let selectTime = time1
        ? dayjs(time1).format("YYYY-MM-DD") + " 00:00:00"
        : null;
      const res = await http1.post("/immune/get/immune/page", {
        period: selectTime,
        branchEmpNo: false,
        pageNum: 1,
        pageSize: step * 20,
      });
      if (res.code === 500) {
        return;
      }
      setHasMore(res.total !== (res.records || []).length);
      setReportersList(res.records || []);
      if (res.records && res.records.length === 0) {
        setShowDate(true);
      } else {
        setShowDate(false);
        setStep(step + 1);
      }
    } catch (e) {
      setShowDate(false);
    }
  };

  const loadMoreSub = async () => {
    console.log("load more sub");
    try {
      let selectTime = time2
        ? dayjs(time2).format("YYYY-MM-DD") + " 00:00:00"
        : null;
      const res = await http1.post("/immune/get/immune/page", {
        period: selectTime,
        branchEmpNo: true,
        pageNum: 1,
        pageSize: step2 * 20,
      });
      if (res.code === 500) {
        return;
      }
      setHasMore2(res.total !== (res.records || []).length);
      setSubReportersList(res.records || []);
      if (res.records && res.records.length === 0) {
        setShowDate(true);
      } else {
        setShowDate(false);
        setStep2(step2 + 1);
      }
    } catch (e) {
      setHasMore2(false);
    }
  };

  const [hasFloor, setHasFloor] = useState(null);
  useEffect(() => {
    http1.post("/immuneDaily/immuneDaily/hasBranch").then((res) => {
      setHasFloor(res);
    });
  }, []);

  useEffect(() => {
    loadMore();
  }, [time1]);

  useEffect(() => {
    loadMore();
  }, [time2]);

  const handleNum = (value) => {
    if (value === 0) {
      return value;
    } else {
      let temp = String(value);
      if (temp.indexOf(".") !== -1) {
        temp = temp.slice(0, temp.indexOf(".") + 5);
      }
      return Number(temp);
    }
  };

  const handleTarget = () => {
    history.back();
  };

  const handleHome = () => {
    navigate("/category/immuneDaily/immuneDailyReportUpload/dataBoard");
  };

  return (
    <div
      className={dataQuery.dataQuery + " reproductionWeekReportUploaddataQuery"}
    >
      <div className={dataQuery.top}>
        <ChevronLeftIcon
          sx={{ fontSize: 50 }}
          onClick={() => {
            handleTarget();
          }}
        />
        <HomeIcon
          sx={{ fontSize: 30 }}
          style={{ marginRight: "5px" }}
          onClick={() => {
            handleHome();
          }}
        />
        免疫事业部
        <DoubleArrowIcon fontSize={"small"} />
        <span>日战报</span>
        <DoubleArrowIcon fontSize={"small"} />
        数据上报
      </div>
      <Tabs className={dataQuery.content}>
        <Tabs.Tab title={hasFloor ? "本人上报" : null} key="one">
          <div className={dataQuery.tabContainer}>
            <div className={dataQuery.tabContainerTop}>
              <Form form={form}>
                <Form.Item
                  name="time1"
                  label="上报时间"
                  trigger="onConfirm"
                  onClick={(e, datePickerRef) => {
                    datePickerRef.current?.open();
                  }}
                >
                  <DatePicker max={new Date()}>
                    {(value) =>
                      value ? dayjs(value).format("YYYY-MM-DD") : null
                    }
                  </DatePicker>
                </Form.Item>
              </Form>
            </div>
            {/* <Space style={{ paddingLeft: "25px" }}>
              <Tag color="success">N人已提报</Tag>
              <Tag color="danger">N人未提报</Tag>
              <Tag color="primary">代理商沟通数: 58</Tag>
            </Space> */}
          </div>
          {reportersList.map((item) => {
            return <SalesCard data={item} key={item.id} />;
          })}
          <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />
          {showDate ? <div className={dataQuery.showText}>暂无数据</div> : null}
        </Tabs.Tab>
        {hasFloor ? (
          <Tabs.Tab title="下级上报" key="two">
            <Form form={subForm}>
              <Form.Item
                name="time2"
                label="上报时间"
                trigger="onConfirm"
                onClick={(e, datePickerRef) => {
                  datePickerRef.current?.open();
                }}
              >
                <DatePicker max={new Date()}>
                  {(value) =>
                    value ? dayjs(value).format("YYYY-MM-DD") : null
                  }
                </DatePicker>
              </Form.Item>
            </Form>
            {subReportersList.map((item) => {
              return <SubSalesCard data={item} key={item.id} />;
            })}
            <InfiniteScroll loadMore={loadMoreSub} hasMore={hasMore2} />

            {showDate ? (
              <div className={dataQuery.showText}>暂无数据</div>
            ) : null}
          </Tabs.Tab>
        ) : null}
      </Tabs>
    </div>
  );
};

const SalesCard = (props) => {
  let { data } = props;

  const navigate = useNavigate();

  const handleDetail = (id, time) => {
    navigate("/category/immuneDaily/immuneDailyReportUpload/dataUpdate", {
      state: { id, formType: "update", time },
    });
  };
  const handleCustomerDetail = (id, time) => {
    navigate(
      "/category/immuneDaily/immuneDailyReportUpload/dataUpdateCustomer",
      {
        state: { id, formType: "update", time },
      }
    );
  };
  const handleNum = (value) => {
    let temp = String(value);
    console.log(temp);
    if (temp.indexOf(".") !== -1) {
      temp = temp.slice(0, temp.indexOf(".") + 5);
    }
    return temp;
  };
  return data.customer ? (
    <div
      onClick={() => handleCustomerDetail(data.customer.id, data.insertTime)}
      style={{ borderBottom: "1px solid #ccc", padding: "10px" }}
    >
      {data.customer.hospitalName}+{data.customer.insDept}+
      {data.customer.productThreeName}+{data.customer.num}新增预估
      <div style={{ margin: "10px 0 0 240px" }}>
        {dayjs(data.insertTime).format("YYYY-MM-DD")}
      </div>
    </div>
  ) : (
    <div
      onClick={() => handleDetail(data.id, data.insertTime)}
      style={{ borderBottom: "1px solid #ccc", padding: "10px" }}
    >
      {dayjs(data.insertTime).format("YYYY-MM-DD")};
      {data.hospitalName ? data.hospitalName : ""};
      {data.medicineHospitalName ? data.medicineHospitalName : ""}
      {data.entityList &&
        data.entityList.map((d) => {
          return (
            <>
              [ 产品:
              {d.productType}; 购药渠道:
              {d.channel}; 品类:
              {d.prdBrand}; 规格：
              {d.specification}; 科室：
              {d.dept}; 适应症：
              {d.patientType}; 新增数：
              {d.newPatNum}; 新增单价：
              {d.newPatientPrice}; ]
            </>
          );
        })}
      {/*销售代表姓名:{data.createdName}-*/}
      {/*{data.hospitalName ? data.hospitalName : ""}*/}
      {/*  /!*+新客户{data.customers}*!/*/}
      {/*  +存量预估金额{handleNum(data.sales)}元+新增预估*/}
      {/*{data.newPatients}+存量预估{data.oldPatients}*/}
      {/*<div style={{ margin: "10px 0 0 240px" }}>*/}
      {/*  {dayjs(data.insertTime).format("YYYY-MM-DD")}*/}
      {/*</div>*/}
    </div>
  );
};
const SubSalesCard = (props) => {
  let { data } = props;

  const navigate = useNavigate();

  const handleDetail = (id, time) => {
    navigate("/category/immuneDaily/immuneDailyReportUpload/dataUpdate", {
      state: { id, formType: "update", time, reporter: "management" },
    });
  };

  const handleCustomerDetail = (id, time) => {
    navigate(
      "/category/immuneDaily/immuneDailyReportUpload/dataUpdateCustomer",
      {
        state: { id, formType: "update", time },
      }
    );
  };

  const handleNum = (value) => {
    let temp = String(value);
    console.log(temp);
    if (temp.indexOf(".") !== -1) {
      temp = temp.slice(0, temp.indexOf(".") + 5);
    }
    return temp;
  };

  return data.customer ? (
    <div
      onClick={() => handleCustomerDetail(data.customer.id, data.insertTime)}
      style={{ borderBottom: "1px solid #ccc", padding: "10px" }}
    >
      {data.customer.hospitalName}+{data.customer.insDept}+
      {data.customer.productThreeName}+{data.customer.num}新增预估
      <div style={{ margin: "10px 0 0 240px" }}>
        {dayjs(data.insertTime).format("YYYY-MM-DD")}
      </div>
    </div>
  ) : (
    <Collapse>
      <Collapse.Panel key="1" title={"销售代表姓名：" + data.createdName}>
        <div onClick={() => handleDetail(data.id, data.insertTime)}>
          {dayjs(data.insertTime).format("YYYY-MM-DD")};
          {data.hospitalName ? data.hospitalName : ""};
          {data.medicineHospitalName ? data.medicineHospitalName : ""}
          {data.entityList &&
            data.entityList.map((d) => {
              return (
                <>
                  [ 产品:
                  {d.productType}; 购药渠道:
                  {d.channel}; 品类:
                  {d.prdBrand}; 规格：
                  {d.specification}; 科室：
                  {d.dept}; 适应症：
                  {d.patientType}; 新增数：
                  {d.newPatNum}; 新增单价：
                  {d.newPatientPrice}; 新增产品数量：
                  {d.npVolume}; 新增金额：
                  {d.newPatientAmount}; 存量数：
                  {d.oldPatNum}
                  存量单价：
                  {d.oldPatientPrice}; 存量产品数量：
                  {d.opVolume}; 存量金额：
                  {d.oldPatientAmount}; ]
                </>
              );
            })}
          {/*  销售代表姓名:{data.createdName}-*/}
          {/*  {data.hospitalName ? data.hospitalName : ""}*/}
          {/*  /!*+新客户{data.customers}*!/*/}
          {/*  +存量预估金额{handleNum(data.sales)}*/}
          {/*  元+新增预估{data.newPatients}+存量预估{data.oldPatients}*/}
          {/*<div style={{ margin: "10px 0 0 240px" }}>*/}
          {/*  {dayjs(data.insertTime).format("YYYY-MM-DD")}*/}
          {/*</div>*/}
        </div>
      </Collapse.Panel>
    </Collapse>
  );
};

export default DataQuery;
