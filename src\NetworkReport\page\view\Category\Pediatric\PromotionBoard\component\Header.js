import React from "react";
import styled from 'styled-components';

const Wrapper = styled.div`
  .header {
    text-align: center;
    background: #efefef;
    border: 1px solid #bbb;
		.title {
      height: 50px;
      line-height: 50px;
      font-weight: bold;
      font-size: 16px;
		}
		.content {
      display: flex;
      justify-content: space-between;
      padding: 10px;
		}
	}
`;

const Header = ({ data = {}}) => {
	return (
		<Wrapper>
			<div className='header'>
				<div className="title">代表晋升看板</div>
				<div className="content">
					<div className="item">姓名：{data.name}</div>
					<div className="item">当前级别：{data.level}</div>
					<div className="item">工号：{data.empNo}</div>
				</div>
			</div>
		</Wrapper>
	)
}
export default Header;
