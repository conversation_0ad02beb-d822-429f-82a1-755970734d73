.neuroendocrineDayReportUpload {
    height: 100%;
}
.neuroendocrineDayReportUpload .top{
    height: 38px;
    background: var(--gensci-second);
    color: white;
    font-size: 15px;
    display: flex;
    align-items: center;
    padding: 0 8px;
    box-shadow: 0 2px 2px #d2d2d2;
}

.neuroendocrineDayReportUpload .content{
    height: calc(100% - 25px);
}

.neuroendocrineDayReportUpload .fontMain{
    color: var(--gensci-main);
}

.neuroendocrineDayReportUpload .fontSecond{
    color: var(--gensci-second);
}

.neuroendocrineDayReportUpload .header{
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px 0 5px;
    background: #3644ac0f
}

.neuroendocrineDayReportUpload .container{
    height: 100%;
    overflow: auto;
}

.neuroendocrineDayReportUpload .footer{
    height: 100%;
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    align-items: center;
}

.neuroendocrineDayReportUpload .countFunc{
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 15px;
    color: black;
}

.neuroendocrineDayReportUpload .headerContext {
    display: flex;
    align-items: center;
    font-size: 15px;
}
.neuroendocrineDayReportUpload .admformitem::before{
   content: "*";
   color:red;
   position: absolute;
   /* left: -0.6em; */

   top: 10px;
   left: 7px;
   user-select: none;
   font-family: SimSun, sans-serif;
 }

 .neuroendocrineDayReportUpload .searchBarlist{
    display: flex;
    justify-content: space-between;
    align-items: center;
 }
 .neuroendocrineDayReportUpload .fotter{
    width: 100%;
    height: 55px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    
    z-index: 999;
}

.neuroendocrineDayReportUpload__searchContainer {
    margin: 5px 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

