import React, {useEffect, useState, useRef} from 'react';
import styled from 'styled-components';
import {Button, Toast, Dialog} from "antd-mobile";
import {RightOutline, LeftOutline, CheckCircleFill, DownOutline, UpOutline} from 'antd-mobile-icons'
import {useNavigate, useSearchParams} from "react-router-dom";
import {http1} from "../../../utils/network";

const Wrapper = styled.div`
  table td {
    padding: 5px 16px;
    text-align: center;
  }
  table thead {
    background-color: #F3F4F5;
  }
  table thead tr th {
    padding: 5px 16px;
    font-size: 14px;
  }

  input:focus-visible, textarea:focus-visible {
    border: 1px solid #F3F4F5;
    padding: 2px 6px;/*清除自带的padding间距*/
    outline: none;/*清除input点击之后的黑色边框*/
  }
  input, textarea {
    border: 1px solid #F3F4F5;
    padding: 2px 6px;/*清除自带的padding间距*/
    outline: none;/*清除input点击之后的黑色边框*/
  }
  
  
  
  .title {
    color: var(--color-text-1, #1D2129);
    /* 16/CN-Medium */
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px; /* 150% */
    margin-bottom: 22px;
  }
  .hospital-list-wrapper {
    position: relative;
    height: 72px;
    overflow: hidden;
    .left-icon {
      position: absolute;
      left: 20px;
      top: 50%;
      transform: translateY(-50%);
    }
    .right-icon {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);
    }
    .hospital-list {
      display: flex;
      width: calc(100% - 200px);
      margin: 0 auto;
      flex-wrap: wrap;
      min-height: 60px;
      .item {
        width: calc(20% - 18px);
        padding: 12px 16px;
        flex-direction: column;
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        background: #F5F6F8;
        margin-right: 16px;
        cursor: pointer;
        margin-bottom: 16px;
        &:last-of-type {
          margin-right: 0;
        }
        .top {
          color: #1D212B;
          font-size: 16px;
          font-weight: 500;
          line-height: 24px;
          height: 24px;
        }
        .bot {
          font-size: 14px;
          font-weight: 500;
          line-height: 22px;
          height: 22px;
          color: #96A1AA;
        }
        .done {
          color: #34C724;
        }
      }
      .item-selected {
        border: 1px solid var(--unnamed, #2551F2) !important;
        background: var(--unnamed, #E8F0FF) !important;
        .top {
          color: #2551F2 !important;
        }
        .bot {
          color: #2551F2;
        }
      }
    }
  }
  .hospital-full-wrapper {
    margin: 25px 16px;
    border: 1px solid #E5E6EB;
    .hospital-info-wrapper {
      border-radius: 8px 8px 0 0;
      background: #E8F0FF;
      padding: 16px 24px;
      .name-header {
        margin-bottom: 15px;
        .name {
          font-size: 24px;
          font-style: normal;
          font-weight: 500;
          line-height: 32px;
          height: 32px;
          margin-right: 10px;
          color: #2551F2;
        }
        .hospital-tag {
          color: #F54A45;
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
          line-height: 22px; /* 157.143% */
          border-radius: 2px;
          background: #FEEDEC;
          padding: 2px 8px;
        }
        .hospital-tag-1 {
          color: #FF8800;
          background: #FFFCE8;
        }
        .hospital-tag-2{
          color: #F7BA1E;
          background: #FFFCE8;
        }
        .hospital-tag-3{
          color: #33D1C9;
          background: #E8FFFB;
        }
        .hospital-tag-4{
          color: #722ED1;
          background: #F5E8FF;
        }
        .hospital-tag-5{
          color: #1D212B;
          background: #F3F4F5;
        }
      }
      .sub-info {
        display: flex;
        .sub-info-item {
          margin-right: 50px;
          &>div {
            display: inline-block;
          }
          .label {
            color: #96A1AA;
            /* 14/CN-Regular */
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
            height: 22px;
          }
          .value {
            color: #1D212B;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            height: 22px;
          }
        }
      }
    }
    .inner {
      padding: 16px 32px;
      .action-bar {
        display: flex;
        justify-content: space-between;
        margin-bottom: 18px;
      }
    }
    .txt {
      color: var(--unnamed, #2551F2);
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
  }
  
  .footer {
    text-align: right;
    position: fixed;
    bottom: 16px;
    right: 16px;
    button {
      margin-left: 16px;
    }
  }
`;

const Report = props => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams()

  const deptcode = searchParams.get('deptcode')
  const estDate = searchParams.get('estDate')
  const empNo = searchParams.get('empNo')

  const [loading, setLoading] = useState(false);
  const [currentHospital, setCurrentHospital] = useState({});
  const [hospitalList, setHospitalList] = useState([]);
  const [formValue, setFormValue] = useState({
    planVo: {},
    boardVos: []
  });
  const [open, setOpen] = useState(false);
  const handler = useRef();
  const handler2 = useRef();
  const handler3 = useRef();

  useEffect(() => {
    if(deptcode) {
      getHospitalList(deptcode);
    }
  }, [deptcode])

  useEffect(() => {
    if(currentHospital.hospitalid) {
      getForm();
    }
  }, [currentHospital.hospitalid])

  const toggleOpen = () => {
    setOpen(!open)
  }

  const getHospitalList = async (deptcode) => {
    try {
      Toast.show({
        icon: 'loading',
        duration: 0
      })
      const res = await http1.get(`/estimate/query/hospital?deptcode=${deptcode}`)
      setHospitalList(res || []);
      Toast.clear();
    } catch (e) {
      Toast.clear();
    }
  }

  const handleSelectHospital = (hospital) => {
    if(currentHospital.hospitalid) {
      handler3.current = Dialog.show({
        content: '当前填写未保存，确认跳切换机构吗？',
        actions: [
          [
            {
              key: 'cancel',
              text: '取消',
              onClick: () => {
                handler3.current?.close()
              }
            },
            {
              key: 'queren',
              text: '确认',
              onClick: () => {
                handler3.current?.close();
                setCurrentHospital(hospital)
              },
            }
          ],
        ]})
    }else {
      setCurrentHospital(hospital)

    }
  }

  const getForm = async () => {
    try {
      Toast.show({
        icon: 'loading',
        duration: 0
      })
      const res = await http1.post(`/estimate/query/form`, {
        deptcode: deptcode,
        estDate,
        hospitalId: currentHospital.hospitalid,
        empNo
      }) || {};
      setFormValue({
        ...res,
        planVo: {
          ...res.planVo,
          actionPlan: res.planVo.actionPlan || ''
        }
      })
      Toast.clear();
    } catch (e) {
      Toast.clear();
    }
  }

  const planVoNumberChange = (event, type) => {
    const inputValue = event.target.value;
    const maxValue = 9999; // 设置最大值
    const minValue = 0; // 设置最小值

    if (parseInt(inputValue) > maxValue) {
      setFormValue({
        ...formValue,
        planVo: {
          ...formValue.planVo,
          [type]: maxValue.toString()
        }
      })
    }else if (parseInt(inputValue) < minValue) {
      setFormValue({
        ...formValue,
        planVo: {
          ...formValue.planVo,
          [type]: minValue.toString()
        }
      })
    } else {
      setFormValue({
        ...formValue,
        planVo: {
          ...formValue.planVo,
          [type]: inputValue
        }
      })
    }
  }

  const depNumberChange = (event, item, type) => {
    const inputValue = event.target.value;
    const maxValue = 9999; // 设置最大值
    const minValue = 0; // 设置最小值

    if (parseInt(inputValue) > maxValue) {
      setFormValue({
        ...formValue,
        boardVos: formValue.boardVos.map(d => {
          if(d.code === item.code) {
            d[type] = maxValue.toString()
          }
          return d
        })
      })
    }else if (parseInt(inputValue) < minValue) {
      setFormValue({
        ...formValue,
        boardVos: formValue.boardVos.map(d => {
          if(d.code === item.code) {
            d[type] = minValue.toString()
          }
          return d
        })
      })
    } else {
      setFormValue({
        ...formValue,
        boardVos: formValue.boardVos.map(d => {
          if(d.code === item.code) {
            d[type] = inputValue
          }
          return d
        })
      })
    }
  }

  const actionPlanChange = (event) => {
    const inputValue = event.target.value;
    setFormValue({
      ...formValue,
      planVo: {
        ...planVo,
        actionPlan: inputValue
      }
    })
  }

  const handlePage = (type) => {
    const currentIndex = hospitalList.findIndex(d => d.hospitalid === currentHospital.hospitalid);
    if(currentIndex === -1 ) {
      return false
    }
    if(currentIndex === hospitalList.length - 1 || (currentIndex === 0 && type === '<')) {
      return false;
    }
    if(type === '>') {
      setCurrentHospital(hospitalList[currentIndex + 1])
    }else {
      setCurrentHospital(hospitalList[currentIndex - 1])
    }
  }

  const handleSave = async () => {
     try {
       if(!formValue.planVo.actionPlan) {
         Toast.show('请填写其他核心管理举措和行动计划！');
         return false;
       }
       setLoading(true);
       await http1.post(`/estimate/save`, {
         ...formValue,
         deptcode: deptcode,
         estDate,
         hospitalid: currentHospital.hospitalid,
         empNo
       });
       await getHospitalList(deptcode)
       Toast.show('操作成功！')
       setLoading(false);

       const currentIndex = hospitalList.findIndex(d => d.hospitalid === currentHospital.hospitalid);

       if(currentIndex < hospitalList.length - 1) {
         setCurrentHospital(hospitalList[currentIndex + 1])
       }else {
         navigate(-1)
       }
     } catch (e) {
       setLoading(false);
     }

  }

  // 合计
  const total = (data) => {
    let newPat = 0;
    let longPat = 0;
    let longRate = '0%';
    data.forEach(item => {
      newPat = +(item.newPat || 0) + newPat;
      longPat = +(item.longPat || 0) + longPat;
    })
    if(newPat) {
      longRate = ((longPat / newPat) * 100).toFixed(2) + '%';
    }


    return {
      newPat,
      longPat,
      longRate
    }
  }

  const {
    hospitalName,
    province,
    city,
    district,
    level,
    type,
    grade,
    top,
    potential,
    highPotential,
    publicHos,
    nonPublicHos,
    other
  } = currentHospital;
  const { planVo, boardVos } = formValue;

  return (
    <Wrapper>
      <div className="title">
        我的机构列表
      </div>
      <div
        style={
          open ?
            {
              height: 'auto'
            } :
            {}
        }
       className="hospital-list-wrapper"
      >
        <Button className={'right-icon'} onClick={() => {
          handler.current = Dialog.show({
            content: '当前填写未保存，确认跳转下一个机构吗？',
            actions: [
              [
                {
                  key: 'cancel',
                  text: '取消',
                  onClick: () => {
                    handler.current?.close()
                  }
                },
                {
                  key: 'queren',
                  text: '确认',
                  onClick: () => {
                    handler.current?.close();
                    handlePage('>')
                  },
                }
              ],
            ]})
        }}>
          <RightOutline />
        </Button>
        <Button className={'left-icon'} onClick={() => {

          handler2.current = Dialog.show({
            content: '当前填写未保存，确认跳转上一个机构吗？',
            actions: [
              [
                {
                  key: 'cancel',
                  text: '取消',
                  onClick: () => {
                    handler2.current?.close()
                  }
                },
                {
                  key: 'queren',
                  text: '确认',
                  onClick: () => {
                    handler2.current?.close();
                    handlePage('<')
                  },
                }
              ],
            ]})
        }}>
          <LeftOutline />
        </Button>
        <div className="hospital-list">
          {
            hospitalList.map(item => {
              return (
                <div
                  key={item.hospitalid}
                  className={`item truncate ${item.hospitalid === currentHospital.hospitalid && 'item-selected'}`}
                  onClick={() => handleSelectHospital(item)}
                  style={item.isEst ? {
                    border: '1px solid #CCF1C8',
                    background: '#EBF9E9'
                  } : {}}
                  title={item.hospitalName}
                >
                  <div className="top">{item.hospitalName}</div>

                  {
                    item.hospitalid === currentHospital.hospitalid ? <div className={'bot'}>当前</div> : (
                      item.isEst ? <div className="bot done"><CheckCircleFill style={{ color: '#34C724', display: 'inline-block', marginRight: 8 }} />预估完成</div>
                        :
                        <div className="bot">未填写</div>
                    )
                  }
                </div>
              )
            })
          }
        </div>
      </div>
      {
        hospitalList.length > 5 && (
          <div style={{
            textAlign: 'center',
            padding: '12px 0',
            cursor: 'pointer'
          }}
               onClick={toggleOpen}
          >
            {
              open ? <UpOutline style={{ color: '#2551F2', display: 'inline-block' }} /> : <DownOutline style={{ color: '#2551F2', display: 'inline-block' }} />
            }
            {
              open ? <span style={{ color: '#2551F2' }}>收起</span> : <span style={{ color: '#2551F2' }}>展开</span>
            }
          </div>
        )
      }
      {
        // 选中后再渲染下面的内容
        currentHospital.hospitalid && (
          <>
            <div className="hospital-full-wrapper">
              <div className="hospital-info-wrapper">
                <div className="name-header truncate">
                  <span className="name">{hospitalName}</span>
                  {
                    top && <span className={'hospital-tag'}>Top医院</span>
                  }
                  {
                    potential && <span className={'hospital-tag hospital-tag-1'}>潜力医院</span>
                  }
                  {
                    highPotential && <span className={'hospital-tag hospital-tag-2'}>高潜医院</span>
                  }
                  {
                    publicHos && <span className={'hospital-tag hospital-tag-3'}>公立医院</span>
                  }
                  {
                    nonPublicHos && <span className={'hospital-tag hospital-tag-4'}>非公医院</span>
                  }
                  {
                    other && <span className={'hospital-tag hospital-tag-5'}>其他</span>
                  }
                </div>
                <div className="sub-info">
                  <div className="sub-info-item">
                    <div className="label">
                      省/市/区县:
                    </div>
                    <div className="value">
                      {province}/{city}/{district}
                    </div>
                  </div>
                  <div className="sub-info-item">
                    <div className="label">
                      医院级别:
                    </div>
                    <div className="value">
                      {level || '--'}
                    </div>
                  </div>
                  <div className="sub-info-item">
                    <div className="label">
                      医院等次:
                    </div>
                    <div className="value">
                      {grade || '--'}
                    </div>
                  </div>
                  <div className="sub-info-item">
                    <div className="label">
                      经济类型:
                    </div>
                    <div className="value">
                      {type || '--'}
                    </div>
                  </div>
                </div>
              </div>
              <div className="inner">
                <div className="action-bar">
                  <div className="txt">新增预估</div>
                  {/*<Button color="primary" fill='outline'>编辑</Button>*/}
                  <div></div>
                </div>

                <div className="table-1">
                  <table className="border-collapse border border-slate-400 w-full">
                    <thead>
                    <tr>
                      <th className="border border-slate-300 w-2/12">科室</th>
                      <th className="border border-slate-300 w-5/12">分类</th>
                      <th className="border border-slate-300 w-5/12">下周({estDate})</th>
                    </tr>
                    </thead>
                    <tbody>
                    {/* 合计*/}
                    <tr>
                      <td className="border border-slate-300" rowSpan={3}>合计</td>
                      <td className="border border-slate-300">新增数</td>
                      <td className="border border-slate-300">{total(boardVos).newPat}</td>
                    </tr>
                    <tr>
                      {/*<td></td>*/}
                      <td className="border border-slate-300">长效数</td>
                      <td className="border border-slate-300">{total(boardVos).longPat}</td>
                    </tr>
                    <tr>
                      {/*<td></td>*/}
                      <td className="border border-slate-300">长效占比</td>
                      <td className="border border-slate-300">{total(boardVos).longRate}</td>
                    </tr>
                    {
                      boardVos.map(item => {
                        return (
                          <React.Fragment key={item.code}>
                            {/* 一组 科室数据 */}
                            <tr>
                              <td className="border border-slate-300" rowSpan={2}>{item.code}</td>
                              <td className="border border-slate-300">新增数</td>
                              <td className="border border-slate-300">
                                <input type="number" max={9999} min={0} value={item.newPat} onChange={e => depNumberChange(e, item, 'newPat')}/>
                              </td>
                            </tr>
                            <tr>
                              {/*<td></td>*/}
                              <td className="border border-slate-300">长效数</td>
                              <td className="border border-slate-300">
                                <input type="number" max={9999} min={0} value={item.longPat} onChange={e => depNumberChange(e, item, 'longPat')}/>
                              </td>
                            </tr>
                          </React.Fragment>
                        )
                      })
                    }
                    </tbody>
                  </table>
                </div>

                <div className="table-2">
                  <div className="txt" style={{ margin: '18px 0' }}>
                    周度业务计划
                  </div>
                  <table className="border-collapse border border-slate-400 w-full">
                    <thead>
                    <tr>
                      <th className="border border-slate-300 w-2/12"></th>
                      <th className="border border-slate-300 w-5/12">分类</th>
                      <th className="border border-slate-300 w-5/12">下周({estDate})</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                      <td className="border border-slate-300" rowSpan={3}>KH改善</td>
                      <td className="border border-slate-300">观念谨慎型</td>
                      <td className="border border-slate-300">
                        <input type="number" max={9999} min={0} value={planVo.improveConceptNum} onChange={e => planVoNumberChange(e, 'improveConceptNum')}/>
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">合作改善型</td>
                      <td className="border border-slate-300">
                        <input type="number" max={9999} min={0} value={planVo.improveCooperateNum} onChange={e => planVoNumberChange(e, 'improveCooperateNum')}/>
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">长效和KH改善型</td>
                      <td className="border border-slate-300">
                        <input type="number" max={9999} min={0} value={planVo.improveLongNum} onChange={e => planVoNumberChange(e, 'improveLongNum')}/>
                      </td>
                    </tr>

                    <tr>
                      <td className="border border-slate-300" rowSpan={2}>行为</td>
                      <td className="border border-slate-300">大区经理拜访</td>
                      <td className="border border-slate-300">
                        <input type="number" max={9999} min={0} value={planVo.distVisitNum} onChange={e => planVoNumberChange(e, 'distVisitNum')}/>
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">大区经理协访</td>
                      <td className="border border-slate-300">
                        <input type="number" max={9999} min={0} value={planVo.distAidVisitNum} onChange={e => planVoNumberChange(e, 'distAidVisitNum')}/>
                      </td>
                    </tr>

                    <tr>
                      <td className="border border-slate-300" rowSpan={3}>活动计划</td>
                      <td className="border border-slate-300">增流活动目标数</td>
                      <td className="border border-slate-300">
                        <input type="number" max={9999} min={0} value={planVo.flowActivityNum} onChange={e => planVoNumberChange(e, 'flowActivityNum')}/>
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">帮扶/义诊目标数</td>
                      <td className="border border-slate-300">
                        <input type="number" max={9999} min={0} value={planVo.helpActivityNum} onChange={e => planVoNumberChange(e, 'helpActivityNum')}/>
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">学术活动目标数</td>
                      <td className="border border-slate-300">
                        <input type="number" max={9999} min={0} value={planVo.academicActivityNum} onChange={e => planVoNumberChange(e, 'academicActivityNum')}/>
                      </td>
                    </tr>

                    <tr>
                      <td className="border border-slate-300" rowSpan={2}>YZHZ</td>
                      <td className="border border-slate-300">YZHZ储备数</td>
                      <td className="border border-slate-300">
                        <input type="number" max={9999} min={0} value={planVo.reserveNum} onChange={e => planVoNumberChange(e, 'reserveNum')}/>
                      </td>
                    </tr>
                    <tr>
                      <td className="border border-slate-300">新增宝宝数</td>
                      <td className="border border-slate-300">
                        <input type="number" max={9999} min={0} value={planVo.newBabyNum} onChange={e => planVoNumberChange(e, 'newBabyNum')}/>
                      </td>
                    </tr>

                    <tr>
                      <td className="border border-slate-300" colSpan={2}>其他核心管理举措和行动计划</td>
                      <td className="border border-slate-300">
                        <textarea maxLength={500} value={planVo.actionPlan} onChange={actionPlanChange} style={{ width: '100%' }}/>
                      </td>
                    </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <div style={{ height: '100px' }} />
            <div className="footer">
              <Button onClick={() => navigate(-1) }>返回合计</Button>
              <Button color="primary" fill="solid" onClick={handleSave} loading={loading}>
                {
                  hospitalList.findIndex(item => item.hospitalid === currentHospital.hospitalid) === hospitalList.length - 1 ?
                    '保存' :
                    '保存上报并填写下一个'
                }
              </Button>
            </div>
          </>
        )
      }
    </Wrapper>
  )
}

export default Report;
