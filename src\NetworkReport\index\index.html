<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no">
  <script>
    (function(win, export_obj) {
      win['LogAnalyticsObject'] = export_obj;
      if (!win[export_obj]) {
        function _collect() {
          _collect.q.push(arguments);
        }
        _collect.q = _collect.q || [];
        win[export_obj] = _collect;
      }
      win[export_obj].l = +new Date();
    })(window, 'collectEvent');

    window.collectEvent('init', {
      app_id: window.location.host === 'sales-networkreport.nbims.com.cn' ? 10000013 : 10000012, // 参考2.1节获取，注意类型是number而非字符串
      channel_domain: 'https://snssdk.genscigroup.com', // 设置私有化部署数据上送地址，参考2.2节获取
      log: true, // true:开启日志，false:关闭日志
      autotrack: true, // 全埋点开关，true开启，false关闭
      enable_stay_duration: true,
      spa: true
    });
    // 此处可添加设置uuid、设置公共属性等代码
    window.collectEvent('start'); // 通知SDK设置完毕，可以真正开始发送事件了
  </script>
  <script async src="https://datarangers.genscigroup.com/minio.byterangers.onpremise.docor.static/collect-privity-v5.0.0.js"></script>
  <title>战报小程序  </title>
</head>
<body>
<div id="app" ></div>
</body>

</html>
