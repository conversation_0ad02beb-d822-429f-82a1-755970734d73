import React, {useEffect, useState} from 'react';
import {Routes, Route} from "react-router";
import { useNavigate } from "react-router-dom";

import {useSearchParams} from "react-router-dom";
import {setToken, isAuth}  from '../../../utils/auth'
import Board from './Board';
import __Report from './__Report';
import Report from './Report';
import {http1} from "../../../utils/network";
import styled from 'styled-components';
import PadBoard from './pad/Board';
import PadReport from './pad/Report';
import dayjs from "dayjs";
import empty from './empty.png';
import {LeftOutline} from "antd-mobile-icons";
import SubmitBoard from "./SubmitBoard";

const Wrapper = styled.div`
	.user-header {
    height: 24px;
    /* 自动布局 */
    display: flex;
		padding: 32px 16px;
		justify-content: space-between;

		color: #4E595E;
		/* 14/CN-Regular */
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 22px;
		.name {
			font-size: 14px;
			padding-right: 20px;
			height: 20px;
			line-height: 20px;
		}
		.empNo {
      height: 20px;
			line-height: 20px;
      background: rgb(232, 243, 255);
			color: rgb(22, 93, 255);
			padding: 0 4px;
      border-radius: 2px;
		}
	}
`;


const PedWeekHome = () => {
	const navigate = useNavigate();
	let [searchParams] = useSearchParams()
	const [userInfo, setUserInfo] = useState({});
	const [roleInfo, setRoleInfo] = useState({});
	const [done, setDone] = useState(false);

	const readLevel = +searchParams.get('readLevel')
	const deptname = searchParams.get('deptname')
	const empName = searchParams.get('empName')

	useEffect(() => {
		window.document.title = '月新增预估'
		// getUserInfo()
		getRole();
	}, [])

	let [tokenState] = useSearchParams()
	let token = tokenState.get('token');
	const channel = tokenState.get('channel');
	if(token){
		setToken(token)
	}
	if(channel) {
		localStorage.setItem('channel', channel)
	}else {
		localStorage.removeItem('channel')
	}

	const getRole = async () => {
		/*
		* area (string, optional): 地区 ,
			dist (string, optional): 大区 ,
			empName (string, optional): 姓名 ,
			empNo (string, optional): 工号 ,
			level (integer, optional): 等级 , 6地区 5大区 4区域总
			region (string, optional): 区域 ,
			warZone (string, optional): 战区
		* */
		let res = {};
		if(searchParams.get('tbaDeptcode')) {
			res = await http1.get(`/estimate/query/role?estDateType=month&deptcode=${searchParams.get('tbaDeptcode') || ''}`) || {};
		}else {
			res = await http1.get(`/estimate/query/role?estDateType=month`) || {};
		}

		res.currentLevel = res.level
		if(readLevel) {
			res.level = readLevel
		}
		if(searchParams.get('deptcode')) {
			res.deptcode = searchParams.get('deptcode')
		}
		setRoleInfo(res)
		setDone(true)
	}

	return (
		roleInfo.level ? <Wrapper>
			<div className="user-header">
				<div className="area">
					{roleInfo.warZone}
					{
						roleInfo.region && (
							<>
								/ {roleInfo.region}
							</>
						)
					}
					{
						roleInfo.dist && (
							<>
								/ {roleInfo.dist}
							</>
						)
					}
					{
						roleInfo.area && (
							<>
								/ {roleInfo.area}
							</>
						)
					}
				</div>
				<div className="time">
					当前人：{empName || roleInfo.empName}&nbsp;&nbsp;
					当前时间：{dayjs().format('YYYY-MM-DD')}
				</div>
			</div>
				{
					!!readLevel && (
						<div style={{ cursor: 'pointer', marginLeft: 16, fontSize: '20px' }} onClick={() => navigate(-1)}>
							<LeftOutline style={{ display: 'inline-block' }} />返回
						</div>
					)
				}
			{
				<div style={{ padding: '16px' }}>
					<Routes>
						<Route path={'board'} element={<Board roleInfo={roleInfo} readLevel={readLevel} reloadRole={getRole} />}></Route>
						<Route path={'submitBoard'} element={<SubmitBoard roleInfo={roleInfo} readLevel={readLevel} reloadRole={getRole} />}></Route>
						<Route path={'padBoard'} element={<PadBoard />}></Route>
						<Route path={'padReport'} element={<PadReport />}></Route>
						<Route path={'boardTBA'} element={<Board />}></Route>
						<Route path={'report'} element={<Report />}></Route>
					</Routes>
				</div>
			}
		</Wrapper>
			:
			(done && <img src={empty} alt="" style={{ margin: '0 auto' }}/>)
	)
}

export default PedWeekHome
