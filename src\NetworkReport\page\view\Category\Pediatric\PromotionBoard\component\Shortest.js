import React from "react";
import styled from 'styled-components';
import ItemWrapper from "./ItemWrapper";
import {BoldText, isEmpty, realNum, RedBoldText} from './../index';

const Wrapper = styled.div`
	.title {
		padding: 0 10px 10px 10px;
	}
	.sub-title {
		border: 1px solid #bbb;
		border-left: none;
		border-right: none;
    text-align: center;
    .top {
			font-size: 16px;
		}
    .top, .bot {
      height: 25px;
      line-height: 25px;
		}
	}
`;

const Shortest = ({ data = {} }) => {
	const _data1 = data['最短任职时间新患数据'] || {};
	const _data2 = data['最短任职时间纯销数据'] || {};
	return (
		<ItemWrapper>
			<Wrapper>
				<div className="title">
					最短任职时间内是否达标
				</div>
				<div className="sub-title">
					<div className="top">新患</div>
					<div className="bot">
						考核期——新患达成率≥85%
					</div>
				</div>
			</Wrapper>
			<div className="content" style={{ paddingBottom: 10 }}>
				<div className="item">
					<div className="title">新患差距</div>
					<BoldText isBold isWarning={(_data1.achievementRate * 100) < 85}>
						<div className="value">{!isEmpty(_data1.achievementRate) && ((_data1.achievementRate * 100) < 85 ? realNum(_data1.goalGap) : '达成') || '-'}</div>
					</BoldText>
				</div>
				<div className="item">
					<div className="title">指标</div>
					<div className="value">{realNum(_data1.index)}</div>
				</div>
				<div className="item">
					<div className="title">新患达成</div>
					<div className="value">{realNum(_data1.actualAch)}</div>
				</div>
				<div className="item">
					<div className="title">新患达成率</div>
					<BoldText isBold={false} isWarning={(_data1.achievementRate * 100) < 85}>
						<div className="value">{realNum(_data1.achievementRate, true)}</div>
					</BoldText>
				</div>
			</div>
			<Wrapper>
				<div className="sub-title">
					<div className="top">纯销（万）</div>
					<div className="bot">
						考核期——纯销达成率≥100%
					</div>
				</div>
			</Wrapper>
			<div className="content">
				<div className="item">
					<div className="title">纯销差距</div>
					<BoldText isBold isWarning={(_data2.achievementRate * 100) < 100}>
						<div className="value">{!isEmpty(_data2.achievementRate) && ((_data2.achievementRate * 100) < 100 ? realNum(_data2.goalGap, false, true) : '达成') || '-'}</div>
					</BoldText>
				</div>
				<div className="item">
					<div className="title">指标</div>
					<div className="value">{realNum(_data2.index, false, true)}</div>
				</div>
				<div className="item">
					<div className="title">纯销实际达成</div>
					<div className="value">{realNum(_data2.actualAch, false, true)}</div>
				</div>
				<div className="item">
					<div className="title">纯销达成率</div>
					<BoldText isBold={false} isWarning={(_data2.achievementRate * 100) < 100}>
						<div className="value">{realNum(_data2.achievementRate, true)}</div>
					</BoldText>
				</div>
			</div>
		</ItemWrapper>
	)
}

export default Shortest;
