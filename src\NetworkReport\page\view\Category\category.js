import React, { useEffect } from "react";
import "./category.css";
import { Routes, Route, useParams } from "react-router";

import { useSearchParams } from "react-router-dom";
import { setToken, getToken, isAuth } from "../../../utils/auth";
import Neuroendocrine from "./Neuroendocrine/neuroendocrine";

import ImmuneDaily from "./ImmuneDaily/immuneDaily";

import AestheticMedicine from "./AestheticMedicine/aestheticMedicine";
import Reproduction from "./Reproduction/reproduction";
import Gynaecology from "./Gynaecology/gynaecology";
import Merchants from "./Merchants/merchants";

//医美系列
import AestheticMedicineDayReportUpload from "../../../components/private/Category/AestheticMedicine/AestheticMedicineDayReportUpload/aestheticMedicineDayReportUpload";
import AestheticMedicineDayReportUploadDataCreate from "../../../components/private/Category/AestheticMedicine/AestheticMedicineDayReportUpload/DataCreate/dataCreate";
import AestheticMedicineDayReportUploadDataUpdate from "../../../components/private/Category/AestheticMedicine/AestheticMedicineDayReportUpload/DataUpdate/dataUpdate";
import AestheticMedicineDayReportUploadDataQuery from "../../../components/private/Category/AestheticMedicine/AestheticMedicineDayReportUpload/DataQuery/dataQuery";
import AestheticMedicineDayReportUploadDataBoard from "../../../components/private/Category/AestheticMedicine/AestheticMedicineDayReportUpload/DataBoard/dataBoard";
import AestheticMedicineWeekPredictUpload from "../../../components/private/Category/AestheticMedicine/AestheticMedicineWeekPredictUpload/aestheticMedicineWeekPredictUpload";
//妇科系列
import GynaecologyWeekReportUpload from "../../../components/private/Category/Gynaecology/GynaecologyWeekReportUpload/gynaecologyWeekReportUpload";
import GynaecologyWeekReportUploadDataCreate from "../../../components/private/Category/Gynaecology/GynaecologyWeekReportUpload/DataCreate/dataCreate";
import GynaecologyWeekReportUploadDataUpdate from "../../../components/private/Category/Gynaecology/GynaecologyWeekReportUpload/DataUpdate/dataUpdate";
import GynaecologyWeekReportUploadDataQuery from "../../../components/private/Category/Gynaecology/GynaecologyWeekReportUpload/DataQuery/dataQuery";
import GynaecologyWeekReportUploadDataBoard from "../../../components/private/Category/Gynaecology/GynaecologyWeekReportUpload/DataBoard/dataBoard";
//招商系列
import MerchantsAgentSignUpload from "../../../components/private/Category/Merchants/MerchantsAgentSignUpload/merchantsAgentSignUpload";
import MerchantsAgentSignUploadDataCreate from "../../../components/private/Category/Merchants/MerchantsAgentSignUpload/DataCreate/dataCreate";
import MerchantsAgentSignUploadDataUpdate from "../../../components/private/Category/Merchants/MerchantsAgentSignUpload/DataUpdate/dataUpdate";
import MerchantsAgentSignUploadDataQuery from "../../../components/private/Category/Merchants/MerchantsAgentSignUpload/DataQuery/dataQuery";
import MerchantsAgentSignUploadDataBoard from "../../../components/private/Category/Merchants/MerchantsAgentSignUpload/DataBoard/dataBoard";
import MerchantsDirectAgentSignUpload from "../../../components/private/Category/Merchants/MerchantsDirectAgentSignUpload/merchantsDirectAgentSignUpload";
import MerchantsDayReportJFNUpload from "../../../components/private/Category/Merchants/MerchantsDayReportJFNUpload/merchantsDayReportJFNUpload";
import MerchantsDayReportJFNUploadDataCreate from "../../../components/private/Category/Merchants/MerchantsDayReportJFNUpload/DataCreate/dataCreate";
import MerchantsDayReportJFNUploadDataUpdate from "../../../components/private/Category/Merchants/MerchantsDayReportJFNUpload/DataUpdate/dataUpdate";
import MerchantsDayReportJFNUploadDataQuery from "../../../components/private/Category/Merchants/MerchantsDayReportJFNUpload/DataQuery/dataQuery";
import MerchantsDayReportJFNUploadDataBoard from "../../../components/private/Category/Merchants/MerchantsDayReportJFNUpload/DataBoard/dataBoard";
//神经内分泌系列
import NeuroendocrineDayReportNewCustomer from "../../../components/private/Category/Neuroendocrine/NeuroendocrineDayReportNewCustomer/neuroendocrineDayReportNewCustomer";
import NeuroendocrineDayReportNewPatient from "../../../components/private/Category/Neuroendocrine/NeuroendocrineDayReportNewPatient/neuroendocrineDayReportNewPatient";
import NeuroendocrineDayReportUpload from "../../../components/private/Category/Neuroendocrine/NeuroendocrineDayReportUpload/neuroendocrineDayReportUpload";
import NeuroendocrineDayReportUploadDataCreate from "../../../components/private/Category/Neuroendocrine/NeuroendocrineDayReportUpload/DataCreate/dataCreate";
import NeuroendocrineDayReportUploadDataCreateCustomer from "../../../components/private/Category/Neuroendocrine/NeuroendocrineDayReportUpload/DataCreateCustomer/dataCreate";
import NeuroendocrineDayReportUploadDataUpdate from "../../../components/private/Category/Neuroendocrine/NeuroendocrineDayReportUpload/DataUpdate/dataUpdate";
import NeuroendocrineDayReportUploadDataUpdateCustomer from "../../../components/private/Category/Neuroendocrine/NeuroendocrineDayReportUpload/DataUpdate/dataUpdateCustomer";
import NeuroendocrineDayReportUploadDataQuery from "../../../components/private/Category/Neuroendocrine/NeuroendocrineDayReportUpload/DataQuery/dataQuery";
import NeuroendocrineDayReportUploadDataBoard from "../../../components/private/Category/Neuroendocrine/NeuroendocrineDayReportUpload/DataBoard/dataBoard";
import NeuroendocrineWeekReportJSMPharmacy from "../../../components/private/Category/Neuroendocrine/NeuroendocrineWeekReportJSMPharmacy/neuroendocrineWeekReportJSMPharmacy";
import NeuroendocrineWeekReportJSMPharmacyDataCreate from "../../../components/private/Category/Neuroendocrine/NeuroendocrineWeekReportJSMPharmacy/DataCreate/dataCreate";
import NeuroendocrineWeekReportJSMPharmacyDataUpdate from "../../../components/private/Category/Neuroendocrine/NeuroendocrineWeekReportJSMPharmacy/DataUpdate/dataUpdate";
import NeuroendocrineWeekReportJSMPharmacyDataQuery from "../../../components/private/Category/Neuroendocrine/NeuroendocrineWeekReportJSMPharmacy/DataQuery/dataQuery";
import NeuroendocrineWeekReportJSMPharmacyDataBoard from "../../../components/private/Category/Neuroendocrine/NeuroendocrineWeekReportJSMPharmacy/DataBoard/dataBoard";

// 免疫
//神经内分泌系列
import ImmuneDailyReportNewCustomer from "../../../components/private/Category/ImmuneDaily/ImmuneDailyReportNewCustomer/immuneDailyReportNewCustomer";
import ImmuneDailyReportNewPatient from "../../../components/private/Category/ImmuneDaily/ImmuneDailyReportNewPatient/immuneDailyReportNewPatient";
import ImmuneDailyReportUpload from "../../../components/private/Category/ImmuneDaily/ImmuneDailyReportUpload/immuneDailyReportUpload";
import ImmuneDailyReportUploadDataCreate from "../../../components/private/Category/ImmuneDaily/ImmuneDailyReportUpload/DataCreate/dataCreate";
import ImmuneDailyReportUploadDataCreateCustomer from "../../../components/private/Category/ImmuneDaily/ImmuneDailyReportUpload/DataCreateCustomer/dataCreate";
import ImmuneDailyReportUploadDataUpdate from "../../../components/private/Category/ImmuneDaily/ImmuneDailyReportUpload/DataUpdate/dataUpdate";
import ImmuneDailyReportUploadDataUpdateCustomer from "../../../components/private/Category/ImmuneDaily/ImmuneDailyReportUpload/DataUpdate/dataUpdateCustomer";
import ImmuneDailyReportUploadDataQuery from "../../../components/private/Category/ImmuneDaily/ImmuneDailyReportUpload/DataQuery/dataQuery";
import ImmuneDailyReportUploadDataBoard from "../../../components/private/Category/ImmuneDaily/ImmuneDailyReportUpload/DataBoard/dataBoard";

//生殖系列
import ReproductionWeekReportUpload from "../../../components/private/Category/Reproduction/ReproductionWeekReportUpload/reproductionWeekReportUpload";
import ReproductionWeekReportUploadDataCreate from "../../../components/private/Category/Reproduction/ReproductionWeekReportUpload/DataCreate/dataCreate";
import ReproductionWeekReportUploadDataUpdate from "../../../components/private/Category/Reproduction/ReproductionWeekReportUpload/DataUpdate/dataUpdate";
import ReproductionWeekReportUploadDataQuery from "../../../components/private/Category/Reproduction/ReproductionWeekReportUpload/DataQuery/dataQuery";
import ReproductionWeekReportUploadDataBoard from "../../../components/private/Category/Reproduction/ReproductionWeekReportUpload/DataBoard/dataBoard";
import SubmitList from "../../../components/private/Category/Reproduction/ReproductionWeekReportUpload/SubmitList";
import SubmitDetail from "../../../components/private/Category/Reproduction/ReproductionWeekReportUpload/SubmitDetail";
// 儿科系列
import PediatricDayReportUpload from "../../../components/private/Category/Pediatric/PediatricDayReportUpload/pediatricDayReportUpload";
import PediatricDayReportUploadDataCreate from "../../../components/private/Category/Pediatric/PediatricDayReportUpload/DataCreate/dataCreate";
import PediatricDayReportUploadDataUpdate from "../../../components/private/Category/Pediatric/PediatricDayReportUpload/DataUpdate/dataUpdate";
import PediatricDayReportUploadDataQuery from "../../../components/private/Category/Pediatric/PediatricDayReportUpload/DataQuery/dataQuery";
import PediatricDayReportUploadDataBoard from "../../../components/private/Category/Pediatric/PediatricDayReportUpload/DataBoard/dataBoard";
import PediatricDayReportUploadReportBoard from "../../../components/private/Category/Pediatric/PediatricDayReportUpload/ReportBoard/reportBoard";
import PediatricDayReportDataReport from "../../../components/private/Category/Pediatric/PediatricDayReportUpload/DataReport";
import PediatricsPromotionBoard from "./Pediatric/PromotionBoard";

// 客户分析表
import CustomUploadRouter from "./CustomAnalysis/uploadRoute";
import CustomAnalysisEdit from "./CustomAnalysis/Upload/index";
import CustomAnalysisList from "./CustomAnalysis/DataList/index";

const Category = () => {
  let [tokenState, setTokenState] = useSearchParams();
  let token = tokenState.get("token");
  const channel = tokenState.get("channel");
  if (token) {
    // setToken('eyJhbGciOiJIUzUxMiJ9.eyJyb2xlIjoic2FsZXMiLCJlbXBObyI6IkdTMjc0NyIsImV4cCI6MTY2MzMwMDE2OX0.2E7LpgEY43DYl6gqrVkqu02ik-tQrn7p2WBhcUR8kHM1O8RNIPINUuGmCw6muV5K7KYXCknghS8gno1aSOsUvw')
    setToken(token);
  }
  if (channel) {
    localStorage.setItem("channel", channel);
  } else {
    localStorage.removeItem("channel");
  }

  return (
    <div id={"category"}>
      {isAuth() || token ? (
        <Routes>
          {/*医美*/}
          <Route path={"aestheticMedicine/*"} element={<AestheticMedicine />}>
            {/*医美日报上传*/}
            <Route
              path={"aestheticMedicineDayReportUpload"}
              element={<AestheticMedicineDayReportUpload />}
            >
              <Route
                path={"dataCreate"}
                element={<AestheticMedicineDayReportUploadDataCreate />}
              />
              <Route
                path={"dataUpdate"}
                element={<AestheticMedicineDayReportUploadDataUpdate />}
              />
              <Route
                path={"dataQuery"}
                element={<AestheticMedicineDayReportUploadDataQuery />}
              />
              <Route
                path={"dataBoard"}
                element={<AestheticMedicineDayReportUploadDataBoard />}
              />
            </Route>
            {/*医美周预估上传*/}
            <Route
              path={"aestheticMedicineWeekPredictUpload"}
              element={<AestheticMedicineWeekPredictUpload />}
            />
          </Route>
          {/*妇科*/}
          <Route path={"gynaecology/*"} element={<Gynaecology />}>
            {/*妇科周报上传*/}
            <Route
              path={"gynaecologyWeekReportUpload/*"}
              element={<GynaecologyWeekReportUpload />}
            >
              <Route
                path={"dataCreate"}
                element={<GynaecologyWeekReportUploadDataCreate />}
              />
              <Route
                path={"dataUpdate"}
                element={<GynaecologyWeekReportUploadDataUpdate />}
              />
              <Route
                path={"dataQuery"}
                element={<GynaecologyWeekReportUploadDataQuery />}
              />
              <Route
                path={"dataBoard"}
                element={<GynaecologyWeekReportUploadDataBoard />}
              />
            </Route>
          </Route>
          {/*招商*/}
          <Route path={"merchants/*"} element={<Merchants />}>
            {/*招商代理上传*/}
            <Route
              path={"merchantsAgentSignUpload/*"}
              element={<MerchantsAgentSignUpload />}
            >
              <Route
                path={"dataCreate"}
                element={<MerchantsAgentSignUploadDataCreate />}
              />
              <Route
                path={"dataUpdate"}
                element={<MerchantsAgentSignUploadDataUpdate />}
              />
              <Route
                path={"dataQuery"}
                element={<MerchantsAgentSignUploadDataQuery />}
              />
              <Route
                path={"dataBoard"}
                element={<MerchantsAgentSignUploadDataBoard />}
              />
            </Route>
            {/*招商直营代理上传*/}
            <Route
              path={"merchantsDirectAgentSignUpload"}
              element={<MerchantsDirectAgentSignUpload />}
            />
            {/*金扶宁日报*/}
            <Route
              path={"merchantsDayReportJFNUpload/*"}
              element={<MerchantsDayReportJFNUpload />}
            >
              <Route
                path={"dataCreate"}
                element={<MerchantsDayReportJFNUploadDataCreate />}
              />
              <Route
                path={"dataUpdate"}
                element={<MerchantsDayReportJFNUploadDataUpdate />}
              />
              <Route
                path={"dataQuery"}
                element={<MerchantsDayReportJFNUploadDataQuery />}
              />
              <Route
                path={"dataBoard"}
                element={<MerchantsDayReportJFNUploadDataBoard />}
              />
            </Route>
          </Route>
          {/*神经内分泌*/}
          <Route path={"neuroendocrine/*"} element={<Neuroendocrine />}>
            {/*神经内分泌日报新客*/}
            <Route
              path={"neuroendocrineDayReportNewCustomer"}
              element={<NeuroendocrineDayReportNewCustomer />}
            />
            {/*神经内分泌日报新患*/}
            <Route
              path={"neuroendocrineDayReportNewPatient"}
              element={<NeuroendocrineDayReportNewPatient />}
            />
            {/*神经内分泌日报上传*/}
            <Route
              path={"neuroendocrineDayReportUpload/*"}
              element={<NeuroendocrineDayReportUpload />}
            >
              <Route
                path={"dataCreate"}
                element={<NeuroendocrineDayReportUploadDataCreate />}
              />
              <Route
                path={"dataCreateCustomer"}
                element={<NeuroendocrineDayReportUploadDataCreateCustomer />}
              />
              <Route
                path={"dataUpdate"}
                element={<NeuroendocrineDayReportUploadDataUpdate />}
              />
              <Route
                path={"dataUpdateCustomer"}
                element={<NeuroendocrineDayReportUploadDataUpdateCustomer />}
              />
              <Route
                path={"dataQuery"}
                element={<NeuroendocrineDayReportUploadDataQuery />}
              />
              <Route
                path={"dataBoard"}
                element={<NeuroendocrineDayReportUploadDataBoard />}
              />
            </Route>
            {/*神经内分泌周报金斯明药店*/}
            <Route
              path={"neuroendocrineWeekReportJSMPharmacy/*"}
              element={<NeuroendocrineWeekReportJSMPharmacy />}
            >
              <Route
                path={"dataCreate"}
                element={<NeuroendocrineWeekReportJSMPharmacyDataCreate />}
              />
              <Route
                path={"dataUpdate"}
                element={<NeuroendocrineWeekReportJSMPharmacyDataUpdate />}
              />
              <Route
                path={"dataQuery"}
                element={<NeuroendocrineWeekReportJSMPharmacyDataQuery />}
              />
              <Route
                path={"dataBoard"}
                element={<NeuroendocrineWeekReportJSMPharmacyDataBoard />}
              />
            </Route>
          </Route>

          {/*免疫线*/}
          <Route path={"immuneDaily/*"} element={<ImmuneDaily />}>
            {/*免疫线日报新客*/}
            <Route
              path={"immuneDailyReportNewCustomer"}
              element={<ImmuneDailyReportNewCustomer />}
            />
            {/*神经内分泌日报新患*/}
            <Route
              path={"immuneDailyReportNewPatient"}
              element={<ImmuneDailyReportNewPatient />}
            />
            {/*神经内分泌日报上传*/}
            <Route
              path={"immuneDailyReportUpload/*"}
              element={<ImmuneDailyReportUpload />}
            >
              <Route
                path={"dataCreate"}
                element={<ImmuneDailyReportUploadDataCreate />}
              />
              <Route
                path={"dataCreateCustomer"}
                element={<ImmuneDailyReportUploadDataCreateCustomer />}
              />
              <Route
                path={"dataUpdate"}
                element={<ImmuneDailyReportUploadDataUpdate />}
              />
              <Route
                path={"dataUpdateCustomer"}
                element={<ImmuneDailyReportUploadDataUpdateCustomer />}
              />
              <Route
                path={"dataQuery"}
                element={<ImmuneDailyReportUploadDataQuery />}
              />
              <Route
                path={"dataBoard"}
                element={<ImmuneDailyReportUploadDataBoard />}
              />
            </Route>
          </Route>

          {/*生殖*/}
          <Route path={"reproduction/*"} element={<Reproduction />}>
            {/*生殖周报上传*/}
            <Route
              path={"reproductionWeekReportUpload/*"}
              element={<ReproductionWeekReportUpload />}
            >
              <Route
                path={"dataCreate"}
                element={<ReproductionWeekReportUploadDataCreate />}
              />
              <Route
                path={"dataUpdate"}
                element={<ReproductionWeekReportUploadDataUpdate />}
              />
              <Route
                path={"dataQuery"}
                element={<ReproductionWeekReportUploadDataQuery />}
              />
              <Route
                path={"dataBoard"}
                element={<ReproductionWeekReportUploadDataBoard />}
              />
              <Route path={"submitList"} element={<SubmitList />} />
              <Route path={"submitDetail"} element={<SubmitDetail />} />
            </Route>
          </Route>
          {/*儿科*/}
          {/*<Route path={'pediatric/*'} element={<Pediatric />}>*/}
          {/*    /!*儿科日战报上传*!/*/}
          {/*    <Route path={'pediatricDayReportUpload'} element={<PediatricDayReportUpload />}>*/}
          {/*        <Route path={'dataCreate'} element={<PediatricDayReportUploadDataCreate />}/>*/}
          {/*        <Route path={'dataUpdate'} element={<PediatricDayReportUploadDataUpdate />} />*/}
          {/*        <Route path={'dataQuery'}  element={<PediatricDayReportUploadDataQuery />} />*/}
          {/*        <Route path={'dataBoard'}  element={<PediatricDayReportUploadDataBoard />} />*/}
          {/*        <Route path={'reportBoard'}  element={<PediatricDayReportUploadReportBoard />} />*/}
          {/*        <Route path={'dataReport'}  element={<PediatricDayReportDataReport />} />*/}
          {/*    </Route>*/}
          {/*							<Route path={'promotionBoard'} element={<PediatricsPromotionBoard />} />*/}
          {/*							/!*<Route path={'test'} element={<Test />} />*!/*/}
          {/*</Route>*/}
          {/* 客户分析表 */}
          {/*<Route path={'customAnalysis/*'} element={<CustomAnalysis />}>*/}
          {/*    /!* 客户分析表上传 *!/*/}
          {/*    <Route path={'customAnalysisUpload'} element={<CustomUploadRouter />}>*/}
          {/*        <Route path={'dataCreate'} element={<CustomAnalysisEdit />} />*/}
          {/*        <Route path={'dataUpdate'} element={<CustomAnalysisEdit />} />*/}
          {/*        <Route path={'dataQuery'} element={<CustomAnalysisList />} />*/}
          {/*    </Route>*/}
          {/*</Route>*/}
        </Routes>
      ) : null}
    </div>
  );
};

export default Category;
