import React, {useEffect, useState} from 'react';
import styled from 'styled-components';
import Tabs from "./Tabs";
import {http1} from "../../../utils/network";
import {useNavigate, useSearchParams} from "react-router-dom";
import {To<PERSON>, Button} from 'antd-mobile';
import ApprovalBar from "./ApprovalBar";
import dayjs from "dayjs";
import SubmitTabs from "./SubmitTabs";
import SubmitBar from "./SubmitBar";
import ApprovalProcess from "./ApprovalProcess";

const Wrapper = styled.div`
  padding-bottom: 100px;
  .light {
    color: #2551F2;
  }
  .merge-div {
    display: inline-block;
    border-bottom: 1px solid rgb(148, 163, 184);
  }

  table td {
    padding: 7px 16px;
    text-align: center;
  }

  table thead {
    background-color: #F3F4F5;
  }

  table thead tr th {
    padding: 7px 16px;
    font-size: 14px;
  }

  .zhou {
    color: #2551F2;
    /* 16/CN-Medium */
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    margin: 32px 0 16px 0;
  }

  .dept {
    color: #2551F2;
    /* 16/CN-Medium */
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    margin-bottom: 16px;
  }
`;

const Board = props => {
  const navigate = useNavigate();

  const [searchParams] = useSearchParams()

  const readLevelQuery = +searchParams.get('readLevel') || ''
  const empNoQuery = searchParams.get('empNo') || ''
  const status = searchParams.get('status') || ''

  const {roleInfo, readLevel} = props;

  const {deptcode, estDate = [], empNo, level} = roleInfo || {};

  const [tabKey, setTabKey] = useState('');
  const [planData, setPlanData] = useState({}); // 地区经理 周计划
  const [hospitalBoardData, setHospitalBoardData] = useState([]); // 地区经理 机构看板
  const [hospitalBoardDataFull, setHospitalBoardDataFull] = useState([]); // 地区经理 机构看板
  const [deptBoardData, setDeptBoardData] = useState([]); // 地区经理 部门看板
  const [deptBoardDataFull, setDeptBoardDataFull] = useState([]); // 地区经理 部门看板
  const [hospitalPlanDataFull, setHospitalPlanDataFull] = useState([]); // 机构视图 计划看板
  const [deptBoardTr, setDeptBoardTr] = useState([]); // 大区、区总 =》 动态部门看板 表头
  const [userBoardData, setUserBoardData] = useState([]); // 大区、区总 =》 动态部门看板 表头
  const [approvalLog, setApprovalLog] = useState([]); // 地区审批日志
  const [approvalFlow, setApprovalFlow] = useState([]); // 地区审批记录

  useEffect(() => {
    if (roleInfo && roleInfo.deptcode) {
      if (tabKey === '1') {
        getHospitalData();
        getHospitalPlanData()
        if(level === 6) {
          getApprovalLog();
        }
      }
    }
  }, [roleInfo, tabKey])


  // 获取几口 计划看板 td数据
  const currentHospitalPlanTdRender = (monthIndex, tag, type) => {
    try {
      return hospitalPlanDataFull[monthIndex][tag][type]
    } catch (e) {
      return 0;
    }
  }
  // 地区经理获取审批记录
  const getApprovalLog = async () => {
    try {
      const res = await http1.post(`/pediatrics/approval/query/log?deptCode=${deptcode}`) || []
      setApprovalLog(res)
    } catch (e) {
      // e
    }
  }

  const getApprovalFlow = async () => {
    try {
      const res = await http1.post(`/pediatrics/approval/query/flow?deptCode=${deptcode}`) || []
      setApprovalFlow(res)
    } catch (e) {
      // e
    }
  }

  // 获取机构看板 计划
  const getHospitalPlanData = async () => {
    try {
      const res = await http1.post(`/estimate/query/board/plan/month`,  {
        empNo,
        estDateList: estDate,
        deptcode,
        estDateType: 'month'
      }) || [];
      setHospitalPlanDataFull(res)

    } catch (e) {
      //
    }
  }

  // 获取科室看板数据
  const getDeptData = async () => {
    try {
      Toast.show({
        icon: 'loading',
        duration: 0
      })
      const res = await http1.post('/estimate/query/board/dept/month', {
        empNo,
        estDateList: estDate,
        deptcode,
        estDateType: 'month'
      }) || [];

      // 科室列表
      setDeptBoardDataFull(res || []);
      setDeptBoardData(res[0] && res[0].boardVos || []);

        // 科室列表
        // setDeptBoardData(rowsData);
        // 地区、大区 设置 科室看板动态表头
        setDeptBoardTr(res)
        // 周计划
        // setPlanData(data.planVo || {})


      Toast.clear();
    } catch (e) {
      Toast.clear();
    }

  }

  // 获取机构看板数据
  const getHospitalData = async () => {
    try {
      Toast.show({
        icon: 'loading',
        duration: 0
      })
      const res = await http1.post('/estimate/query/board/hospital/month', {
        empNo,
        estDateList: estDate,
        deptcode,
        estDateType: 'month'
      }) || [];

      const stringhosTypeVoMap = res[0] && res[0].stringhosTypeVoMap || {};
      const planVo = res.planVo || {};

      const arr = [];
      for (const key in stringhosTypeVoMap) {
        arr.push({
          ...stringhosTypeVoMap[key],
          name: key
        })
      }
      setPlanData(planVo);
      setHospitalBoardData(arr)
      setHospitalBoardDataFull(res)
      Toast.clear()
    } catch (e) {
      Toast.clear();
    }
  }

  // 获取人员视图 看板数据
  const getuserBoardData = async () => {
    try {
      Toast.show({
        icon: 'loading',
        duration: 0
      })
      const res = await http1.post('/estimate/query/board/user/month', {
        empNo,
        estDateList: roleInfo.estDate,
        deptcode,
        estDateType: 'month'
      })
      setUserBoardData(res || []);
      Toast.clear()
    } catch (e) {
      Toast.clear()
    }
  }

  const tabOnChange = tabKey => {
    setTabKey(tabKey)
  }

  const deptBoardTrTdRender = (type) => {
    return (
      deptBoardTr.map((item, colIndex) => {
        const planVo = item.planVo || {};
        return (
          <td key={colIndex + item.deptcode + type} className="border border-slate-300">
            {planVo[type]}
          </td>
        )
      })
    )
  }

  // 科室看板 计划 td 取值
  const deptBoardTrTdRender4 = (type) => {
    return (
      deptBoardDataFull.map((item, colIndex) => {
        const planVo = item.planVo || {};
        return (
          <td key={colIndex + item.deptcode + type} className="border border-slate-300">
            {planVo[type]}
          </td>
        )
      })
    )
  }

  // 合计
  const total = (data) => {
    let newPat = 0;
    let longPat = 0;
    let longRate = '0%';

    data.forEach(item => {
      newPat = +item.newPat + newPat;
      longPat = +item.longPat + longPat;
    })
    if(newPat) {
      longRate = ((longPat / newPat) * 100).toFixed(2) + '%';
    }

    return {
      newPat,
      longPat,
      longRate
    }
  }

  // 大区-地区 跳转到 机构详情
  const handleToHospital = (item) => {
    if(item.name === '合计') {
      return false
    }else {
      const deptcode = searchParams.get('deptcode');
      navigate(`/pediatricsMonthEstimate/report?name=${item.name}&deptcode=${deptcode}&estDate=${estDate}&empNo=${empNoQuery}&readLevel=${readLevelQuery}`)
    }
  }

  // 机构看板 td数据
  const currentHospitalTdValue = (hospitalItem, estDate, type) => {
    try {
      const currentEstDateRow = hospitalBoardDataFull.find(d => d.estDate === estDate) && hospitalBoardDataFull.find(d => d.estDate === estDate).stringhosTypeVoMap || {};
      const stringhosTypeVo = currentEstDateRow[hospitalItem.name] || {};

      return stringhosTypeVo[type]
    } catch (e) {
      return 0
    }
  }

  // 机构看板 计划 td数据
  const currentHosPlanValue = (name, monthIndex, type) => {
    try {
      const value = hospitalPlanDataFull[monthIndex]['stringhosTypeVoMap'][name][type]
      return value
    } catch (e) {
      return 0
    }
  }

  // 机构看板-计划
  const deptBoardTrTdRender3 = (type) => {
    return (
      estDate.map((month, monthIndex) => {
        return (
          hospitalBoardData.filter(d => d.name !== '合计').map((item, colIndex) => {
            return (
              <td key={colIndex + item.name + type + month} className="border border-slate-300">
                {currentHosPlanValue(item.name, monthIndex, type)}
              </td>
            )
          })
        )
      })
    )
  }

  const planTable = () => {
    return (
      <div className="table-2">
        <div className="zhou">
          业务计划
        </div>
        <table className="border-collapse border border-slate-400 w-full" style={{ overflowY: 'auto'}}>
          <thead>
          <tr>
            <th className="border border-slate-300" rowSpan={2} colSpan={2}>月度计划3</th>
            <th className="border border-slate-300" rowSpan={2}>分类</th>
            {
              estDate.map(month => {
                return (
                  <th key={month} className="border border-slate-300" colSpan={hospitalBoardData.filter(d => d.name !== '合计').length}>{month}</th>
                )
              })
            }

          </tr>
          <tr>
            {
              estDate.map(month => {
                return (
                  hospitalBoardData.filter(d => d.name !== '合计').map((item) => {
                    return (
                      <th style={{ width: '15vw'}} key={item.name + month} className="border border-slate-300">{item.name}</th>
                    )
                  })
                )
              })
            }
          </tr>
          </thead>
          <tbody>
          <tr>
            <td className="border border-slate-300" rowSpan={7}>活动计划</td>
            <td className="border border-slate-300" rowSpan={3}>宣传类</td>
            <td className="border border-slate-300">增流活动</td>
            {deptBoardTrTdRender3('flowActivityNum')}
          </tr>
          <tr>
            <td className="border border-slate-300">帮扶活动</td>
            {deptBoardTrTdRender3('helpActivityNum')}
          </tr>
          <tr>
            <td className="border border-slate-300">品牌活动</td>
            {deptBoardTrTdRender3('brandActivityNum')}
          </tr>
          <tr>
            <td className="border border-slate-300" rowSpan={4}>学术类</td>
            <td className="border border-slate-300">科室会</td>
            {deptBoardTrTdRender3('deptMeetingNum')}
          </tr>
          <tr>
            <td className="border border-slate-300">品牌会议</td>
            {deptBoardTrTdRender3('regionMeetingNum')}
          </tr>
          <tr>
            <td className="border border-slate-300">第三方会议</td>
            {deptBoardTrTdRender3('otherMeetingNum')}
          </tr>
          <tr>
            <td className="border border-slate-300">KA活动</td>
            {deptBoardTrTdRender3('kaActivityNum')}
          </tr>
          <tr>
            <td className="border border-slate-300" rowSpan={2} colSpan={2}>用户引流</td>
            <td className="border border-slate-300">新增宝宝数</td>
            {deptBoardTrTdRender3('newBabyNum')}
          </tr>
          <tr>
            <td className="border border-slate-300">有效覆盖数(YZ)</td>
            {deptBoardTrTdRender3('reserveNum')}
          </tr>
          <tr>
            <td className="border border-slate-300" rowSpan={3} colSpan={2}>HCP改善</td>
            <td className="border border-slate-300">观念谨慎型</td>
            {deptBoardTrTdRender3('improveConceptNum')}
          </tr>
          <tr>
            <td className="border border-slate-300">合作改善型</td>
            {deptBoardTrTdRender3('improveCooperateNum')}
          </tr>
          <tr>
            <td className="border border-slate-300">长效和KH改善型</td>
            {deptBoardTrTdRender3('improveLongNum')}
          </tr>
          <tr>
            <td className="border border-slate-300" rowSpan={2} colSpan={2}>行为</td>
            <td className="border border-slate-300">大区经理拜访</td>
            {deptBoardTrTdRender3('distVisitNum')}
          </tr>
          <tr>
            <td className="border border-slate-300">大区经理协访</td>
            {deptBoardTrTdRender3('distAidVisitNum')}
          </tr>
          {/*<tr>*/}
          {/*  <td className="border border-slate-300" colSpan={2}>其他核心管理举措和行动计划</td>*/}
          {/*  {deptBoardTrTdRender2('actionPlan')}*/}
          {/*</tr>*/}
          </tbody>
        </table>
      </div>)
  }



  return (
    <Wrapper>
      <SubmitTabs roleInfo={roleInfo} onChange={tabOnChange} readLevel={readLevel} getHospitalData={getHospitalData}/>
      {
        tabKey === '1' && (
          <>
            <div className="zhou">业务目标</div>
            <table className="border-collapse border border-slate-400 w-full">
              <thead>
                <tr>
                  <th className="border border-slate-300" rowSpan={2}>机构分类</th>
                  {
                    estDate.map((item, index) => {
                      return (
                        <React.Fragment key={item + index}>
                          <th className="border border-slate-300" colSpan={3}>{item}预估数</th>
                        </React.Fragment>
                      )
                    })
                  }
                  <th className="border border-slate-300 light" colSpan={5}>合计</th>
                </tr>
                <tr>
                  {
                    estDate.map((item, index) => {
                      return (
                        <React.Fragment key={item + index}>
                          <th className="border border-slate-300">预估数</th>
                          <th className="border border-slate-300">长效预估数</th>
                          <th className="border border-slate-300">长效占比</th>
                        </React.Fragment>
                      )
                    })
                  }
                  {/* 合计 */}
                  <th className="border border-slate-300 light">预估数</th>
                  <th className="border border-slate-300 light">长效预估数</th>
                  <th className="border border-slate-300 light">长效占比</th>
                  <th className="border border-slate-300 light">有预估新增的机构数</th>
                  <th className="border border-slate-300 light">有预估新增的机构占比</th>
                </tr>
              </thead>
              <tbody>
              {
                hospitalBoardData.map(item => {
                  return (
                    <tr key={item.name}>
                      <td
                        className="border border-slate-300"
                        style={item.name === '合计' ? { color: '#2551F2' } : {}}
                        // onClick={() => handleToHospital(item)}
                      >
                        {item.name}
                      </td>
                      {
                        estDate.map((monthItem, monthIndex) => {
                          return (
                            <React.Fragment key={monthIndex + monthItem}>
                              <td className="border border-slate-300" style={item.name === '合计' ? { color: '#2551F2'} : {}}>{currentHospitalTdValue(item, monthItem, 'newPat')}</td>
                              <td className="border border-slate-300" style={item.name === '合计' ? { color: '#2551F2'} : {}}>{currentHospitalTdValue(item, monthItem, 'longPat')}</td>
                              <td className="border border-slate-300" style={item.name === '合计' ? { color: '#2551F2'} : {}}>{Number(currentHospitalTdValue(item, monthItem, 'longRate') * 100).toFixed(2)}%</td>
                            </React.Fragment>
                          )
                        })
                      }
                      <td className="border border-slate-300 light">{currentHospitalTdValue(item, '合计', 'newPat')}</td>
                      <td className="border border-slate-300 light">{currentHospitalTdValue(item, '合计', 'longPat')}</td>
                      <td className="border border-slate-300 light">{Number(currentHospitalTdValue(item, '合计', 'longRate') * 100).toFixed(2)}%</td>
                      <td className="border border-slate-300 light" style={item.name === '合计' ? { color: '#2551F2'} : {}}>{currentHospitalTdValue(item, '合计', 'newPatHos')}</td>
                      <td className="border border-slate-300 light" style={item.name === '合计' ? { color: '#2551F2'} : {}}>{Number(currentHospitalTdValue(item, '合计', 'newPatHosRate') * 100).toFixed(2)}%</td>
                    </tr>
                  )
                })
              }
              </tbody>
            </table>
            {planTable()}
            {
              (readLevel === 5 && level === 5 && status === '1') && (
                <ApprovalBar empNo={empNo} deptcode={deptcode} />
              )
            }
            {
              (level === 6 && approvalLog && !!approvalLog.length) && (
                <div>
                  <div style={{ fontWeight: 'bold', marginTop: 16 }}>审批意见</div>
                  {
                    approvalLog.map((item, index) => {
                      return (
                        <div key={index} style={{ marginBottom: 4, paddingLeft: 16 }}>
                          {dayjs(item.time).format('YYYY年MM月DD日 HH:mm:ss')},
                          {item.name}
                          {item.rejectContent && <>，驳回意见：{item.rejectContent}</>}
                        </div>
                      )
                    })
                  }
                </div>
              )
            }
          </>
        )
      }

      {
        // 审批流程
        tabKey === '2' && (
          <ApprovalProcess deptCode={roleInfo.deptcode} />
        )
      }
      {
        tabKey === '1' && (
          <SubmitBar roleInfo={roleInfo} />
        )
      }
    </Wrapper>
  )
}

export default Board;
