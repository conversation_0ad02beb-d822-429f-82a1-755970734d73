import React, {useEffect} from 'react';
import { <PERSON> } from 'react-router-dom';
import {http1} from "../../../utils/network";
import { useLocation } from 'react-router-dom';
import { Toast } from 'antd-mobile';


const Index = () => {
	const location = useLocation();
	const code = new URLSearchParams(location.search).get('code');

	useEffect(() => {
		login();
	}, [])

	const login = async () => {
		try {
			Toast.show({
				content: '授权中...请稍后',
				duration: 0,
				icon: 'loading'
			})
			const res = await http1.post('/public/feishu/logIn', {
					code,
					redirectUri: "https://sales-networkreport-test.nbims.com.cn/login_callback"
				}
			) || {};

			if(res.employeeNo) {
				const info = await http1.post('/public/feishu/token', {
					// empNo: res.employeeNo
					empNo: 'GS1436'
				})
				console.log(info);
				if(info.userJurVO && info.userJurVO.url) {
					Toast.clear();
					window.location.href = info.userJurVO.url
				}
			}

		} catch (e) {
			if(e.message) {
				Toast.show({
					content: e.message,
					icon: 'fail',
					duration: 0
				})
			}
		}
	}
	return (
		<div />
	)
}
export default Index
