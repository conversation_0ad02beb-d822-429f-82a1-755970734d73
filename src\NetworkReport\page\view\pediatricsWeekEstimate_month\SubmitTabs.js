import React, {useEffect, useRef, useState} from 'react';
import styled from 'styled-components';
import {Button, Space, Toast} from "antd-mobile";
import {useNavigate, useSearchParams} from "react-router-dom";
import {http1} from "../../../utils/network";

const Wrapper = styled.div`
  margin-bottom: 32px;
  .tab-bar {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .tab-item {
        height: 42px;
        line-height: 42px;
        position: relative;
        margin-right: 32px;
        cursor: pointer;
        color: #4E595E;
      }
      .tab-item-selected {
        color: #2551F2;
        &:after {
          content: ' ';
          display: block;
          height: 1px;
          width: 100%;
          position: absolute;
          bottom: 0;
          left: 0;
          background: #2551F2;
        }
      }
    }
    
  }
`;

const Tabs = props => {
  const navigate = useNavigate();
  let [searchParams, setSearchParams] = useSearchParams()

  const { roleInfo = {}, readLevel } = props;
  const { level } = roleInfo;

  const [loading, setLoading] = useState(false);
  const uploader = useRef(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [tabKey, setTabKey] = useState((searchParams.get('tabKey') || null) || '1')

  const status = searchParams.get('status');

  useEffect(() => {
    props.onChange(tabKey);
    window.sessionStorage.setItem('tabKey', tabKey);
  }, [tabKey])

  const downloadBinaryFile = (data, fileName, mimeType) => {
    const blob = new Blob([data], { type: mimeType });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
  }

  const handleDownload = async () => {
    try {
      setLoading(true);
      Toast.show({
        content: '下载中...',
        icon: 'loading',
        duration: 0
      })

      const name = {
        6: '代表周新增模版',
        5: '地区周新增模版',
        4: '大区周新增模版',
        3: '区域周新增模版'
      }

      const fileName = `${name[level]}.xlsx`; // 指定下载文件的名称
      const mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"; // 指定MIME类型
      const binaryData = await http1.post('/pediatricsEstimate/excel/download',{}, {responseType: 'blob'})

      await downloadBinaryFile(binaryData, fileName, mimeType);
      setLoading(false);
      Toast.clear();
    } catch (e) {
      if(e && e.message) {
        Toast.show(e.message)
      }
      setLoading(false);
      Toast.clear();
    }
  }

  const chooseFile = () => {
    uploader.current.click()
  }

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    setSelectedFile(file);
  };

  const tabs = [
    {
      label: '提交审核',
      value: '1'
    },
    {
      label: '审批流程',
      value: '2'
    }
  ]

  const handleToReport = () => {
    navigate(`/pediatricsMonthEstimate/report?deptcode=${roleInfo.deptcode}&estDate=${roleInfo.estDate}&empNo=${roleInfo.empNo || ''}&level=${level}`)
  }


  const canSubmit = () => {
    return true;
    const date = (new Date()).getDate()
    return date > 20 && date < 25
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      await http1.post('/pediatrics/approval/represent/submit', {
        deptcode: roleInfo.deptcode,
        empNo: roleInfo.empNo
      })
      await props.getHospitalData();
      setLoading(false)
    } catch (e) {
      setLoading(false)
      if(e && e.message) {
        Toast.show(e.message)
      }
    }
  }

  return (
    <Wrapper>
      <div className="tab-bar">
        <div className="left">
          {
            tabs.map(item => {
              return (
                <div
                  key={item.value}
                  className={item.value === tabKey ? 'tab-item tab-item-selected' : 'tab-item'}
                  onClick={() => setTabKey(item.value)}
                >
                  {item.label}
                </div>
              )
            })
          }
        </div>
        <div className="right">
        </div>
      </div>
    </Wrapper>
  )
}

export default Tabs;
