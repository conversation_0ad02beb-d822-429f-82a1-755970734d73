.pediatricsPromotionBoard {
  background: #efefef;
}

.pediatricsPromotionBoard .header {
  text-align: center;
  background: #efefef;
  border: 1px solid #bbb;
}

.pediatricsPromotionBoard .header .title {
  height: 50px;
  line-height: 50px;
  font-weight: bold;
  font-size: 16px;
}

.pediatricsPromotionBoard .header .content {
  display: flex;
  justify-content: space-between;
  padding: 10px;
}

.pediatricsPromotionBoard .standard {
  padding: 10px 0;
  border: 1px solid #bbb;
  background: #fff;
  margin-bottom: 10px;
}

.pediatricsPromotionBoard .standard .content {
  display: flex;
}

.pediatricsPromotionBoard .standard .content .item {
  width: 50%;
  text-align: center;
}

.pediatricsPromotionBoard .standard .item .title {
  height: 30px;
  line-height: 30px;
}

.pediatricsPromotionBoard .standard .item .value {
  font-size: 16px;
  font-weight: bold;
}

.pediatricsPromotionBoard .standard .standard-header {
  border-bottom: 1px solid #bbb;
  padding-bottom: 10px;
  padding-left: 10px;
}
