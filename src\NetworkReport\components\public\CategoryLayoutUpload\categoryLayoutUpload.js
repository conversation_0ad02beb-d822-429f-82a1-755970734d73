import React from 'react';
import './categoryLayoutUpload.css'


const CategoryLayoutUpload = (props) => {
    console.log(props)
    return (
        <div className={'categoryLayoutUpload'}>
            <div className={'categoryLayoutUpload__header'}>
                {props.children[0]}
            </div>
            <div className={'categoryLayoutUpload__container'}>
                {props.children[1]}
            </div>
            <div className={'categoryLayoutUpload__footer'}>
                {props.children[2]}
            </div>
        </div>
    )
}

export default CategoryLayoutUpload
