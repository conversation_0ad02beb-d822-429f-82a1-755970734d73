import React from "react";
import styled from 'styled-components';
import { Divider } from 'antd-mobile';
import {BoldText, isEmpty} from "../index";

const Wrapper = styled.div`
	background: #fff;
	padding: 0 10px;
	.header {
		text-align: center;
		border-bottom: 1px solid #bbb;
		padding-bottom: 10px;
		margin-bottom: 8px;
		padding-top: 10px;
	}
	.item {
		display: flex;
		justify-content: space-between;
		padding: 10px;
	}
`;

const Standard = ({ data = {}}) => {
	const _data1 = data['拟晋升级别达标'] || {};
	const _data2 = data['拟晋升级别2达标'] || {};
	return (
		<Wrapper>
			{
				!_data2.tagLevel ? (
					<div className="item">
						<div className="left" style={{ width: '50%', textAlign: 'center' }}>
							<div style={{ paddingBottom: 10 }}>拟晋升至</div>
							<div style={{ fontSize: '20px' }}>{_data1.tagLevel || '-'}</div>
							<div>{_data1.tagPosition && _data1.tagLevel.includes('T') ? `(${_data1.tagPosition})` : ''}</div>
						</div>
						<div className="right" style={{ width: '50%', textAlign: 'center' }}>
							<div style={{ paddingBottom: 10 }}>综合达标</div>
							<BoldText isBold isWarning={!_data1.totalCompliance}>
								<div style={{ fontSize: '20px' }}>
									{isEmpty(_data1.totalCompliance) ? '-' : _data1.totalCompliance ? '是' : '否'}
								</div>
							</BoldText>
						</div>
					</div>
				) : (
					<>
						<div className="header">
							综合达标
						</div>
						<div className="item">
							<div className="left">拟晋升级别：{_data1.tagLevel}{_data1.tagPosition && _data1.tagLevel.includes('T') ? `(${_data1.tagPosition})` : ''}</div>
							<div className="right">
								<BoldText isBold isWarning={!_data1.totalCompliance}>
									<div style={{ fontSize: '20px', position: 'relative', top: '-3px' }}>
										{isEmpty(_data1.totalCompliance) ? '-' : _data1.totalCompliance ? '是' : '否'}
									</div>
								</BoldText>
							</div>
						</div>
					</>
				)
			}

			{
				_data2.tagLevel && (
					<>
						<Divider style={{
							borderStyle: 'dashed',
							borderColor: '#bbb',
							margin: '8px 0'
						}} />
						<div className="item">
							<div className="left">拟晋升级别：{_data2.tagLevel}{_data2.tagPosition ? `(${_data2.tagPosition})` : ''}</div>
							<BoldText isBold isWarning={!_data2.totalCompliance}>
								<div style={{ fontSize: '20px', position: 'relative', top: '-3px' }}>
									{isEmpty(_data2.totalCompliance) ? '-' : _data2.totalCompliance ? '是' : '否'}
								</div>
							</BoldText>
						</div>
					</>
				)
			}
		</Wrapper>
	)
}

export default Standard;
