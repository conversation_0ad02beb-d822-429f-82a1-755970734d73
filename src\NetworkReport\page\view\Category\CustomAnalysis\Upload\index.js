import React, {useEffect, useRef, useState} from "react";
import { useLocation, useNavigate } from "react-router-dom";

import './index.css'
import {
	Button,
	List,
	Form,
	Input,
	Dialog,
	DatePicker,
	Space,
	Radio,
	Picker,
	Popup,
	PickerView,
	SearchBar,
	Toast,
	SafeArea
} from "antd-mobile";
import { http1 } from '../../../../../utils/network'
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import DoubleArrowIcon from "@mui/icons-material/DoubleArrow";
import CategoryLayoutUpload from "../../../../../components/public/CategoryLayoutUpload/categoryLayoutUpload";
import PostAddIcon from "@mui/icons-material/PostAdd";
import dayjs from "dayjs";
import {DataGrid, GridActionsCellItem} from "@mui/x-data-grid";
import EditIcon from "@mui/icons-material/Edit";
import AddFormDialog from "../components/addFormDialog";
import {nanoid} from "nanoid";

const DataEdit = () => {
    const [userInfo, setUserInfo] = useState({})
    const [doctorCodeList, setDoctorCodeList] = useState([])
    const [isShow, setIsShow] = useState(false)
    const info = useLocation()
    const id = info?.state?.id
    const isMe = info?.state?.isMe
    const ref =useRef()
    const navigate = useNavigate()

	const JSM_columns = (item) => [{
		headerName: "预估新患数",
		field: "estimateNewPat",
		type: "string"
	}, {
		headerName: "MTD新患数",
		field: "newPatMtd",
		type: "string"
	}, {
		headerName: "MTD达成率",
		field: "newPatRate",
		type: "string"
	}, {
		headerName: "本周门诊患者数",
		field: "patNumWeek",
		type: "string",
		width: 120
	}, {
		headerName: "AD&PDD本周患者数",
		field: "adpddPatNumWeek",
		type: "string",
		width: 160
	}, {
		headerName: "本周客户提及贴剂的患者数",
		field: "patchPatNumWeek",
		type: "string",
		width: 200
	}, {
		headerName: "提及贴剂的患者类型",
		field: "patchPatType",
		type: "string",
		width: 160
	}, {
		headerName: "标杆客户备注",
		field: "benchmarkCustomerRemarks",
		type: "string",
		width: 160
	},{
		headerName: "客户分类",
		field: "customerClassification",
		type: "string",
	}, {
		headerName: "问题归类",
		field: "problemClassification",
		type: "string"
	}, {
		headerName: "影响新患产生的具体问题描述",
		field: "problemDescription",
		type: "string",
		width: 220
	}, {
		headerName: "保障措施",
		field: "safeguardMeasures",
		type: "string"
	}, {
		headerName: "责任人",
		field: "personResponsible",
		type: "string"
	}, {
		headerName: "时间节点",
		field: "timeNode",
		type: "string"
	}, {
		headerName: `W${item.gsWeek}保障措施进展`,
		field: "safeguardMeasuresW",
		type: "string",
		width: "160"
	}, {
		headerName: '操作',
		field: '操作',
		type: 'actions',

		getActions: (params) => [
			<GridActionsCellItem
				icon={<EditIcon />}
				label="Edit"
				onClick={() => editUser(params.row)}
			/>,
		],
	}]

	const GH_columns = (item) => [{
		headerName: "预估新患数",
		field: "estimateNewPat",
		type: "string"
	}, {
		headerName: "MTD新患数",
		field: "newPatMtd",
		type: "string"
	}, {
		headerName: "MTD达成率",
		field: "newPatRate",
		type: "string"
	}, {
		headerName: "患者流",
		field: "patientFlow",
		type: "string"
	}, {
		headerName: "筛查",
		field: "screening",
		type: "string"
	}, {
		headerName: "标杆客户备注",
		field: "benchmarkCustomerRemarks",
		type: "string",
		width: 140
	}, {
		headerName: "客户分类",
		field: "customerClassification",
		type: "string",
	}, {
		headerName: "问题归类",
		field: "problemClassification",
		type: "string"
	}, {
		headerName: "影响新患产生的具体问题描述",
		field: "problemDescription",
		type: "string",
		width: 220
	}, {
		headerName: "保障措施",
		field: "safeguardMeasures",
		type: "string"
	}, {
		headerName: "责任人",
		field: "personResponsible",
		type: "string"
	}, {
		headerName: "时间节点",
		field: "timeNode",
		type: "string"
	}, {
		headerName: `W${item.gsWeek}保障措施进展`,
		field: "safeguardMeasuresW",
		type: "string",
		width: "160"
	}, {
		headerName: '操作',
		field: '操作',
		type: 'actions',

		getActions: (params) => [
			<GridActionsCellItem
				icon={<EditIcon />}
				label="Edit"
				onClick={() => editUser(params.row)}
			/>,
		],
	}]

	const columns = (item) => {
		if(item.prdBrand === 'JSM') {
			return JSM_columns(item)
		}else if(item.prdBrand === 'GH') {
			return GH_columns(item)
		}else {
			return [];
		}
	}

    const [form] = Form.useForm()
    const [data, setData] = useState([])
    const [reportData, setReportData] = useState({})
    const [row, setRow] = useState({})
    const [isDisabled, setDisabled] = useState(false)
    useEffect(() => {
        http1.post("/account/api/userInfo", {}).then((res) => {
            setUserInfo(res);
        });
    }, [])
    useEffect(() => {
        if (id) {
            http1.post("/nerveDaily/customer/queryList", {
                id,
                empNo: userInfo.empno
            }).then((res) => {
                let temp = (res?.entities || []).map((item) => {
                    item.timeNode = dayjs(item.timeNode).format('YYYY-MM-DD')
                    item.tempData = [item]
                    item.problemDescription = item.problemDescription || ''
                    item.safeguardMeasures = item.safeguardMeasures || ''
                    item.safeguardMeasuresW = item.safeguardMeasuresW || ''
                    return item
                })
                setData(temp)
                setReportData(res?.entity)
            })
        } else {
			if(userInfo.empno) {
				http1.post("/nerveDaily/customer/getDoc", {
					empNo: userInfo.empno
				}).then((res) => {
					if (res.length < 1) {
						setDisabled(true)
					} else {
						setDisabled(false)
					}
					let temp = res.map((item) => {
						item.tempData = []
						item.problemDescription = item.problemDescription || ''
						item.safeguardMeasures = item.safeguardMeasures || ''
						item.safeguardMeasuresW = item.safeguardMeasuresW || ''
						return item
					})
					setData(temp)
				})
			}
        }
    }, [userInfo])
    const handleTarget = () => {
        navigate("/category/customAnalysis/customAnalysisUpload/dataQuery")
    }
    const editUser = (data) => {
        setRow(data)
        setIsShow(true)
    }
    const handleOpenDialog = (row) => {
        setRow(row)
        setIsShow(true)
    }
    const handleCancelDialog = () => {
        setIsShow(false)
    }
    const handleRemoveData = () => {
        let tempData = data.map((item) => {
            if (item.doctorCode + item.prdBrand === row.doctorCode + row.prdBrand) {
                item.problemClassification = ''
                item.patientFlow = ''
                item.screening = ''
                item.problemDescription = ''
                item.safeguardMeasures = ''
                item.safeguardMeasuresW = ''
                item.timeNode = null
                item.tempData = []
            }
            return item
        })
        setData(tempData)
        handleCancelDialog()
    }
    const handleSubmitCenter = async () => {
        await ref.current.validateForm()
        let values = ref.current.handleFormData()
        if (values) {
            let tempData = data.map((item) => {
                if (item.doctorCode + item.prdBrand === values.doctorCode + values.prdBrand) {
                    item.tempData = [{
                        ...values,
                        patientFlow: values.patientFlow && values.patientFlow[0] || '',
                        screening: values.screening && values.screening[0] || '',
                        problemClassification: values.problemClassification && values.problemClassification[0] || '',
                        timeNode: values?.timeNode ? dayjs(values?.timeNode).format('YYYY-MM-DD') : '',
                        id: nanoid()
                    }]
                }
                return item
            })
            setData(tempData)
        }
        setIsShow(false)
    }
    const handleSubmit = () => {
        // const noData = []
        // const temp = data.filter(item => {
        //     if (item.tempData.length < 1) {
        //         noData.push(item.doctorCode + '-' + item.prdBrand)
        //     }
        //     return item.tempData.length < 1
        // })
        // if (temp.length > 0 || data.length < 1) {
        //     Dialog.alert({
        //         content: `存在未上报的编码三${noData.join(',')}`,
        //         onConfirm: () => {
        //             console.log("Confirmed");
        //         },
        //     });
        //     return
        // }

	    // 有一条数据就可以提交
	    if(!data.some(item => item.tempData && item.tempData.length)) {
		    Toast.show('请至少填写一条数据！');
		    return
	    }
        let temp1 = data.map(it => {
            const temp = it.tempData[0] || {}
            return {
              ...temp,
              tempData: null,
              id: null,
              timeNode: new Date(temp?.timeNode).getTime(),
              gsWeek: it.gsWeek,
              reporter: userInfo.empno,
	            doctorCode: it.doctorCode,
	            prdBrand: it.prdBrand,
	            estimateNewPat: it.estimateNewPat,
	            newPatMtd: it.newPatMtd,
	            newPatRate: it.newPatRate,
	            benchmarkCustomerRemarks: it.benchmarkCustomerRemarks,
	            customerClassification: it.customerClassification
            }
        })

        http1.post("/nerveDaily/customer/save", {
            reporter: userInfo.empno,
            id: id,
            nerveCustomerAnalisisEntityList: temp1
        }).then((res) => {
            handleRedirectList()
        })
    }
    const handleRemoveReport = () => {
        Dialog.confirm({
            content: "删除该上报后，该数据一并删除，是否确认？",
            onConfirm: () => {
                http1.get(`/nerveDaily/customer/delete/${id}`).then((res) => {
                    handleRedirectList()
                })
            }
        })
    }
    const handleRedirectList = () => {
        navigate("/category/customAnalysis/customAnalysisUpload/dataQuery")
    }
    return (
        <div className={"custom-analysis"}>
            <div className={"top"}>
                {
                    id ? (<ChevronLeftIcon sx={{ fontSize: 50 }} onClick={() => {handleTarget()}}/>) : null
                }
                客户分析表<DoubleArrowIcon fontSize={'small'} />数据上报
            </div>
            <div className={"content"}>
                <CategoryLayoutUpload>
                    {/*header*/}
                    <>
                        <div className={"header"}>
                            <div className={"headerContext"}>
                                <PostAddIcon className={"fontMain"} fontSize={"large"} />
                                <div style={{ fontWeight: "bold" }}>数据上报</div>
                            </div>
                            <div className={"headerContext"}>
                                <span className={"fontMain"} style={{ fontWeight: "bold" }}>
                                    上报人：
                                </span>
                                <span className={"fontSecond"}>{id ? reportData.createdName : userInfo.empname}</span>
                            </div>
                        </div>
                    </>
                    {/*body*/}
                    <>
                        <div className={"container"}>
                            <Form form={form} initialValues={{ hasSale: 1 }}>
                                <Form.Item disabled
                                    label='上报日期'
                                    trigger='onConfirm'>
                                    <DatePicker>
                                        {
                                            value => dayjs(id ? reportData.reportTime :  new Date()).format('YYYY-MM-DD')
                                        }
                                    </DatePicker>
                                </Form.Item>
                            </Form>
                            <List header={''}>
                                {
                                    data.length > 0 ? data.map((item, index) => {
                                        return (<List.Item key={item.doctorCode + item.prdBrand}>
                                            <div className={"countFunc"}>
                                                <div><label>编码三：</label>{item.doctorCode}-{item.prdBrand}</div>
                                                <div>
                                                    <Button size='small' color='primary' onClick={() => handleOpenDialog(item)} disabled={item.tempData.length > 0}>新增</Button>
                                                </div>
                                            </div>
                                            <DataGrid columns={columns(item)} rows={item.tempData} hideFooter={true} style={{ height: '120px' }} disableColumnMenu />
                                        </List.Item>)
                                    }) : null
                                }
                            </List>
                        </div>
                        <div>
							<div className={"footer"}>
								{
									(info?.state && !isMe) ? null : (<Button block color='primary' disabled={(info?.state && !isMe) || isDisabled} size='middle' style={{ marginTop: '5px', width: 'calc(50% - 5px)' }} onClick={() => handleSubmit()}>{ id ? '更新' : '提交' }</Button>)
								}
								{
									(id && info?.state && isMe) ? (<Button block color='primary' size='middle' style={{ marginTop: '5px', width: 'calc(50% - 5px)' }} onClick={() => handleRemoveReport()}>删除</Button>) : null
								}
								{
									(id) ? null : (<Button block color='primary' size='middle' style={{ marginTop: '5px', width: !(info?.state && !isMe) ? 'calc(50% - 5px)' : '100%' }} onClick={() => handleRedirectList()}>上报查询</Button>)
								}
							</div>
							<SafeArea position={'bottom'} />
						</div>
                    </>
                </CategoryLayoutUpload>
            </div>
            <Dialog destroyOnClose visible={isShow} title={'客户分析填报'}
                content={<AddFormDialog childRef={ref} data={row} />}
                closeOnAction
                actions={[
                    ([
                        {
                            key: 'cancel',
                            text: '取消',
                            danger: true,
                            onClick: handleCancelDialog
                        },
                        {
                            key: 'del',
                            text: '删除',
                            danger: true,
                            onClick: () => {
                                Dialog.confirm({
                                    content: '确认删除吗？',
                                    onConfirm: async () => {
                                        handleRemoveData()
                                    },
                                })
                            }
                        },
                        {
                            key: 'confirm',
                            text: '确定',
                            bold: true,
                            onClick: handleSubmitCenter
                        },
                    ]).filter(d => {
                        if (row?.id) {
                            return true
                        } else {
                            return d.key !== 'del'
                        }
                    })
                ]}>
            </Dialog>
        </div>
    )
}

export default DataEdit
