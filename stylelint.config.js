//CSS语法规范解析器
module.exports = {

    //您可以扩展现有配置（无论是您自己的配置还是第三方配置）。
    "extends": ["stylelint-config-standard", "stylelint-config-recommended"],

    //插件是社区构建的规则或规则集，支持方法，工具集，非标准CSS功能或非常特定的用例。
    "plugins":[
        //"stylelint-order"
    ],

    "processors":[
        //
    ],

    //您可以为所有未在其次级选项中指定严重性的规则设置默认严重性级别。
    //"defaultSeverity": "warning",

    "rules": {
        //
    },

    //stylelint node_modules默认情况下忽略目录。但是，如果ignoreFiles已设置，则将其覆盖。
    //"ignoreFiles": ["**/*.js"]
}
