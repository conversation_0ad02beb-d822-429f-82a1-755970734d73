import React, {useEffect, useState, useImperative<PERSON>andle} from 'react';
import styled from 'styled-components';
import { useNavigate } from "react-router-dom";
import {Button, NumberKeyboard, SafeArea, Space, Toast, Dialog, TextArea} from 'antd-mobile';
import {http1} from "../../../utils/network";

const Wrapper = styled.div`
  @keyframes breathe {
    0%   {box-shadow: 0 0 2px #ccc;}
    50%  {box-shadow: 0 0 4px #ccc, 0 0 10px #ccc;}
    100% {box-shadow: 0 0 2px #ccc;}
  }
	color: rgb(29, 33, 41); 
	font-weight: 400;
	.table-container {
		
	}
	.header {
		display: flex;
    .cell {
			width: 25%;
			text-align: center;
			border: 0.5px solid rgb(229, 230, 235);
      align-items: center;
	    background: rgb(247, 248, 250);
      color: rgb(134, 144, 156);
      font-size: 12px;
      font-weight: 400;
	    border-right: none;
	    .cell-title {
		    //padding-top: 20px;
		    line-height: 36px;
		    height: 36px;
	    }
	    .sub-row {
		    margin-top: 5px;
	    }
			.sub-row{
				display: flex;
				line-height: normal;
        .sub-cell {
          line-height: 30px;
	        width: 50%;
          border: 0.5px solid rgb(229, 230, 235);
          border-right: none;
        }
			}
		}
	}
	.body {
		.b-row {
			display: flex;
      .b-cell-1 {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 72px;
        .inner {
          line-height: 22px;
        }
      }
			.b-row-right {
				display: flex;
				width: 50%;
       
			}
			.b-cell,.b-cell-1 {
				width: 25%;
				text-align: center;
				border: 0.5px solid  rgb(229, 230, 235);
        .b-cell-row {
          height: 35px;
          line-height: 35px;
          border-bottom: 0.5px solid rgb(204, 204, 204);
	        .input {
						border: 0.5px solid  rgb(229, 230, 235);		    
		        line-height: 18px;
		        padding: 2px 0;
	        }
        }
        .b-cell-row:last-child {
	        border-bottom: none;
        }
				.b-cell-row-edit {
          .adm-input {
	          border: 1px solid  rgb(229, 230, 235);
	          padding: 0 4px;
            width: calc(100% - 16px);
	          text-align: center;
	          display: inline-block;
	          height: 24px;
	          line-height: 24px;
	          margin-top: 5px;
          }
          .adm-input-editing {
            animation: breathe 2s infinite ease-in-out;
          }
				}
			}
		}
	}
	.footer {
		text-align: center;
		padding-top: 20px;
		position: fixed;
		width: 100vw;
		bottom: 0;
		left: 0;
    height: 76px;
		border-top: 1px solid rgb(242, 243, 245);;
		.adm-button {
			width: 100px;
		}
	}
	.area {
		margin: 10px;
		.t {
			padding-bottom: 5px;
		}
		.adm-text-area-element {
      border: 1px solid #f1f1f1;
    }
	}
	.topic {
		color: red;
		padding: 10px;
		line-height: 22px;
	}
`;

const boardVO = {
	estLongNum: 'estLongNum',
	estWaterNum: 'estWaterNum',
	estPowderNum: 'estPowderNum'
}

const ReportTable = (props) => {
	const navigate = useNavigate();
	const [data, setData] = useState([]);
	const [current, setCurrent] = useState('');
	const [visible, setVisible] = useState(false);
	const [currentItem, setCurrentItem] = useState({});
	const [loading, setLoading] = useState(false);
	const [coreActions , setCoreActions ] = useState(null);
	const [pointOfGrowth  , setPointOfGrowth  ] = useState(null);

	const userInfo = window.localStorage.getItem('__userInfo__') && JSON.parse(window.localStorage.getItem('__userInfo__')) || {};

	// eslint-disable-next-line react/prop-types
	useImperativeHandle(props.refs,() => {
		return {
			handleSave
		}
	})

	useEffect(() => {
		setVisible(false)
	}, [props.hideVisible])

	useEffect(() => {
		if(!visible) {
			setCurrentItem({});
		}
	}, [visible])

	useEffect(() => {
		if(props.estDate && props.hospitalId) {
			getData();
		}
	}, [props.estDate, props.hospitalId])

	const handleChange = (value, doctorCode, type) => {
		setCurrent(value);
		setData(data.map(item => {
			if(item.doctorCode === doctorCode) {
				item.boardVO[type] = item.boardVO[type] == '0' ? value : (item.boardVO[type] || '')  + value;
			}
			return item
		}))
	}

	const handleDelete = (doctorCode, type) => {
		setData(data.map(item => {
			if(item.doctorCode === doctorCode) {
				item.boardVO[type] = item.boardVO[type].toString().slice(0, item.boardVO[type].length - 1) || 0;
			}
			return item
		}))
	}

	const getData = async () => {
		try {
			Toast.show('加载中...');
			const res = await http1.post('/pediatricsEstimate/query/form',{
				empNo: userInfo.empno,
				estDate: props.estDate,
				hospitalId: props.hospitalId
			}) || {};
			setData(res.map(item => ({...item, boardVO: item.boardVO || {}, doctorCode: item.label || item.doctorCode})));
			if(res && res[0]) {
				setCoreActions(res[0].coreActions);
				setPointOfGrowth(res[0].pointOfGrowth);
			}
			Toast.clear();
		} catch (e) {
			Toast.show(e.message)
		}
	}

	const handleSave = async (cb) => {
		try {
			let shouldCheck = false;
			data.forEach(item => {
				for(const key in boardVO) {
					if(item.boardVO[key] !== '0' && item.boardVO[key] !== 0) {
						shouldCheck = true;
					}
				}
			})
			if((props.level === 4 || props.level === 3) && (!pointOfGrowth || !coreActions) && shouldCheck) {
				Toast.show('请完整填写内容！');
				return false
			}
			Toast.show('保存中...');
			setLoading(true);
			await http1.post('/pediatricsEstimate/save',
				data.map(item => {

					return ({
						...item,
						empNo: userInfo.empno,
						estDate: props.estDate,
						hospitalId: props.hospitalId,
						label: props.hospitalId,
						pointOfGrowth,
						coreActions
					})
				})
			);
			setLoading(false);
			Toast.show('操作成功！');
			navigate(-1)
			// if(cb) {
			// 	await cb(true);
			// }
			// await props.init();
			// await getData();
		} catch (e) { /* empty */ }
			finally {
				setLoading(false);
			}
	}

	const cleanUp = () => {
		setData(data.map(item => {
			item.boardVO = {
				minLongTarget: '0',
				minWaterTarget: '0',
				minPowderTarget: '0',
				estLongNum: '0',
				estWaterNum: '0',
				estPowderNum: '0',
				minTotalTarget: '0',
				estTotalNum: '0'
			}
			item.pointOfGrowth = null;
			item.coreActions = null;
			return item
		}))
	}

	const total = (arr, type) => {
		let minTotal, estTotal = 0;
		minTotal = arr.reduce((sum, item) => sum + Number(item.boardVO[type]), 0);
		estTotal = arr.reduce((sum, item) => sum + Number(item.boardVO[type]), 0);

		return {
			minTotal,
			estTotal
		}
	}

	return (
		<Wrapper>
			<div className="table-container">
				<div className="header">
					<div className="cell">
						<div className='cell-title'>
							{props.level === 6 ? 'HCP' : '市场类型'}
						</div>
					</div>
					<div className="cell">
						<div className="cell-title">
							分类
						</div>
					</div>
					<div className="cell">
						<div className='cell-title'>底线</div>
					</div>
					<div className="cell">
						<div className='cell-title'>增长数</div>
					</div>
				</div>
				<div className="body">
					{
						data.map((item) => {
							return (
								<div className="b-row" key={item.doctorCode}>
									<div className="b-cell-1">
										{item.doctorCode}
									</div>
									<div className="b-cell">
										<div className="b-cell-row">
											新增数
										</div>
										<div className="b-cell-row">
											长效数
										</div>
									</div>
									<div className="b-cell">
										<div className="b-cell-row b-cell-row-edit">
											<div
												className={`adm-input ${(currentItem.doctorCode === item.doctorCode && currentItem.type === 'minTotalTarget') ? 'adm-input-editing' : ''}`}
												onClick={() => {
													setVisible(true);
													setCurrentItem({
														doctorCode: item.doctorCode,
														type: 'minTotalTarget'
													})
												}}
											>{item.boardVO.minTotalTarget}</div>
										</div>
										<div className="b-cell-row b-cell-row-edit">
											<div
												className={`adm-input ${(currentItem.doctorCode === item.doctorCode && currentItem.type === 'minLongTarget') ? 'adm-input-editing' : ''}`}
												onClick={() => {
													setVisible(true);
													setCurrentItem({
														doctorCode: item.doctorCode,
														type: 'minLongTarget'
													})
												}}
											>{item.boardVO.minLongTarget}</div>
										</div>
									</div>
									<div className="b-cell">
										<div className="b-cell-row b-cell-row-edit">
											<div
												className={`adm-input ${(currentItem.doctorCode === item.doctorCode && currentItem.type === 'estTotalNum') ? 'adm-input-editing' : ''}`}
												onClick={() => {
													setVisible(true);
													setCurrentItem({
														doctorCode: item.doctorCode,
														type: 'estTotalNum'
													})
												}}
											>{item.boardVO.estTotalNum}</div>
										</div>
										<div className="b-cell-row b-cell-row-edit">
											<div
												className={`adm-input ${(currentItem.doctorCode === item.doctorCode && currentItem.type === 'estLongNum') ? 'adm-input-editing' : ''}`}
												onClick={() => {
													setVisible(true);
													setCurrentItem({
														doctorCode: item.doctorCode,
														type: 'estLongNum'
													})
												}}
											>{item.boardVO.estLongNum}</div>
										</div>
									</div>

									<NumberKeyboard
										visible={visible}
										onClose={() => setVisible(false)}
										onInput={v => handleChange(v, currentItem.doctorCode, currentItem.type)}
										onDelete={() => handleDelete(currentItem.doctorCode, currentItem.type)}
										confirmText='确定'
									/>
								</div>
							)
						})
					}
					{
						props.level === 6 && (
							<div className="b-row" style={{ background: 'rgb(232, 243, 255)'}}>
								<div className="b-cell-1">
									合计
								</div>
								<div className="b-cell">
									<div className="b-cell-row">
										新增数
									</div>
									<div className="b-cell-row">
										长效数
									</div>
								</div>
								<div className="b-cell">
									<div className="b-cell-row">
										<div>{total(data, 'minTotalTarget').minTotal}</div>
									</div>
									<div className="b-cell-row">
										<div>{total(data, 'minLongTarget').minTotal}</div>
									</div>
								</div>
								<div className="b-cell">
									<div className="b-cell-row">
										<div>{total(data, 'estTotalNum').estTotal}</div>
									</div>
									<div className="b-cell-row">
										<div>{total(data, 'estLongNum').estTotal}</div>
									</div>
								</div>

								<NumberKeyboard
									visible={visible}
									onClose={() => setVisible(false)}
									onInput={v => handleChange(v, currentItem.doctorCode, currentItem.type)}
									onDelete={() => handleDelete(currentItem.doctorCode, currentItem.type)}
									confirmText='确定'
								/>
							</div>
						)
					}
				</div>
			</div>
			{
				// 大区展示
				(props.level === 4 || props.level === 3) && (data && !!data.length) && (
					<div className="text">
						<div className="area">
							<div className="t">{props.estDate}增长点</div>
							<TextArea
								showCount
								maxLength={500}
								autoSize={{ minRows: 5, maxRows: 10 }}
								onChange={setPointOfGrowth}
								value={pointOfGrowth}
								onFocus={() => setVisible(false)}
							/>
						</div>
						<div className="area">
							<div className="t">{props.estDate}行动计划</div>
							<TextArea
								showCount
								maxLength={500}
								autoSize={{ minRows: 5, maxRows: 10 }}
								onChange={setCoreActions}
								value={coreActions}
								onFocus={() => setVisible(false)}
							/>
						</div>
						<div className="topic">
							填写目标：
							<br />
							*工作计划至少填写最近2周
							<br />
							*目标至少6周
						</div>
					</div>
				)
			}
			{
				data && !!data.length && (
					<div className="footer">
						<Space>
							<Button
								onClick={() =>
									Dialog.confirm({
										content: `是否${(props.level === 4 || props.level === 3) ? '重置' : '清零'}?`,
										onConfirm: async () => {
											cleanUp()
										},
									})
								}
								style={{ width: 'calc(30vw)', background: 'rgb(232, 243, 255)', border: 'none', color: 'rgb(51, 112, 255)'}}
							>
								{(props.level === 4 || props.level === 3) ? '重置' : '清零'}
							</Button>
							<Button
								color='primary'
								fill='solid'
								onClick={handleSave}
								loading={loading}
								style={{ width: 'calc(50vw)', background: 'rgb(22, 93, 255)', color: '#fff' }}
							>
								提交
							</Button>
						</Space>
					</div>
				)
			}
			<SafeArea position={'bottom'} />
			<div style={{ height: 20 }} />
		</Wrapper>
	)
}

export default ReportTable;
