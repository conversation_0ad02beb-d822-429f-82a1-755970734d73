import React, {
  useState,
  useEffect,
  RefObject,
  useImperativeHandle,
} from "react";
import addSalesAmount from "./addSalesAmount.less";

import {
  Button,
  Popup,
  Form,
  Input,
  SearchBar,
  PickerView,
  DatePicker,
  Picker,
  Radio,
  Toast,
} from "antd-mobile";
import dayjs from "dayjs";
import { http1 } from "../../../../../../../../utils/network";
import Big from "big.js";

const AddSalesAmount = (props) => {
  const {
    code3Info,
    setCode3Info,
    commonInfo,
    setCommonInfo,
    itemData = {},
    userInfo = {},
  } = props;
  console.log("props=========", props);

  const [tempCode3, setTempCode3] = useState({});
  const [tempCode3Old, setTempCode3Old] = useState({});

  const [form] = Form.useForm();
  const productType = Form.useWatch("productType", form);
  const type = Form.useWatch("type", form);
  const channel = Form.useWatch("channel", form);
  const prdBrand = Form.useWatch("prdBrand", form);
  const specification = Form.useWatch("specification", form);
  const dept = Form.useWatch("dept", form);
  const patientType = Form.useWatch("patientType", form);
  const secProductType = Form.useWatch("secProductType", form);
  const volume = Form.useWatch("volume", form);
  const priceUnit = Form.useWatch("priceUnit", form);
  const newPatientPrice = Form.useWatch("newPatientPrice", form);
  const npVolume = Form.useWatch("npVolume", form);
  const oldPatientPrice = Form.useWatch("oldPatientPrice", form);
  const opVolume = Form.useWatch("opVolume", form);
  const newPatNum = Form.useWatch("newPatNum", form);
  const oldPatNum = Form.useWatch("oldPatNum", form);

  const [productTypeList, setProductTypeList] = useState([]);
  const [productThreeNameList, setProductThreeNameList] = useState([]);
  const [channelList, setChannelList] = useState([]);
  const [specificationList, setSpecificationList] = useState([]);
  const [deptList, setDeptList] = useState([]);
  const [patientTypeList, setPatientTypeList] = useState([]);

  const [basicColumns, setBasicColumns] = useState([]); //编码3请求数据

  const [basicColumnsOld, setBasicColumnsOld] = useState([]); //编码3请求数据

  const [basicProduct, setBasicProduct] = useState([]); //产品请求数据

  const [basicType, setBasicType] = useState([]); //患者类型请求数据

  const [basicMoneyType, setBasicMoneyType] = useState([]); //剂型

  useImperativeHandle(props.childRef, () => {
    return {
      validateForm,
      handleFormData,
      currentProducts,
    };
  });

  const currentProducts = () => {
    return products[0];

    // if(props.formDialog === "manager" || props.formDialog === 'money') {
    // 	return products[0]
    // }else {
    // 	return basicProduct[0]
    // }
  };

  const validateForm = async () => {
    await form.validateFields();
  };

  const handleFormData = () => {
    let values = form.getFieldsValue();
    return values;
  };

  // 获取产品
  const getProduct = async () => {
    try {
      const res = (await http1.get(`/get/product`)) || [];
      console.log(res, "resresresres");
      setProductTypeList([
        res.map((d) => ({
          label: d,
          value: d,
        })),
      ]);
    } catch (e) {}
  };

  const getPatient = async (threeLvlVarietCode) => {
    try {
      if (!threeLvlVarietCode) return false;
      const res =
        (await http1.get(`/meta/api/patient/type/${threeLvlVarietCode}`)) || [];
      setBasicType([
        res.map((d) => ({
          label: d,
          value: d,
        })),
      ]);
    } catch (e) {}
  };

  /*
  * 4：购药渠道
  1：品类
  2：科室
  3：适应症
  * */
  // 获取产品信息
  const getChannelList = async (product) => {
    if (!product) return false;
    try {
      const res =
        (await http1.get(`/get/product/detail?type=4&product=${product}`)) ||
        [];

      setChannelList([
        res.map((d) => ({
          label: d.channel,
          value: d.channel,
        })),
      ]);
    } catch (e) {}
  };

  // 获取品类
  const getProductThreeNameList = async (product) => {
    if (!product) return false;
    try {
      const res =
        (await http1.get(`/get/product/detail?type=1&product=${product}`)) ||
        [];
      setProductThreeNameList([
        res.map((d) => ({
          label: d.catgory,
          value: d.catgory,
        })),
      ]);
    } catch (e) {}
  };

  // 获取规格
  const getSpecificationList = async (product, prdBrand) => {
    if (!product || !prdBrand) return false;
    try {
      const res =
        (await http1.get(
          `/get/product/detail?product=${product}&prdBrand=${prdBrand}`
        )) || [];
      setSpecificationList([
        res.map((d) => ({
          ...d,
          label: d.spec,
          value: d.spec,
        })),
      ]);
    } catch (e) {}
  };

  // 获取科室
  const getDeptList = async (product) => {
    if (!product) return false;
    try {
      const res =
        (await http1.get(`/get/product/detail?type=2&product=${product}`)) ||
        [];
      setDeptList([
        res.map((d) => ({
          label: d.dept,
          value: d.dept,
        })),
      ]);
    } catch (e) {}
  };

  // 获取适应症
  const getPatientTypeList = async (product, type) => {
    console.log(product, type, "typetypetypetype");
    if (!product) return false;
    if (!type) return false;
    try {
      const res =
        (await http1.get(`/get/product/detail?type=3&product=${product}`)) ||
        [];
      setPatientTypeList([
        res
          .map((d) => ({
            label: d.indications,
            value: d.indications,
            catgory: d.catgory,
          }))
          .filter((d) => d.catgory === type),
      ]);
    } catch (e) {}
  };

  useEffect(() => {
    getPatient(productType && productType[0]);
  }, [productType]);

  const queryDoctor = () => {
    const hospitalId = props.hospitalId;
    http1
      .post("/nerveDaily/nerveDaily/doctor/query", { hospitalId: hospitalId })
      .then((res) => {
        console.log(res, "resresres");
        let temp = [];
        temp =
          res.length > 0 &&
          res.map((item) => {
            return { label: item.doctorCode, value: item.doctorCode };
          });
        if (temp.length > 0) {
          setBasicColumns([temp]);
        } else {
          setBasicColumns([[{ label: "", value: "" }]]);
        }
      });
  };

  const queryDoctorOld = () => {
    const hospitalId = props.hospitalId;
    http1
      .post("/nerveDaily/nerveDaily/doctor/query", { hospitalId: hospitalId })
      .then((res) => {
        let temp = [];
        temp =
          res.length > 0 &&
          res.map((item) => {
            return { label: item.doctorCode, value: item.doctorCode };
          });
        if (temp.length > 0) {
          setBasicColumnsOld([temp]);
        } else {
          setBasicColumnsOld([[{ label: "", value: "" }]]);
        }
      });
  };

  useEffect(() => {
    const hospitalId = props.hospitalId;
    if (hospitalId) {
      queryDoctor();
      queryDoctorOld();
    }
    if (itemData.productType !== "0JSM") {
      onSearchPCode3(itemData.productType);
    }

    http1.post("/meta/select", { tag: "NerveProduct" }).then((res) => {
      let temp = [];
      temp =
        res.length > 0 &&
        res.map((item) => {
          return { label: item.description, value: item.code };
        });
      if (temp.length > 0) {
        setBasicProduct([temp]);
      } else {
        setBasicProduct([[{ label: "", value: "" }]]);
      }
    });

    getProduct();
  }, []);

  useEffect(() => {
    getChannelList(productType);
    getProductThreeNameList(productType);
    getDeptList(productType);
    getSpecificationList(productType, prdBrand);
  }, [productType, prdBrand]);

  useEffect(() => {
    if (productType && productType[0] && type && type[0]) {
      console.log(productType, type, "productType, type");
      getPatientTypeList(productType && productType[0], type && type[0]);
    }
  }, [productType, type]);

  const handleVolume = (value) => {
    let str = String(value * priceUnit);
    console.log(str);
    if (str.indexOf(".") !== -1) {
      str = str.slice(0, str.indexOf(".") + 3);
      console.log(str);
    }
    form.setFieldsValue({ price: str });
  };

  const handlePriceUnit = (value) => {
    if (value.indexOf(".") !== -1) {
      value = value.slice(0, value.indexOf(".") + 3);
    }
    let x = new Big(value);
    let str = x.times(volume);
    form.setFieldsValue({ price: str });
  };

  const [code3PopupShow, setCode3PopupShow] = useState(false);
  const [code3PopupShowOld, setCode3PopupShowOld] = useState(false);

  const [code3Show, setCode3Show] = useState(true);
  const [code3ShowOld, setCode3ShowOld] = useState(true);

  const [code3Name, setCode3Name] = useState("");
  const [code3NameOld, setCode3NameOld] = useState("");

  const handleCode3Name = (val) => {
    setCode3Name(val);
  };
  const handleCode3NameOld = (val) => {
    setCode3NameOld(val);
  };

  const onSearchPCode3 = (brand = null) => {
    const _brand =
      brand ||
      (productType && productType[0]) ||
      commonInfo.productName ||
      itemData.productThreeName;
    if (_brand) {
      http1
        .post("/doctor/info/queryBy", {
          empNo: userInfo.empno,
          buCode: "neuroendocrine",
          hospitalId: props.hospitalId,
          brand: _brand,
          page: 1,
          size: 10000,
          doctorName: code3Name,
          doctorCode: "",
        })

        .then((res) => {
          let temp = [];
          temp =
            res.list.length > 0 &&
            res.list.map((item) => {
              return { label: item.doctorCode, value: item.doctorCode };
            });
          if (temp.length > 0) {
            setCode3Show(true);
            setBasicColumns([temp]);
          } else {
            setCode3Show(false);
            setBasicColumns([[{ label: "", value: "" }]]);
          }
        });
    }
  };
  const onSearchPCode3Old = (brand = null) => {
    if (
      brand ||
      commonInfo.name ||
      itemData.secProductThreeName ||
      (secProductType && secProductType[0])
    ) {
      http1
        .post("/doctor/info/queryBy", {
          empNo: userInfo.empno,
          buCode: "neuroendocrine",
          hospitalId: props.hospitalId,
          brand:
            brand ||
            commonInfo.name ||
            itemData.secProductThreeName ||
            (secProductType && secProductType[0]),
          page: 1,
          size: 10000,
          doctorName: code3NameOld,
          doctorCode: "",
        })

        .then((res) => {
          let temp = [];
          temp =
            res.list.length > 0 &&
            res.list.map((item) => {
              return { label: item.doctorCode, value: item.doctorCode };
            });
          if (temp.length > 0) {
            setCode3ShowOld(true);
            setBasicColumnsOld([temp]);
          } else {
            setCode3ShowOld(false);
            setBasicColumnsOld([[{ label: "", value: "" }]]);
          }
        });
    }
  };

  const handleConfirmCode3 = () => {
    setCode3Info({
      ...code3Info,
      doctorCode: tempCode3.value,
      doctorName: tempCode3.label,
    });
    form.setFields([
      {
        name: "doctorCode",
        value: tempCode3.value,
        errors: null,
      },
    ]);
    form.setFieldValue("doctorCode", tempCode3.value);
    setCode3PopupShow(false);
  };
  const handleConfirmCode3Old = () => {
    setCode3Info({
      ...code3Info,
      doctorSecCode: tempCode3Old.value,
      doctorSecName: tempCode3Old.label,
    });
    form.setFields([
      {
        name: "doctorSecCode",
        value: tempCode3Old.value,
        errors: null,
      },
    ]);
    form.setFieldValue("doctorSecCode", tempCode3Old.value);
    setCode3PopupShowOld(false);
  };

  const products = props.products || [];
  const fullProducts = props.fullProducts || [];

  useEffect(() => {
    if (products && products[0].length === 1) {
      form.setFieldValue("productType", [products[0][0].value]);
      const _specificationColumns = specificationColumns(products[0][0].value);
      if (_specificationColumns.length === 1) {
        form.setFieldValue("specification", [_specificationColumns[0].value]);
      }
    }
  }, [products, props.visible]);

  useEffect(() => {
    if (itemData.id) {
      onSearchPCode3();
      onSearchPCode3Old();
    }
  }, [itemData.id]);

  const specificationColumns = (productType) => {
    return fullProducts
      .filter((d) => d.threeLvlVarietCode === productType)
      .map((d) => ({ label: d.spec, value: d.specCode }));
  };

  return (
    <div>
      {props.formDialog === "manager" && (
        <div className={addSalesAmount}>
          <Form form={form}>
            <Form.Item
              name="productType"
              label="产品"
              trigger="onConfirm"
              rules={[{ required: true }]}
              onClick={(e, pickref) => {
                pickref.current?.open();
              }}
              initialValue={[itemData.productType].filter(Boolean)}
            >
              <Picker
                columns={productTypeList}
                onConfirm={(v, extend) => {
                  console.log(v, "vvvvvvvvvv");
                  if (v?.[0] === "金蓓欣") {
                    form.setFieldValue("newPatientPrice", "8988");
                    form.setFieldValue("oldPatientPrice", "8988");
                  }else {
										form.setFieldValue("newPatientPrice", "");
										form.setFieldValue("oldPatientPrice", "");
									}
                  const [item] = extend.items;
                  if (item?.value !== "0JSM") {
                    onSearchPCode3(item?.label);
                    onSearchPCode3(item?.label);
                  } else {
                    queryDoctor();
                  }
                  setCommonInfo({
                    ...commonInfo,
                    productName: extend.items[0].label,
                  });
                  // setCode3Show(true);
                  getChannelList(item?.label);
                  getDeptList(item?.label);
                  getPatientTypeList(item?.label);
                  getProductThreeNameList(item?.label);
                  form.setFieldValue("channel", []);
                  form.setFieldValue("prdBrand", []);
                  form.setFieldValue("specification", []);
                  form.setFieldValue("dept", []);
                  form.setFieldValue("secSpecification", []);
                }}
              >
                {(e) => {
                  return (
                    <div>
                      {(e && e[0] && e[0].label) || (
                        <span style={{ color: "#ccc" }}>请选择</span>
                      )}
                    </div>
                  );
                }}
              </Picker>
            </Form.Item>
            {productType && productType[0] && productType[0] === "GH" && (
              <Form.Item
                name="type"
                label="领域"
                trigger="onConfirm"
                rules={[{ required: true }]}
                onClick={(e, pickref) => {
                  pickref.current?.open();
                }}
                initialValue={[itemData.type].filter(Boolean)}
              >
                <Picker
                  columns={[
                    [
                      {
                        label: "长高",
                        value: "长高",
                      },
                      {
                        label: "经典",
                        value: "经典",
                      },
                      {
                        label: "特发-其它",
                        value: "特发-其它",
                      },
                      {
                        label: "TBI-神外领域",
                        value: "TBI-神外领域",
                      },
                      {
                        label: "TBI-康复领域",
                        value: "TBI-康复领域",
                      },
                    ],
                  ]}
                  onConfirm={(v, extend) => {
                    form.setFieldValue("patientType", []);
                    getPatientTypeList(productType && productType[0], v[0]);
                  }}
                >
                  {(e) => {
                    return (
                      <div>
                        {(e && e[0] && e[0].label) || (
                          <span style={{ color: "#ccc" }}>请选择</span>
                        )}
                      </div>
                    );
                  }}
                </Picker>
              </Form.Item>
            )}
            {productType && productType[0] && productType[0] === "金斯明" && (
              <Form.Item
                name="channel"
                label="购药渠道"
                trigger="onConfirm"
                onClick={(e, pickref) => {
                  pickref.current?.open();
                }}
                initialValue={[itemData.channel].filter(Boolean)}
              >
                <Picker
                  columns={channelList}
                  onConfirm={(v, extend) => {
                    const [item] = extend.items;
                    if (item?.value !== "0JSM") {
                      onSearchPCode3(item?.label);
                    } else {
                      queryDoctor();
                    }
                    setCommonInfo({
                      ...commonInfo,
                      productName: extend.items[0].label,
                    });
                    setCode3Show(true);
                  }}
                >
                  {(e) => {
                    return (
                      <div>
                        {(e && e[0] && e[0].label) || (
                          <span style={{ color: "#ccc" }}>请选择</span>
                        )}
                      </div>
                    );
                  }}
                </Picker>
              </Form.Item>
            )}

            {/*<Form.Item*/}
            {/*  name="doctorCode"*/}
            {/*  label="编码3"*/}
            {/*  trigger="onConfirm"*/}
            {/*  rules={[{ required: true }]}*/}
            {/*  onClick={() => {*/}
            {/*		if (productType && !productType.length) {*/}
            {/*			Toast.show('请先选择产品！')*/}
            {/*		} else {*/}
            {/*			setCode3PopupShow(true)*/}
            {/*		}*/}
            {/*	}}*/}
            {/*  initialValue={itemData.doctorCode}*/}
            {/*>*/}
            {/*  {code3Info.doctorCode ? (*/}
            {/*    <Input value={code3Info.doctorCode} />*/}
            {/*  ) : (*/}
            {/*    <span style={{ color: '#ccc' }}>请选择</span>*/}
            {/*  )}*/}
            {/*</Form.Item>*/}
            <Form.Item
              name="prdBrand"
              label="品类"
              onClick={(e, pickerRef) => {
                pickerRef.current?.open();
              }}
              trigger="onConfirm" //
              rules={[{ required: true }]}
              initialValue={[itemData.prdBrand].filter(Boolean)}
            >
              <Picker
                columns={productThreeNameList}
                onConfirm={(v, extend) => {
                  const [item] = extend.items;
                  form.setFieldValue("specification", []);
                  form.setFieldValue("patientType", []);
                  getSpecificationList(
                    form.getFieldsValue().productType,
                    item?.label
                  );
                }}
              >
                {(e) => {
                  return (
                    <div>
                      {(e && e[0] && e[0].label) || (
                        <span style={{ color: "#ccc" }}>请选择</span>
                      )}
                    </div>
                  );
                }}
              </Picker>
            </Form.Item>
            <Form.Item
              name="specification"
              disabled={!productType}
              label="规格"
              trigger="onConfirm"
              rules={[{ required: true }]}
              onClick={(e, pickref) => {
                if (productType && !productType.length) {
                  Toast.show("请先选择产品！");
                } else {
                  pickref.current?.open();
                }
              }}
              initialValue={[itemData.specification].filter(Boolean)}
            >
              <Picker
                columns={specificationList}
                onConfirm={(v, extend) => {
                  setCommonInfo({
                    ...commonInfo,
                    specificationName: extend.items[0].label,
                  });
                  const [item] = extend.items;
                  if (item.catgory === "赛必健套餐") {
                    form.setFieldValue("newPatientPrice", item.value);
                    form.setFieldValue("oldPatientPrice", item.value);
                  }
                }}
              >
                {(e) => {
                  return (
                    <div>
                      {(e && e[0] && e[0].label) || (
                        <span style={{ color: "#ccc" }}>请选择</span>
                      )}
                    </div>
                  );
                }}
              </Picker>
            </Form.Item>
            <Form.Item
              name="dept"
              disabled={!productType}
              label="科室"
              trigger="onConfirm"
              rules={[{ required: true }]}
              onClick={(e, pickref) => {
                if (productType && !productType.length) {
                  Toast.show("请先选择产品！");
                } else {
                  pickref.current?.open();
                }
              }}
              initialValue={[itemData.dept].filter(Boolean)}
            >
              <Picker
                columns={deptList}
                onConfirm={(v, extend) => {
                  setCommonInfo({
                    ...commonInfo,
                    specificationName: extend.items[0].label,
                  });
                }}
              >
                {(e) => {
                  return (
                    <div>
                      {(e && e[0] && e[0].label) || (
                        <span style={{ color: "#ccc" }}>请选择</span>
                      )}
                    </div>
                  );
                }}
              </Picker>
            </Form.Item>
            {productType && productType[0] === "GH" && (
              <Form.Item
                name="patientType"
                disabled={!productType}
                rules={[{ required: true }]}
                label="适应症"
                trigger="onConfirm"
                onClick={(e, pickref) => {
                  if (productType && !productType.length) {
                    Toast.show("请先选择产品！");
                  } else {
                    pickref.current?.open();
                  }
                }}
                initialValue={[itemData.patientType].filter(Boolean)}
              >
                <Picker
                  columns={patientTypeList}
                  onConfirm={(v, extend) => {
                    setCommonInfo({
                      ...commonInfo,
                      patientName: extend.items[0].label,
                    });
                  }}
                >
                  {(e) => {
                    return (
                      <div>
                        {(e && e[0] && e[0].label) || (
                          <span style={{ color: "#ccc" }}>请选择</span>
                        )}
                      </div>
                    );
                  }}
                </Picker>
              </Form.Item>
            )}
            <Form.Item>
              <div>新增预估</div>
            </Form.Item>
            {(productType &&
              productType[0] === "GH" &&
              prdBrand &&
              prdBrand[0] === "长效") ||
            (productType && productType[0] === "金蓓欣") ? (
              <Form.Item
                name="earlyBirdPlanNew"
                label="早鸟计划"
                rules={[{ required: true }]}
                initialValue={itemData.earlyBirdPlanNew || "否"}
              >
                <Radio.Group>
                  <Radio value="是">是&nbsp;&nbsp;</Radio>
                  <Radio value="否">否</Radio>
                </Radio.Group>
              </Form.Item>
            ) : null}
            <Form.Item
              name="newPatNum"
              label="新增数"
              rules={[
                { required: true },
                {
                  pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
                  message: "新增数只能为整数",
                },
              ]}
              initialValue={itemData.newPatNum}
            >
              <Input type={"number"} placeholder="请填写" max={99999} />
            </Form.Item>
            <Form.Item
              name="newPatientPrice"
              label="单价"
              rules={[{ required: true }]}
              getValueFromEvent={(value) => {
                return value.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); // 只能输入两位小数
              }}
              initialValue={itemData.newPatientPrice}
            >
              <Input type={"number"} placeholder="请填写" max={99999} />
            </Form.Item>
            <Form.Item
              name="npVolume"
              label="数量"
              rules={[
                { required: true },
                {
                  pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
                  message: "数量只能为整数",
                },
              ]}
              initialValue={itemData.npVolume}
            >
              <Input type={"number"} placeholder="请填写" max={99999} />
            </Form.Item>
            <Form.Item
              name="newPatientAmount"
              label="金额"
              value={
                newPatientPrice !== undefined &&
                npVolume !== undefined &&
                newPatientPrice * npVolume
              }
            >
              {newPatientPrice !== undefined &&
                npVolume !== undefined &&
                Number(newPatientPrice * npVolume).toFixed(2)}
            </Form.Item>
            <Form.Item>
              <div>存量预估</div>
            </Form.Item>
            {(productType &&
              productType[0] === "GH" &&
              prdBrand &&
              prdBrand[0] === "长效") ||
            (productType && productType[0] === "金蓓欣") ? (
              <Form.Item
                name="earlyBirdPlanOld"
                label="早鸟计划"
                rules={[{ required: true }]}
                initialValue={itemData.earlyBirdPlanOld || "否"}
              >
                <Radio.Group>
                  <Radio value="是">是&nbsp;&nbsp;</Radio>
                  <Radio value="否">否</Radio>
                </Radio.Group>
              </Form.Item>
            ) : null}
            <Form.Item
              name="oldPatNum"
              label="存量数"
              rules={[
                { required: true },
                {
                  pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
                  message: "存量数只能为整数",
                },
              ]}
              initialValue={itemData.oldPatNum}
            >
              <Input type={"number"} placeholder="请填写" max={99999} />
            </Form.Item>
            {!(oldPatNum == 0) && (
              <>
                {/*<Form.Item*/}
                {/*  name="secProductType"*/}
                {/*  label="产品"*/}
                {/*  trigger="onConfirm"*/}
                {/*  rules={[{ required: true }]}*/}
                {/*  onClick={(e, pickref) => {*/}
                {/*    pickref.current?.open()*/}
                {/*  }}*/}
                {/*  initialValue={[itemData.secProductType].filter(Boolean)}*/}
                {/*>*/}
                {/*  <Picker*/}
                {/*    columns={products}*/}
                {/*    onConfirm={(v, extend) => {*/}
                {/*      const [item] = extend.items*/}
                {/*      if (item?.value !== '0JSM') {*/}
                {/*        onSearchPCode3Old(item?.label)*/}
                {/*      }else {*/}
                {/*				queryDoctorOld()*/}
                {/*			}*/}
                {/*      setCommonInfo({*/}
                {/*        ...commonInfo,*/}
                {/*        productName: extend.items[0].label,*/}
                {/*      })*/}
                {/*			setCode3ShowOld(true);*/}
                {/*			form.setFieldValue('doctorSecCode', [])*/}
                {/*      form.setFieldValue('source', [])*/}
                {/*      form.setFieldValue('secSpecification', [])*/}
                {/*    }}*/}
                {/*  >*/}
                {/*    {(e) => {*/}
                {/*      return (*/}
                {/*        <div>*/}
                {/*          {(e && e[0] && e[0].label) || (*/}
                {/*            <span style={{ color: '#ccc' }}>请选择</span>*/}
                {/*          )}*/}
                {/*        </div>*/}
                {/*      )*/}
                {/*    }}*/}
                {/*  </Picker>*/}
                {/*</Form.Item>*/}
                {/*<Form.Item*/}
                {/*  name="secSpecification"*/}
                {/*  disabled={!secProductType}*/}
                {/*  label="剂型"*/}
                {/*  trigger="onConfirm"*/}
                {/*  rules={[{ required: true }]}*/}
                {/*  onClick={(e, pickref) => {*/}
                {/*    if (secProductType && !secProductType.length) {*/}
                {/*      Toast.show('请先选择产品！')*/}
                {/*    } else {*/}
                {/*      pickref.current?.open()*/}
                {/*    }*/}
                {/*  }}*/}
                {/*  initialValue={[itemData.secProductCode].filter(Boolean)}*/}
                {/*>*/}
                {/*  <Picker*/}
                {/*    columns={[*/}
                {/*      specificationColumns(secProductType && secProductType[0]),*/}
                {/*    ]}*/}
                {/*    onConfirm={(v, extend) => {*/}
                {/*      setCommonInfo({*/}
                {/*        ...commonInfo,*/}
                {/*        specificationName: extend.items[0].label,*/}
                {/*      })*/}
                {/*    }}*/}
                {/*  >*/}
                {/*    {(e) => {*/}
                {/*      return (*/}
                {/*        <div>*/}
                {/*          {(e && e[0] && e[0].label) || (*/}
                {/*            <span style={{ color: '#ccc' }}>请选择</span>*/}
                {/*          )}*/}
                {/*        </div>*/}
                {/*      )*/}
                {/*    }}*/}
                {/*  </Picker>*/}
                {/*</Form.Item>*/}
                <Form.Item
                  name="oldPatientPrice"
                  label="单价"
                  rules={[{ required: true }]}
                  getValueFromEvent={(value) => {
                    return value.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); // 只能输入两位小数
                  }}
                  initialValue={itemData.oldPatientPrice}
                >
                  <Input type={"number"} placeholder="请填写" max={99999} />
                </Form.Item>
                <Form.Item
                  name="opVolume"
                  label="数量"
                  rules={[
                    { required: true },
                    {
                      pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
                      message: "数量只能为整数",
                    },
                  ]}
                  initialValue={itemData.opVolume}
                >
                  <Input type={"number"} placeholder="请填写" max={99999} />
                </Form.Item>
                <Form.Item
                  name="oldPatientAmount"
                  label="金额"
                  value={
                    oldPatientPrice !== undefined &&
                    opVolume !== undefined &&
                    oldPatientPrice * opVolume
                  }
                >
                  {oldPatientPrice !== undefined &&
                    opVolume !== undefined &&
                    Number(oldPatientPrice * opVolume).toFixed(2)}
                </Form.Item>
              </>
            )}

            <Popup
              visible={code3PopupShow}
              onMaskClick={() => {
                setCode3PopupShow(false);
              }}
              bodyStyle={{
                borderTopLeftRadius: "8px",
                borderTopRightRadius: "8px",
                minHeight: "40vh",
              }}
            >
              <div
                className={"adm-picker-header"}
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  height: "42px",
                }}
              >
                <span
                  className={"adm-picker-header-button"}
                  style={{
                    fontSize: "15px",
                    color: "var(--gensci-main)",
                  }}
                  onClick={() => setCode3PopupShow(false)}
                >
                  取消
                </span>
                <span
                  className={"adm-picker-header-button"}
                  style={{
                    fontSize: "15px",
                    color: "var(--gensci-main)",
                  }}
                  onClick={handleConfirmCode3}
                >
                  确认
                </span>
              </div>
              <div className={addSalesAmount.addSalesAmount__searchContainer}>
                <SearchBar
                  placeholder="请输入内容"
                  onChange={(e) => handleCode3Name(e)}
                  style={{ marginRight: "10px", flex: "1" }}
                />
                <Button
                  size={"small"}
                  color={"primary"}
                  onClick={() => {
                    if (productType && productType[0] === "0JSM") {
                      queryDoctor();
                    } else {
                      onSearchPCode3();
                    }
                  }}
                >
                  查询
                </Button>
              </div>
              {code3Show ? (
                <PickerView
                  columns={basicColumns}
                  onChange={(val, extend) => {
                    setTempCode3(extend.items[0] && extend.items[0]);
                  }}
                ></PickerView>
              ) : (
                <div
                  style={{
                    width: "60px",
                    margin: "80px auto",
                    color: "#ccc",
                  }}
                >
                  暂无数据
                </div>
              )}
            </Popup>

            <Popup
              visible={code3PopupShowOld}
              onMaskClick={() => {
                setCode3PopupShowOld(false);
              }}
              bodyStyle={{
                borderTopLeftRadius: "8px",
                borderTopRightRadius: "8px",
                minHeight: "40vh",
              }}
            >
              <div
                className={"adm-picker-header"}
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  height: "42px",
                }}
              >
                <span
                  className={"adm-picker-header-button"}
                  style={{
                    fontSize: "15px",
                    color: "var(--gensci-main)",
                  }}
                  onClick={() => setCode3PopupShowOld(false)}
                >
                  取消
                </span>
                <span
                  className={"adm-picker-header-button"}
                  style={{
                    fontSize: "15px",
                    color: "var(--gensci-main)",
                  }}
                  onClick={handleConfirmCode3Old}
                >
                  确认
                </span>
              </div>
              <div className={addSalesAmount.addSalesAmount__searchContainer}>
                <SearchBar
                  placeholder="请输入内容"
                  onChange={(e) => handleCode3NameOld(e)}
                  style={{ marginRight: "10px", flex: "1" }}
                />
                <Button
                  size={"small"}
                  color={"primary"}
                  onClick={() => {
                    if (productType && productType[0] === "0JSM") {
                      queryDoctorOld();
                    } else {
                      onSearchPCode3Old();
                    }
                  }}
                >
                  查询
                </Button>
              </div>
              {code3ShowOld ? (
                <PickerView
                  columns={basicColumnsOld}
                  onChange={(val, extend) => {
                    setTempCode3Old(extend.items[0] && extend.items[0]);
                  }}
                ></PickerView>
              ) : (
                <div
                  style={{
                    width: "60px",
                    margin: "80px auto",
                    color: "#ccc",
                  }}
                >
                  暂无数据
                </div>
              )}
            </Popup>
          </Form>
        </div>
      )}
    </div>
  );
};

export default AddSalesAmount;
