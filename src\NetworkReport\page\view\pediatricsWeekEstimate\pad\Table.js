import React from 'react';
import styled from 'styled-components';
import {useNavigate, useSearchParams} from "react-router-dom";
import dayjs from "dayjs";
import {SafeArea} from "antd-mobile";
import {http1} from "@utils/network";

const Wrapper = styled.div`
	color: rgb(29, 33, 41);
	.high-light {
		background: rgb(232, 243, 255) !important;
	}
	.under_line-text {
    color: rgb(22, 93, 255);
    text-decoration-line: underline;
	}
	.header {
		display: flex;
    .cell {
      height: 72px;
			width: 12.5%;
			text-align: center;
			border: 0.5px solid rgb(229, 230, 235);
      align-items: center;
	    background: rgb(247, 248, 250);
      color: rgb(134, 144, 156);
      font-size: 12px;
      font-weight: 400;
	    .cell-title {
        .inner {
          line-height: 72px;
        }
	    }
	    .sub-cell-title {
        line-height: 34px;
      }
	    .sub-row {
	    }
			.sub-row{
				display: flex;
				line-height: normal;
        .sub-cell {
          line-height: 36px;
	        width: 50%;
          border: 0.5px solid rgb(229, 230, 235);
        }
			}
		}
	}
	.body {
		.tba-row {
			padding: 4px 10px;
			background: rgb(235 235 235);
		}
		.b-row {
			height: 72px;
			display: flex;
      .b-cell-1 {
	      //line-height: 100px;
	      display: flex;
        justify-content: center;
        align-items: center;
        height: 72px;
	      .inner {
		      line-height: 22px;
	      }
      }
			.b-row-right {
				display: flex;
				width: 12.5%;
			}
			.b-cell,.b-cell-1 {
        width: 12.5%;
				text-align: center;
				border: 0.5px solid rgb(229, 230, 235);
        .b-cell-row {
          height: 36px;
          line-height: 36px;
          border-top: 0.5px solid rgb(229, 230, 235);
	        width: 100%;
	        text-decoration: underline;
        }
        .b-cell-row:first-child {
	        border-top: none;
        }
			}
		}
	}
  .topic {
    margin-top: 50px;
    color: red;
    padding: 10px;
    line-height: 22px;
  }
	.tba-table {
		margin-top: 20px;
		.item {
			background: #0aa2ea;
			padding: 10px;
			margin: 2px 0;
			color: #fff;
		}
	}
`;

// eslint-disable-next-line react/prop-types
const Table = ({ data = [], currentWeek, level, estDateData = [], TBAData = [] }) => {
	const navigate = useNavigate();
	let [searchParams] = useSearchParams()

	const handleToReport = async (hospitalId, identify) => {
		if(!hospitalId) return false;
		if(identify && identify.indexOf('TBA') > -1 && level !== 6) {
			const res = await http1.post('/account/api/public/code2tokenNoSign', { empNo: hospitalId }) || {};
			// navigate(`/pediatricsWeekEstimate/boardTBA?hospitalId=${hospitalId}&currentWeek=${currentWeek}&identify=${identify}&token=${res.token}`)
			if(res) {
				window.location.href = `/pediatricsWeekEstimate/boardTBA?hospitalId=${hospitalId}&currentWeek=${currentWeek}&identify=${identify}&token=${res.token}&deptcode=${searchParams.get('deptcode') || ''}`
			}
		}else {
			navigate(`/pediatricsWeekEstimate/padReport?hospitalId=${hospitalId}&currentWeek=${currentWeek}&deptcode=${searchParams.get('deptcode') || ''}`)
		}
	}

	const handleToReportTBA = async (empNo, deptcode) => {
		const res = await http1.post('/account/api/public/code2tokenNoSign/dept', { empNo, deptcode }) || {};
		if(res) {
			window.location.href = `/pediatricsWeekEstimate/board?token=${res.token}&deptcode=${deptcode}`
		}
	}

	const valueRender = ({item, estItem, key}) => {
		const boardVO = item.boardVO || [];
		const _current = boardVO.find(_ => _.estDate === estItem.estDate) || {};
		return _current.isEst ? _current[key] : '未预估'
	};

	const hasValue = v => v !== "" && v !== undefined && v !== null;

	const getterHasValue = ({item, estItem}) => {
		const boardVO = item.boardVO || [];
		const _current = boardVO.find(_ => _.estDate === estItem.estDate) || {};
		if(_current.estTotalNum != '0' || _current.estLongNum != '0') {
			return !(hasValue(_current.coreActions) && hasValue(_current.pointOfGrowth))
		}else {
			return false
		}
	};

	const redLight = ({item, estItem, index}) => {
		// if(index > 1) {
		// 	return 'rgb(29, 33, 41)'
		// }
		if(level === 3 || level === 4) {
			return getterHasValue({item, estItem}) && item.identify !== '合计' ? 'red' : 'rgb(29, 33, 41)'
		}else {
			return 'rgb(29, 33, 41)'
		}
	}

	return (
		<Wrapper>
			<div className="header">
				<div className="cell">
					<div className='cell-title'>
						<div className="inner">
							{level === 6 ? '机构' : '客户类型'}
						</div>
					</div>
				</div>
				<div className="cell">
					<div className="cell-title">
						<div className="inner">
							分类
						</div>
					</div>
				</div>
				{
					estDateData.map(item => {
						return (
							<div className="cell" key={item.estDate}>
								<div className='cell-title sub-cell-title'>
									{item.estDate.split('~')[0] && dayjs(item.estDate.split('~')[0].replace(/\./g, '/')).format('MM/DD')}
									~
									{item.estDate.split('~')[1] && dayjs(item.estDate.split('~')[1].replace(/\./g, '/')).format('MM/DD')}
								</div>
								<div className="sub-row">
									<div className="sub-cell">底线</div>
									<div className="sub-cell">增长数</div>
								</div>
							</div>
						)
					})
				}
				{/*<div className="cell">*/}
				{/*	<div className='cell-title sub-cell-title'>下周</div>*/}
				{/*	<div className="sub-row">*/}
				{/*		<div className="sub-cell">底线</div>*/}
				{/*		<div className="sub-cell">增长数</div>*/}
				{/*	</div>*/}
				{/*</div>*/}
			</div>
			<div className="body">
				{
					data.map((item, index) => {
						return (
							<React.Fragment key={item.hospitalId + `${index}`}>
								{
									item.showGap && (
										<div className={'tba-row'}>TBA代表</div>
									)
								}
								<div
									key={index}
									className="b-row"
									onClick={() => handleToReport(item.hospitalId, item.identify)}
									style={{ background: item.identify === '合计' ? '#ccc' : '#fff' }}
								>
									<div className="b-cell-1">
										<div className="inner">
											{
												item.isTBA ? item.empName : item.identify
											}
											{}
										</div>
									</div>
									<div className="b-cell">
										<div className="b-cell-row" style={{ textDecoration: 'none'}}>
											新增数
										</div>
										<div className="b-cell-row" style={{ textDecoration: 'none'}}>
											长效数
										</div>
									</div>
									{
										estDateData.map((estItem, index) => {
											return (
												<div className="b-row-right" key={estItem.estDate}>
													<div className="b-cell" style={{ width: '50%', color: redLight({item, estItem, index}) }}>
														<div className="b-cell-row" style={{ width: '100%', textDecoration: item.identify === '合计' ? 'none' : 'underline' }}>
															{valueRender({item, estItem, key: 'minTotalTarget'})}
														</div>
														<div className="b-cell-row"  style={{ width: '100%', color: redLight({item, estItem, index}), textDecoration: item.identify === '合计' ? 'none' : 'underline' }}>
															{valueRender({item, estItem, key: 'minLongTarget'})}
														</div>
													</div>
													<div className="b-cell" style={{ width: '50%' }}>
														<div className="b-cell-row" style={{ width: '100%', color: redLight({item, estItem, index}), textDecoration: item.identify === '合计' ? 'none' : 'underline' }}>
															{valueRender({item, estItem, key: 'estTotalNum'})}
														</div>
														<div className="b-cell-row" style={{ width: '100%', color: redLight({item, estItem, index}), textDecoration: item.identify === '合计' ? 'none' : 'underline' }}>
															{valueRender({item, estItem, key: 'estLongNum'})}
														</div>
													</div>
													{/*<div className="b-cell">*/}
													{/*	<div className="b-cell-row">*/}
													{/*		{valueRender({value: item.minTotalTargetNext, isEst: item.isEstNext })}*/}
													{/*	</div>*/}
													{/*	<div className="b-cell-row">*/}
													{/*		{valueRender({value: item.minTotalTargetNext, isEst: item.isEstNext })}*/}
													{/*	</div>*/}
													{/*</div>*/}
													{/*<div className="b-cell">*/}
													{/*	<div className="b-cell-row">*/}
													{/*		{valueRender({value: item.estTotalNumNext, isEst: item.isEstNext })}*/}
													{/*	</div>*/}
													{/*	<div className="b-cell-row">*/}
													{/*		{valueRender({value: item.estLongNumNext, isEst: item.isEstNext })}*/}
													{/*	</div>*/}
													{/*</div>*/}
												</div>
											)
										})
									}
								</div>
							</React.Fragment>
						)
					})
				}
			</div>

			<div className="tba-table">
				{
					TBAData.map(item => {
						return (
							<div className={'item'} key={item.empNo} onClick={() => handleToReportTBA(item.empNo, item.deptcode)}>
								<div>{item.empName}{item.isEst ? '(已完成预估)' : '(未完成预估)'}&nbsp;&nbsp;&nbsp;&nbsp;{'>'}</div>
							</div>
						)
					})
				}
			</div>

			{
				// 大区 区域展示
				(level === 4 || level === 3) && (
					<div>
						<div style={{ textAlign: 'center', fontWeight: 'bold', padding: '10px', color: '#6C6C6C'}}>图例：数字为红色，请及时补充填写工作计划</div>
						<div className="topic">
							填写目标：
							<br />
							*工作工作计划至少填写最近2周
							<br />
							*目标至少6周
						</div>
					</div>
				)
			}

			<SafeArea position={'bottom'} />
			<div style={{ height: 40 }} />
		</Wrapper>
	)
}

export default Table;
