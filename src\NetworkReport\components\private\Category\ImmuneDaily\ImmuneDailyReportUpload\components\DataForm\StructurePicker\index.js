import React, {useEffect, useState} from 'react';
import {Button, Input, <PERSON>er<PERSON><PERSON><PERSON>, <PERSON>up, SearchBar, Toast} from "antd-mobile";
import style from './index.less';
import {http1} from "@utils/network";

const StructurePicker = ({ value, onChange, data, hiddenOther, noCompass, onChangeExt }) => {
	const [visible, setVisible] = useState(false);
	const [_value, setValue] = useState([]);
	const [realValue, setRealValue] = useState(undefined);
	const [items, setItems] = useState({});
	const [columns, setColumns] = useState([]);
	const [hospitalName, setHospitalName] = useState('');

	useEffect(() => {
		getList()
	}, []);

	useEffect(() => {
		const currentItem = columns[0] && columns[0].find(d => d.value == (value && value[0])) || {};
		setItems(currentItem);
		if(onChangeExt) {
			if(currentItem.label) {
				onChangeExt(currentItem);
			}
		}
	}, [value, columns])

	const onConfirm = () => {
		setRealValue(_value);
		onChange(_value);
		setVisible(false);
		console.log(_value);
		const currentItem = columns[0].find(d => d.value === _value[0]) || {};
		setItems(currentItem);
		if(onChangeExt) {
			console.log(currentItem, 'currentItem')
			onChangeExt(currentItem);
		}
	}

	const getList = async (hospitalName) => {
		try {
			const res = await http1.post('/immune/ins', {
				pageNum: 1,
				pageSize: 20,
				name: hospitalName
			});
			const data = res.records || [];
			// eslint-disable-next-line react/prop-types
			setColumns([data.map(d => ({
				label: noCompass ? d.mdmname : d.wbid + '-' + d.mdmname,
				value: d.wbid,
				medicineHospitalCode: d.mdmcode
			}))])

			console.log(columns, 'columns')
		} catch (e) {
			if(e && e.message) {
				Toast.show(e.message)
			}
		}
	};

	const onSearch = async () => {
		await getList(hospitalName)
	}
	return (
		<>
			{
				<Input onClick={() => setVisible(true)} value={items.label || ((data.organizationId !== undefined && data.organizationId !== null) ? data.organizationName : '')} />
			}

			<Popup
				visible={visible}
				onMaskClick={() => setVisible(false)}
				bodyStyle={{
					borderTopLeftRadius: "8px",
					borderTopRightRadius: "8px",
					minHeight: "40vh",
				}}
			>
				<div
					className={"adm-picker-header"}
					style={{
						display: "flex",
						alignItems: "center",
						justifyContent: "space-between",
						height: "42px",
					}}
				>
					<span
						className={"adm-picker-header-button"}
						style={{
							fontSize: "15px",
							color: "var(--gensci-main)",
						}}
						onClick={() => setVisible(false)}
					>
						取消
					</span>
					<span
						className={"adm-picker-header-button"}
						style={{
							fontSize: "15px",
							color: "var(--gensci-main)",
						}}
						onClick={() => onConfirm()}
					>
						确认
					</span>
				</div>
				<div
					className={style.pickerSearch}
				>
					<SearchBar
						style={{ marginRight: "10px", flex: "1"}}
						placeholder="请输入内容"
						value={hospitalName}
						onChange={(e) => setHospitalName(e)}
					/>
					<Button
						size={"small"}
						color={"primary"}
						onClick={onSearch}
					>
						查询
					</Button>
				</div>
				{
					(hospitalName && !columns[0].length) ? (
						<div
							style={{
								width: "60px",
								margin: "80px auto",
								color: "#ccc",
							}}
						>
							暂无数据
						</div>
					) : (
						<PickerView
							columns={columns}
							onChange={(value, extend) => {
								setValue(value);
							}}
						/>
					)
				}
			</Popup>
		</>
	)
};

export default StructurePicker;