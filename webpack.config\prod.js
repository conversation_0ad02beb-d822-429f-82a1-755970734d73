const webpack = require('webpack')
const { resolve } = require('path');
// const glob = require('glob');
const PATHS = {
    src: resolve(__dirname, 'src')
        //path.join(__dirname, 'src')
}


const HtmlWebpackPlugin = require('html-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
const WorkboxPlugin = require('workbox-webpack-plugin');
const ESLintPlugin = require('eslint-webpack-plugin');
const StylelintPlugin = require('stylelint-webpack-plugin');
const HtmlMinimizerPlugin = require('html-minimizer-webpack-plugin');
const ImageMinimizerPlugin = require('image-minimizer-webpack-plugin');
const JsonMinimizerPlugin = require('json-minimizer-webpack-plugin');
const PurgeCssPlugin = require('purgecss-webpack-plugin');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
const CopyPlugin = require("copy-webpack-plugin");

module.exports = {
    entry: {
        //单入口文件
        main:resolve(__dirname, '../src/NetworkReport/index/index.js')
    },
    output: {
        //解析后目录
        path: resolve(__dirname, '../dist'),
        //[name]为解析后原来的名字
        filename: '[name].[chunkhash].js',
        chunkFilename: '[name].[contenthash].min.js',
        //生产环境改为完整的URL地址
        //publicPath:resolve(__dirname, '../dist')
        publicPath: '/'
    },

    //仅代表名字
    name: 'NetworkReport',

    //开发模式缓存,提高构建速度
    //cache: {type:"memory"},
    performance:{
        hints: 'warning',
        maxEntrypointSize: 400000,
        maxAssetSize: 100000,
    },

    //调试模式,开发环境下建议source-map(最慢,含原始原代码), cheap-source-map(较快,含转换过的代码（仅限行）)
    //devtool: 'eval',

    //开发模式
    mode: 'production',

    //控制 bundle 信息显示, 开发环境下建议: /'normal' 标准输出 或者/'minimal' 只在发生错误或新的编译开始时输出
    stats: 'normal',

    //优化,生产环境下再做配置
    optimization:{

        runtimeChunk: 'single',

        chunkIds: 'named',

        moduleIds: 'named',

        splitChunks: {

            chunks: "initial",

            maxAsyncRequests: 1,

            maxInitialRequests: 1,

            minSize: 0,

            minChunks: 1,

            cacheGroups:{
                vendor:{
                    chunks: "initial",

                    priority: 0,

                    name: "vendor", // 要缓存的 分隔出来的 chunk 名称

                    minSize: 0,

                    minChunks: 1,

                    test: /react|lodash/,

                    enforce: true,

                    reuseExistingChunk: true,

                },
            },
        },

        minimize: true,

        minimizer:[
            new TerserPlugin(
                {
                    parallel: true,
                    terserOptions:{},
                    extractComments: true,
                }
            ),
            new CssMinimizerPlugin(
                {
                    // cache: true,
                    parallel: true,
                    // sourceMap: true,
                    minimizerOptions: {
                        preset: [
                            'default',
                            {
                                discardComments: {removeAll: true},
                            },
                        ],
                    }
                }
            ),
            new HtmlMinimizerPlugin(
                {
                    parallel: true,
                }
            ),
            new JsonMinimizerPlugin(
                {}
            )
        ],

        removeAvailableModules: true,

        removeEmptyChunks: true,

        mergeDuplicateChunks: true,

        sideEffects:true,

        innerGraph: true,

        mangleWasmImports: false,
    },


    module: {
        rules:[

            {
                test:/\.js|jsx$/,
                exclude: /node_modules/,
                use: [
                    'babel-loader',
                    //多线程编译LOADER
                    {
                        loader: "thread-loader",

                        // 有同样配置的 loader 会共享一个 worker 池
                        options: {
                            // 产生的 worker 的数量，默认是 (cpu 核心数 - 1)，或者，在 require('os').cpus() 是 undefined 时回退至 1
                            workers: 3,

                            // 一个 worker 进程中并行执行工作的数量,默认为 20
                            workerParallelJobs: 50,

                            // 额外的 node.js 参数
                            workerNodeArgs: ['--max-old-space-size=1024'],

                            // 允许重新生成一个僵死的 work 池,这个过程会降低整体编译速度,并且开发环境应该设置为 false
                            poolRespawn: false,

                            // 闲置时定时删除 worker 进程,默认为 500（ms）,可以设置为无穷大，这样在监视模式(--watch)下可以保持 worker 持续存在
                            poolTimeout: 2000,

                            // 池分配给 worker 的工作数量,默认为 200,降低这个数值会降低总体的效率，但是会提升工作分布更均匀
                            poolParallelJobs: 50,

                            // 池的名称,可以修改名称来创建其余选项都一样的池
                            name: "my-pool"
                        },
                    }
                ]
            },
            {
                test: /\.css$/,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader,
                        options: {
                            esModule: true,
                        },
                    },
                    // CSS从组件中分离至单独文件
                    {
                        loader: 'css-loader',
                        options: {
                            importLoaders: 1,
                        }
                    },
                    // CSS语法转换配置模块
                    {
                        loader: 'postcss-loader',
                    },
                ],
                // include: /node_modules/,
            },
            {
                test: /\.less$/,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader,
                        options: {
                            esModule: true,
                        },
                    },
                    {
                        loader: 'css-loader',
                        options: {
                            // 启用/禁用 url/image-set 函数进行处理。 控制 url() 函数的解析。 绝对 URL 和 根目录的相对 URL 都不会去解析。
                            url: true,

                            // 启用/禁用 @import 规则进行处理 控制 @import 的解析。@import 中的绝对 URL 会被直接移到运行时去处理。
                            import: true,

                            // 启用/禁用 CSS 模块及其配置
                            // 该 modules 选项启用/禁用 CSS 模块 规范并且设置基本的行为。
                            // 设置为 false 值会提升性能，因为避免了 CSS Modules 特性的解析，这对于使用普通 CSS 或者其他技术的开发人员是非常有用的。
                            // 对于满足 `/\.module\.\w+$/i` 正则匹配发热文件自动启用 css 模块。
                            // 默认值 modules: { auto: true },
                            modules: true,
                            // {auto: true },

                            // 类型：Number 默认：0
                            // 启用/禁用或设置在CSS加载程序之前应用的加载程序的数量。
                            // importLoaders 选项允许你配置在 css-loader 之前有多少 loader 应用于@imported 资源。
                            // 每一个 CSS 的 `@import` 都运行 `postcss-loader`，不要忘了 `sass-loader` 将不属于 CSS 的 `@import` 编译到一个文件中
                            // 如果您需要在每个 CSS 的 `@import` 上运行 `sass-loader` 和 `postcss-loader`，请将其设置为 `2`。
                            importLoaders: 1,

                            // 类型：Boolean 默认：true
                            // 默认情况下，css-loader 生成使用 ES 模块语法的 JS 模块。 在某些情况下，使用 ES 模块是有益的，例如在模块串联或 tree shaking 时。
                            esModule: false,
                        },
                    },
                    {
                        loader: 'postcss-loader',
                    },
                    {
                        loader: 'less-loader',
                    },
                ],
                exclude: /node_modules/,
            },
            {
                test: /\.html$/i,
                loader: 'html-loader',
                options: {
                    //默认情况下，每个可加载属性（例如 - <img src="image.png"> ）都将被导入（ const img = require ('./image.png') 或 import img from "./image.png"" ）。
                    // 你可能需要为配置中的图片指定 loader（推荐使用 file-loader 或 url-loader ）。
                    sources: true,

                    //类型：Boolean|Object 默认值：在生产模式下为 true ，否则为 false
                    // 告诉 html-loader 编译时需要压缩 HTML 字符串。
                    minimize: {
                        removeComments: true,
                        collapseWhitespace: true,
                    },
                    //类型：Boolean 值默认值：false
                    // 默认情况下， html-loader 生成使用 CommonJS 模块语法的 JS 模块。 在某些情况下，使用 ES 模块会更好，例如在进行模块合并和 tree shaking 时。
                    esModule:false
                },
            },
            // {   /*
            //     当文件体积小于 limit 时，url-loader 把文件转为 Data URI 的格式内联到引用的地方
            //     当文件大于 limit 时，url-loader 会调用 file-loader, 把文件储存到输出目录，并把引用的文件路径改写成输出后的路径
            //     */
            //     /*在 webpack 5 之前，通常使用：
            //     raw-loader 将文件导入为字符串
            //     url-loader 将文件作为 data URI 内联到 bundle 中
            //     file-loader 将文件发送到输出目录
            //     资源模块类型(asset module type)，通过添加 4 种新的模块类型，来替换所有这些 loader：
            //     asset/resource 发送一个单独的文件并导出 URL。之前通过使用 file-loader 实现。
            //     asset/inline 导出一个资源的 data URI。之前通过使用 url-loader 实现。
            //     asset/source 导出资源的源代码。之前通过使用 raw-loader 实现。
            //     asset 在导出一个 data URI 和发送一个单独的文件之间自动选择。之前通过使用 url-loader，并且配置资源体积限制实现。*/
            //
            //     test: /\.(png|jpg|jpeg|gif|eot|ttf|woff|woff2|svg|svgz)(\?.+)?$/,
            //     use: [{
            //         loader: 'url-loader',
            //         options: {
            //             // 类型：Boolean|Number|String默认值：undefined
            //             // 该限制可以通过加载程序选项指定，默认为无限制。
            //             limit: 1000,
            //
            //             //类型：Boolean|String默认值：基于mime-types
            //             // 指定mimetype将与之内联的文件。如果未指定，mimetype则将从mime-types中使用该值。
            //             //The true value allows to generation the mimetype part from mime-types.
            //             // The false value removes the mediatype part from a Data URL (if omitted, defaults to text/plain;charset=US-ASCII).
            //             //String Sets the MIME type for the file to be transformed.
            //             mimetype: false,
            //
            //             //类型：Boolean|String默认值：base64
            //             // 指定encoding将与之内联的文件。如果未指定，encoding将为base64。
            //             //如果您不想使用任何编码，则可以将encoding其false设置true为默认值base64。
            //             //It supports Node.js Buffers and Character Encodings which are ["utf8","utf16le","latin1","base64","hex","ascii","binary","ucs2"].
            //             encoding: 'utf8',
            //
            //             //类型：String默认值：'file-loader'
            //             // 指定当目标文件的大小超过limit选项中设置的限制时使用的备用加载程序。
            //             //fallback: require.resolve('responsive-loader'),
            //             //后备加载程序将获得与url-loader相同的配置选项。 例如，要在上面使用的情况下设置响应加载程序的质量选项：
            //             //quality: 85,
            //
            //             //By default, file-loader generates JS modules that use the ES modules syntax.
            //             // There are some cases in which using ES modules is beneficial, like in the case of module concatenation and tree shaking.
            //             //esModule: false,
            //         }
            //     }],
            //
            //
            // },

            {
                test: /\.(png|jpg|jpeg|gif|eot|ttf|woff|woff2|svg|svgz)(\?.+)?$/,
                type:'asset',
                parser:{
                    dataUrlCondition: {
                        maxSize:8192
                    },
                }
            },
        ],
    },

    devServer: {
        host: 'localhost',
        port: 8000,
        hot: true,
        open: false,
        historyApiFallback: true,
        compress: true,
        //代理,多用于解决跨域问题
        proxy:{},
        static: {
            directory: resolve(__dirname, '../src/NetworkReport/assets'),
            publicPath: '/assets'
        }
    },

		resolve: {
			// 要解析的文件的扩展名
			extensions: ['.js', '.jsx', '.json'],
			alias: {
				'@comp': resolve(__dirname, '../src/NetworkReport/components'),
				'@page': resolve(__dirname, '../src/NetworkReport/page'),
				'@utils': resolve(__dirname, '../src/NetworkReport/utils')
			}
		},

    plugins:[
        new CleanWebpackPlugin({
            // cleanStaleWebpackAssets: false
        }),

        new HtmlWebpackPlugin({

            template: resolve(__dirname, '../src/NetworkReport/index/index.html'),//模版路径
            //favicon: './index/favicon.png',//图标路径
            minify: { //压缩
                removeAttributeQuotes:true,
                removeComments: true,
                collapseWhitespace: true,
                removeScriptTypeAttributes:true,
                removeStyleLinkTypeAttributes:true,

                removeRedundantAttributes: true,
                useShortDoctype: true
            },
        }),

        new MiniCssExtractPlugin({
            //类型：String|Function默认值：[name].css
            //此选项确定每个输出CSS文件的名称。
            filename: '[name].[hash].css',

            //类型：String|Function默认值：based on filename
            //指定chunkFilename为 function仅在webpack @ 5中可用
            chunkFilename: "[id].css",

            //Type: Boolean Default: false
            //Remove Order Warnings
            ignoreOrder:false,

            //类型：Object默认值：{}
            //如果已定义，mini-css-extract-plugin则会将给定属性及其值附加在element上。
            //注意：它仅适用于动态加载的CSS块，如果要修改html文件中的链接属性，请使用html-webpack-plugin
            attributes: {
                id: 'target',
                'data-target': 'example',
            },
        }),

        // new PurgeCssPlugin({
        //     // paths 参数
        //     // 通过 webpack 插件，你可以通过设置文件名数组来指定由 purgecss 来分析的内容。这些内容可以是 html、pug、blade 等文件。你还可以使用类似 glob 或 glob-all 的模块来轻松获取文件列表。
        //     // 您可能需要{ noDir: true }作为一个选项传递给与插件无法操作的目录相匹配的glob.sync()as glob.sync。
        //     paths: glob.sync(`${PATHS.src}/**/*`,  { nodir: true }),
        //
        //     //only 参数
        //     // 您可以通过 only 参数为 purgecss-webpack-plugin 指定入口点（entrypoints）
        //     only: ['bundle', 'vendor'],
        //
        //     //rejected 参数
        //     // 如果此参数被设置为 true，则所有被删除的选择器（selectors）都将被添加到 Stats Data 中并被标记为 purged。
        //     //rejected:true,
        // }),

        //GZIP压缩
        new CompressionPlugin({
            //类型：String|Function默认值：gzip
            // 压缩算法/功能。
            // 如果使用自定义功能的algorithm选项，默认值compressionOptions选项{}。
            //String 该算法取自zlib。Function 允许指定自定义压缩功能。
            algorithm: 'gzip',

            //类型：Object默认值：{ level: 9 }
            // 的压缩选项algorithm。
            // 您可以在zlib中找到所有选项。
            // If️如果对algorithm选项使用自定义功能，则默认值为{}。
            compressionOptions: { level: 1 },

            //类型：Number默认值：0
            // 仅处理大于此大小的资产。以字节为单位。
            threshold: 8192,

            //类型：Number默认值：0.8
            // 仅压缩比该比率更好的资产（minRatio = Compressed Size / Original Size）。例如：您的image.png文件大小为1024b，压缩版本的文件大小为768b，因此minRatio等于0.75。
            // 换句话说，资产将在Compressed Size / Original Size价值减去minRatio价值时处理。
            // 您可以1使用价值来处理比原始资产小的资产。
            // 使用值Infinity来处理所有资产，即使它们大于原始大小或原始大小为0字节（在为AWS预压缩所有资产时很有用）。
            // 使用值Number.MAX_SAFE_INTEGER来处理所有资产，即使它们大于原始大小，也要排除原始大小为0字节的资产。
            minRatio: 0.8,

            //类型：String|Function默认值："[path][base].gz"
            //目标资产文件名。
            filename: '[path][base].gz',

            //类型：Boolean默认值：false
            // 是否删除原始资产。
            deleteOriginalAssets: false,

        }),

        new webpack.ProgressPlugin({
            activeModules: false,
            entries: true,
            handler(percentage, message, ...args) {
                // custom logic
            },
            modules: true,
            modulesCount: 5000,
            profile: false,
            dependencies: true,
            dependenciesCount: 10000,
            percentBy: null
        }),
        //打包后大小提示
        // new BundleAnalyzerPlugin()
        new CopyPlugin({
            patterns: [
                {
                    from: '*.txt'
                },
            ],
        })
    ]
}
