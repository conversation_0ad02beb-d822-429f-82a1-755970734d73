import React, { useState, useEffect, useImperativeHandle } from 'react';
import addSalesAmount from './addSalesAmount.less'

import { Form, Input, Picker, Stepper } from "antd-mobile";

import { http1 } from '../../../../../../../../utils/network'
import Big from 'big.js'
import _ from "lodash";

const AddSalesAmount = ({ salesInfo, setSalesInfo, salesAmountVisible, parentRef }) => {

    const [form] = Form.useForm()

    const prdName = Form.useWatch('prdName', form)
    const spec = Form.useWatch('spec', form)
    const buyNum = Form.useWatch('buyNum', form)
    const priceUnit = Form.useWatch('priceUnit', form)
    // console.log(spec)

    useImperativeHandle((parentRef), () => {
        return {
            childHandleAdd,
            formValidate,
            editFormData,
            resetData
        }
    })
    const childHandleAdd = () => {
        let values = form.getFieldsValue()
        return values
    }

    const formValidate = async () => {
        await form.validateFields()
    }

    const editFormData = (value) => {
        console.log(value);
        form.setFieldsValue({
            prdName: [value.prdName],
            spec: [value.spec],
            buyNum: value.buyNum,
            priceUnit: value.priceUnit,
            sumSales: value.sumSales,
        })
        // setSalesInfo({ ...salesInfo, productName: value.productName, specificationsName: value.specificationsName })
    }

    const resetData = () => {
        form.resetFields()
    }

    const [productList, setProductList] = useState([])
    const [productAllList, setProductAllList] = useState([])

    // 处理枚举数组格式
    const handleListProduct = (arr) => {
        const uniqueFunc = (arr, uniId) => {
            const res = new Map();
            return arr.filter((item) => !res.has(item[uniId]) && res.set(item[uniId], 1));
        }
        let temp = uniqueFunc(arr, 'prdName')
        temp = arr.length > 0 && temp.map(item => {
            return { label: item.prdName, value: item.prdName, prdBrand: item.prdBrand, prdCode: item.prdCode, threeLvlVarietCode: item.threeLvlVarietCode, threeLvlVarietName: item.threeLvlVarietName }
        })
        if (temp.length > 0) {
            return [temp]
        } else {
            return[]
        }
    }

    // 获取同一品类下的所有规格并去重
    const currentSpec = (prdName) => {
        try {
            // console.log(productAllList)
            const currentItems = productAllList.filter(d => d.prdName === prdName[0]);
            // console.log(currentItems);
            return [_.uniqBy(currentItems, 'spec').map(d => ({
                // label: d.prdBrand === '赛增' ? d.dosForm + '-' + d.spec : d.spec,
                label: d.spec,
                value: d.spec,
                specCode: d.specCode,
            }))]
        } catch (e) {
            return []
        }
    }

    useEffect(() => {
        http1.post('/meta/api/product/page', { size: -1, buCode: 'aestheticMedicine' }).then((res) => {
						const _product = handleListProduct(res.list);
            setProductList(_product)
            setProductAllList(res.list);
						try {
							if(_product[0] && _product[0].length === 1) {
								form.setFieldValue('prdName', [_product[0][0].value]);
								setSalesInfo({
									...salesInfo,
									prdBrand: _product[0][0].prdBrand,
									prdCode: _product[0][0].prdCode,
									threeLvlVarietCode: _product[0][0].threeLvlVarietCode,
									threeLvlVarietName: _product[0][0].threeLvlVarietName
								})
								const _currentSpec = currentSpec([_product[0][0].value])[0];
								if(_currentSpec && _currentSpec.length === 1) {
									form.setFieldValue('spec', [_currentSpec[0].value])
								}
							}
						} catch (e) {
							//
						}
        })
    }, [])


    const handleVolume = (value) => {
        let str = Number(value * priceUnit).toFixed(2)
        console.log(str);
        if (str.indexOf('.') !== -1) {
            str = str.slice(0, str.indexOf('.') + 3)
            console.log(str);
        }
        form.setFieldsValue({ sumSales: str })
    }

    const handlePriceUnit = (value) => {
        if (value.indexOf(".") !== -1) {
            value = value.slice(0, value.indexOf(".") + 3);
        }
        let x = new Big(value)
        let str = x.times(buyNum)
        console.log(str);
        form.setFieldsValue({ sumSales: str })
    }

    return (
        <div className={addSalesAmount.addSalesAmount}>
            <Form form={form}
                initialValues={{ buyNum: 0 }}>
                <Form.Item
                    name='prdName'
                    label='产品'
                    trigger='onConfirm'
                    rules={[{ required: true }]}
                    onClick={(e, pickerRef) => {
                        pickerRef.current?.open()
                    }}
                >
                    <Picker
                        columns={productList}
                        onConfirm={(v, extend) => {
                            // console.log(v, extend.items);
                            form.setFieldValue('spec', [])
                            setSalesInfo({
                                ...salesInfo,
                                prdBrand: extend.items[0].prdBrand,
                                prdCode: extend.items[0].prdCode,
                                threeLvlVarietCode: extend.items[0].threeLvlVarietCode,
                                threeLvlVarietName: extend.items[0].threeLvlVarietName
                            })
														const _currentSpec = currentSpec(v)[0];
														if(_currentSpec && _currentSpec.length === 1) {
															form.setFieldValue('spec', [_currentSpec[0].value])
														}
                        }}
                    >
                        {items => {
                            if (items.every(item => item === null)) {
                                return ''
                            } else {
                                return items.map(item => item?.label ?? '未选择')
                            }
                        }}
                    </Picker>
                </Form.Item>
                <Form.Item
                    name='spec'
                    label='规格'
                    disabled={!prdName}
                    trigger='onConfirm'
                    rules={[{ required: true }]}
                    onClick={(e, pickerRef) => {
                        pickerRef.current?.open()
                    }}
                >
                    <Picker
                        columns={currentSpec(prdName)}
                        onConfirm={(v, extend) => {
                            setSalesInfo({ ...salesInfo, productCode: extend.items[0].specCode })
                        }}
                    >
                        {items => {
                            console.log(items)
                            if (items.every(item => item === null)) {
                                return ''
                            } else {
                                return items.map(item => item?.label ?? '未选择')
                            }
                        }}
                    </Picker>
                </Form.Item>
                <Form.Item
                    name='buyNum'
                    rules={[{type: 'number'},{ required: true }]}
                    label='支数'
                >
                    <Stepper
                        min={0}
                        digits={0}
                        onChange={handleVolume}
                    />
                </Form.Item>
                <Form.Item
                    name='priceUnit'
                    label='回款单价(元)'
                    rules={[{ required: true }]}
                    getValueFromEvent={(value) => {
                        return value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); // 只能输入两位小数
                    }}
                >
                    <Input type="number" min={0}
                        onChange={handlePriceUnit}
                    />
                </Form.Item>
                <Form.Item
                    name='sumSales'
                    label='纯销金额(元)'
                    rules={[{ required: true }]}
                >
                    <Input disabled type="number" min={0} />
                </Form.Item>
            </Form>
        </div>
    )
}

export default AddSalesAmount
