import React, {useEffect, useRef, useState} from 'react';
import styled from 'styled-components';
import {Button, Space, Toast} from "antd-mobile";
import {useNavigate} from "react-router-dom";

const Wrapper = styled.div`
  margin-bottom: 32px;
  .tab-bar {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      .tab-item {
        height: 42px;
        line-height: 42px;
        position: relative;
        margin-right: 32px;
        cursor: pointer;
        color: #4E595E;
      }
      .tab-item-selected {
        color: #2551F2;
        &:after {
          content: ' ';
          display: block;
          height: 1px;
          width: 100%;
          position: absolute;
          bottom: 0;
          left: 0;
          background: #2551F2;
        }
      }
    }
    
  }
`;

const Tabs = props => {
  const navigate = useNavigate();

  const { roleInfo = {}, readLevel } = props;
  const { level } = roleInfo;

  const [loading, setLoading] = useState(false);
  const uploader = useRef(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [tabKey, setTabKey] = useState('1')

  useEffect(() => {
    props.onChange(tabKey)
  }, [tabKey])

  const downloadBinaryFile = (data, fileName, mimeType) => {
    const blob = new Blob([data], { type: mimeType });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
  }

  const handleDownload = async () => {
    try {
      setLoading(true);
      Toast.show({
        content: '下载中...',
        icon: 'loading',
        duration: 0
      })

      const name = {
        6: '代表周新增模版',
        5: '地区周新增模版',
        4: '大区周新增模版',
        3: '区域周新增模版'
      }

      const fileName = `${name[level]}.xlsx`; // 指定下载文件的名称
      const mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"; // 指定MIME类型
      const binaryData = await http1.post('/pediatricsEstimate/excel/download',{}, {responseType: 'blob'})

      await downloadBinaryFile(binaryData, fileName, mimeType);
      setLoading(false);
      Toast.clear();
    } catch (e) {
      if(e && e.message) {
        Toast.show(e.message)
      }
      setLoading(false);
      Toast.clear();
    }
  }

  const chooseFile = () => {
    uploader.current.click()
  }

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    setSelectedFile(file);
  };

  const tabs = [
    {
      label: '机构视图',
      value: '1'
    },
    {
      label: '人员视图',
      value: '2'
    },
    {
      label: '科室视图',
      value: '3'
    },
  ]

  const tabs2 = [
    {
      label: '机构视图',
      value: '1'
    },
    {
      label: '科室视图',
      value: '3'
    },
  ]

  const handleToReport = () => {
    navigate(`/pediatricsWeekEstimate/report?deptcode=${roleInfo.deptcode}&estDate=${roleInfo.estDate}&empNo=${roleInfo.empNo || ''}`)
  }

  return (
    <Wrapper>
      <div className="tab-bar">
        <div className="left">
          {
            ((level === 6 || readLevel === 5) ? tabs2 : tabs).map(item => {
              return (
                <div
                  key={item.value}
                  className={item.value === tabKey ? 'tab-item tab-item-selected' : 'tab-item'}
                  onClick={() => setTabKey(item.value)}
                >
                  {item.label}
                </div>
              )
            })
          }
        </div>
        <div className="right">
          <Space>
            {/*<Button color="primary" onClick={handleDownload} loading={loading}>下载模板</Button>*/}
            {/*<Button color="primary" loading={loading} onClick={chooseFile}>上传数据</Button>*/}
            {
              // 只有地区经理 可以上报数据
              (roleInfo.level === 6) && (
                <Button color="primary" loading={loading} onClick={handleToReport}>数据上报</Button>
              )
            }
          </Space>
        </div>
      </div>
      <input type="file" onChange={handleFileChange} id={'fileInput'} ref={uploader} accept={'.xlsx'} multiple={false} style={{ opacity: 0, width: 0, height: 0 }} />
    </Wrapper>
  )
}

export default Tabs;
