import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  createRef,
} from "react";
import { createHashHistory } from "history";
import dataForm from "./dataForm.less";

import { useNavigate, useLocation } from "react-router-dom";

import CategoryLayoutUpload from "../../../../../../public/CategoryLayoutUpload/categoryLayoutUpload";

import {
  Button,
  List,
  Form,
  Input,
  Dialog,
  DatePicker,
  Space,
  Radio,
  Picker,
  Popup,
  PickerView,
  SearchBar,
  Toast,
} from "antd-mobile";
import dayjs from "dayjs";
import PostAddIcon from "@mui/icons-material/PostAdd";
import DoubleArrowIcon from "@mui/icons-material/DoubleArrow";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";

import { http1 } from "../../../../../../../utils/network";
import _ from "lodash";

const DataForm = ({ formType }) => {
  const [form] = Form.useForm();
  // const hospitalId = Form.useWatch('hospitalId', form)
  const hasSales = Form.useWatch("hasSale", form);
  const childRef = useRef(null);

  const navigate = useNavigate();

  const [detail, setDetail] = useState({});
  const [products, setProducts] = useState([]);
  const [product, setProduct] = useState({
    drugName: null,
  });
  const [empNo, setEmpNo] = useState(""); // 上报人 工号
  const info = useLocation();
  if (info.state) {
    var id = info.state.id;
    var time = info.state.time;
    var reporter = info.state.reporter;
  }
  console.log(reporter);
  useEffect(() => {
    // 填报人信息
    http1.post("/account/api/userInfo", {}).then((res) => {
      setEmpname(res.empname);

      setEmpNo(res.empno);
      setUserInfo(res);

      http1
        .post("/hospital/list/queryByArea", {
          page: 1,
          size: 20,
          hospitalName: hospitalName,
          buCode: "neuroendocrine",
          empNo: res.empno,
        })
        .then((res) => {
          let temp = [];
          temp =
            res.list &&
            res.list.length > 0 &&
            res.list.map((item) => {
              return { label: item.hospitalName, value: item.hospitalId };
            });
          if (temp.length > 0) {
            setDataShow(true);
            setHospitalList([temp]);
          } else {
            setDataShow(true);
            setHospitalList([[{ label: "", value: "" }]]);
          }
        });
    });
  }, []);

  const [dataTime, setDataTime] = useState("");

  const [empname, setEmpname] = useState(""); // 上报人
  const [userInfo, setUserInfo] = useState();

  const deleteTable = () => {
    Dialog.confirm({
      content: "删除该上报后，该数据一并删除，是否确认？",
      onConfirm: () => {
        http1.post(`/cust/delete?id=${id}`).then((res) => {
          console.log(res);
          Toast.show({
            icon: "success",
            content: "删除成功",
          });
          navigate(
            "/category/immuneDaily/immuneDailyReportUpload/dataQuery"
          );
        });
      },
    });
  };

  useEffect(() => {
    getProduct();
  }, []);

  //回显数据医院等
  useEffect(() => {
    if (formType === "update") {
      http1.get(`/cust/detail?id=${id}`).then((res) => {
        setEmpname(res.createdName);
        setHospitalInfo({
          ...hospitalInfo,
          hospitalId: res.hospitalId,
          hospitalName: res.hospitalName,
        });
        let showTime = dayjs(res.insertTime).format("YYYY-MM-DD");
        setDataTime(showTime);
        setDetail(res || {});
        form.setFieldsValue({
          hospitalType: res.hospitalType,
          hospitalId: [res.hospitalId],
          insDept: [res.insDept],
          product: [res.productThreeCode],
          num: res.num,
          indication: [res.indication],
        });
      });
    }
  }, []);

  const [commonInfo, setCommonInfo] = useState({
    productName: "",
    patientName: "",
    specificationName: "",
  });

  const history = createHashHistory();

  const handleTarget = () => {
    history.back();
  };

  const [hospitalList, setHospitalList] = useState([]); //合作医院
  const [hospitalShow, setHospitalShow] = useState(false);
  const [hospitalInfo, setHospitalInfo] = useState({
    hospitalId: "",
    hospitalName: "",
  });

  const [tempHospital, setTempHospital] = useState({});

  const handleConfirmHospital = () => {
    setHospitalInfo({
      ...hospitalInfo,
      hospitalId: tempHospital.value,
      hospitalName: tempHospital.label,
    });
    form.setFields([
      {
        name: "hospitalId",
        value: tempHospital.value,
        errors: null,
      },
    ]);
    form.setFieldValue("hospitalId", tempHospital.value);
    setHospitalShow(false);
  };

  const [hospitalName, setHospitalName] = useState("");
  const handleQueryName = (val) => {
    setHospitalName(val);
  };

  const [dataShow, setDataShow] = useState(true);
  const onSearchHospital = () => {
    http1
      .post("/hospital/list/queryByArea", {
        page: 1,
        size: 20,
        hospitalName: hospitalName,
        buCode: "neuroendocrine",
        empNo: empNo,
      })
      .then((res) => {
        let temp = [];
        temp =
          res.list &&
          res.list.length > 0 &&
          res.list.map((item) => {
            return { label: item.hospitalName, value: item.hospitalId };
          });
        if (temp.length > 0) {
          setDataShow(true);
          setHospitalList([temp]);
        } else {
          setDataShow(false);
          setHospitalList([[{ label: "", value: "" }]]);
        }
      });
  };

  const [btnDisabled, setBtnDisabled] = useState(false);

  const onSubmit = async () => {
    if (hospitalInfo.hospitalId) {
      form.setFieldValue("hospitalId", hospitalInfo.hospitalId);
    }
    await form.validateFields();

    let temp = {
      hospitalId: hospitalInfo.hospitalId,
    };
    const values = form.getFieldsValue();

    // const currentItem =
    //   products.find((d) => d.threeLvlVarietCode === values.product[0]) || {}
    // values.productCode = null;
    // console.log(currentItem, values, 'currentItemcurrentItem')
    // values['productThreeCode'] = currentItem.threeLvlVarietCode
    // values['productThreeName'] = currentItem.threeLvlVarietName
    // values['productTwoCode'] = currentItem.twoLvlVarietCode
    // values['productTwoName'] = currentItem.twoLvlVarietName
    // values['prdBrand'] = currentItem.prdBrand

    Object.assign(temp, values, {
      insDept: values.insDept[0],
      hospitalName: hospitalInfo.hospitalName,
      indication: values.indication[0],
    });
    if (values.num > 999 || values.num < 1) {
      Dialog.alert({
        content: "新增数：>0,<999正整数",
      });
      return false;
    }

    if (formType === "update") {
      setBtnDisabled(true);
      await http1.post("/cust/update", { ...temp, id: id }).then(
        (res) => {
          setBtnDisabled(false);
          Dialog.alert({
            content: "更新成功",
          });
          navigate(
            "/category/immuneDaily/immuneDailyReportUpload/dataQuery",
            { state: { hasSales } }
          );
        },
        (err) => {
          setBtnDisabled(false);
          Dialog.alert({
            content: "服务器异常，请稍后重试~",
          });
        }
      );
    } else {
      setBtnDisabled(true);
      await http1.post("/cust/insert", temp).then(
        (res) => {
          setBtnDisabled(false);
          Dialog.alert({
            content: "新增成功",
          });
          navigate(
            "/category/immuneDaily/immuneDailyReportUpload/dataQuery",
            { state: { hasSales } }
          );
        },
        (err) => {
          setBtnDisabled(false);
          Dialog.alert({
            content: "服务器异常，请稍后重试~",
          });
        }
      );
    }
  };

  const getProduct = async () => {
    try {
      const res =
        (await http1.post("/meta/api/product/page", {
          buCode: "neuroendocrine",
          size: -1,
        })) || {};
      setProducts(res.list || []);
    } catch (e) {
      if (e && e.message) {
        Toast.show(e.message);
      }
    }
  };

  const realProducts = () =>
    _.uniqBy(products, "threeLvlVarietCode").map((d) => ({
      label: d.threeLvlVarietName,
      value: d.threeLvlVarietCode,
    }));

  // ①内分泌科②神经内科③神经外科④康复科⑤神经重症⑥男科 ⑦其他？
  const deptOptions = [
    {
      label: "内分泌科",
      value: "内分泌科",
    },
    {
      label: "神经内科",
      value: "神经内科",
    },
    {
      label: "神经外科",
      value: "神经外科",
    },
    {
      label: "康复科",
      value: "康复科",
    },
    {
      label: "神经重症",
      value: "神经重症",
    },
    {
      label: "男科",
      value: "男科",
    },
    {
      label: "其他",
      value: "其他",
    },
  ];

  return (
    <div className={dataForm.neuroendocrineDayReportUpload}>
      <div className={dataForm.top}>
        <ChevronLeftIcon
          sx={{ fontSize: 50 }}
          onClick={() => {
            handleTarget();
          }}
        />
        免疫事业部
        <DoubleArrowIcon fontSize={"small"} />
        <span>日战报</span>
        <DoubleArrowIcon fontSize={"small"} />
        合作客户上报
      </div>
      <div className={dataForm.content}>
        <CategoryLayoutUpload>
          {/*header*/}
          <>
            <div className={dataForm.header}>
              <div className={dataForm.headerContext}>
                <PostAddIcon className={dataForm.fontMain} fontSize={"large"} />
                <div style={{ fontWeight: "bold" }}>数据上报</div>
              </div>
              <div className={dataForm.headerContext}>
                <span
                  className={dataForm.fontMain}
                  style={{ fontWeight: "bold" }}
                >
                  上报人:
                </span>
                <span className={dataForm.fontSecond}>{empname}</span>
              </div>
            </div>
          </>
          {/*body*/}
          <>
            <div className={dataForm.container}>
              <Form form={form} initialValues={{ hasSale: 1 }}>
                <Form.Item disabled label="上报日期" trigger="onConfirm">
                  <DatePicker>
                    {(value) =>
                      formType === "update"
                        ? dataTime
                        : dayjs(new Date()).format("YYYY-MM-DD")
                    }
                  </DatePicker>
                </Form.Item>
                <Form.Item
                  name="hospitalType"
                  label="医院类型"
                  rules={[{ required: true }]}
                >
                  <Radio.Group>
                    <Space>
                      <Radio value={"存量医院"}>存量医院</Radio>
                      <Radio value={"破冰医院"}>破冰医院</Radio>
                    </Space>
                  </Radio.Group>
                </Form.Item>
                <Form.Item
                  name="hospitalId"
                  label="合作医院"
                  trigger="onConfirm"
                  rules={[{ required: true }]}
                  onClick={() => setHospitalShow(true)}
                >
                  {hospitalInfo.hospitalName ? (
                    <Input value={hospitalInfo.hospitalName} />
                  ) : null}
                  <Popup
                    visible={hospitalShow}
                    onMaskClick={() => {
                      setHospitalShow(false);
                    }}
                    bodyStyle={{
                      borderTopLeftRadius: "8px",
                      borderTopRightRadius: "8px",
                      minHeight: "40vh",
                    }}
                  >
                    <div
                      className={"adm-picker-header"}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        height: "42px",
                      }}
                    >
                      <span
                        className={"adm-picker-header-button"}
                        style={{
                          fontSize: "15px",
                          color: "var(--gensci-main)",
                        }}
                        onClick={() => setHospitalShow(false)}
                      >
                        取消
                      </span>
                      <span
                        className={"adm-picker-header-button"}
                        style={{
                          fontSize: "15px",
                          color: "var(--gensci-main)",
                        }}
                        onClick={handleConfirmHospital}
                      >
                        确认
                      </span>
                    </div>
                    <div
                      className={
                        dataForm.neuroendocrineDayReportUpload__searchContainer
                      }
                    >
                      <SearchBar
                        placeholder="请输入内容"
                        onChange={(e) => handleQueryName(e)}
                        style={{ marginRight: "10px", flex: "1" }}
                      />
                      <Button
                        size={"small"}
                        color={"primary"}
                        onClick={() => onSearchHospital()}
                      >
                        查询
                      </Button>
                    </div>
                    {dataShow ? (
                      <PickerView
                        columns={hospitalList}
                        onChange={(value, extend) => {
                          setTempHospital(extend.items[0] && extend.items[0]);
                        }}
                      ></PickerView>
                    ) : (
                      <div
                        style={{
                          width: "60px",
                          margin: "80px auto",
                          color: "#ccc",
                        }}
                      >
                        暂无数据
                      </div>
                    )}
                  </Popup>
                </Form.Item>
                <Form.Item
                  name="insDept"
                  label="科室"
                  rules={[{ required: true }]}
                  arrow
                  trigger="onConfirm"
                  onClick={(e, pickref) => {
                    pickref.current?.open();
                  }}
                >
                  <Picker columns={[deptOptions]} onConfirm={(v, extend) => {}}>
                    {(e) => {
                      return (
                        <div>
                          {(e && e[0] && e[0].label) || detail.insDept || (
                            <span style={{ color: "#ccc" }}>请选择</span>
                          )}
                        </div>
                      );
                    }}
                  </Picker>
                </Form.Item>
                {/*<Form.Item*/}
                {/*  name="product"*/}
                {/*  label="产品"*/}
                {/*  trigger="onConfirm"*/}
                {/*  rules={[{ required: true }]}*/}
                {/*  onClick={(e, pickref) => {*/}
                {/*    pickref.current?.open()*/}
                {/*  }}*/}
                {/*>*/}
                {/*  <Picker*/}
                {/*    columns={[realProducts()]}*/}
                {/*    onConfirm={(v, extend) => {*/}
                {/*      const [item] = extend.items*/}
                {/*      setCommonInfo({*/}
                {/*        ...commonInfo,*/}
                {/*        productName: extend.items[0].label,*/}
                {/*      })*/}
                {/*      setProduct({*/}
                {/*        ...product,*/}
                {/*        drugName: extend.items[0].label,*/}
                {/*      });*/}
                {/*    }}*/}
                {/*  >*/}
                {/*    {(e) => {*/}
                {/*      return (*/}
                {/*        <div>*/}
                {/*          {(e && e[0] && e[0].label) || detail.productThreeName || (*/}
                {/*            <span style={{ color: '#ccc' }}>请选择</span>*/}
                {/*          )}*/}
                {/*        </div>*/}
                {/*      )*/}
                {/*    }}*/}
                {/*  </Picker>*/}
                {/*</Form.Item>*/}

                <Form.Item
                  name="indication"
                  label="领域"
                  trigger="onConfirm"
                  rules={[{ required: true }]}
                  onClick={(e, pickref) => {
                    pickref.current?.open();
                  }}
                >
                  <Picker
                    columns={[
                      [
                        {
                          label: "长高",
                          value: "长高",
                        },
                        {
                          label: "经典",
                          value: "经典",
                        },
                        {
                          label: "特发-其它",
                          value: "特发-其它",
                        },
                        {
                          label: "TBI-神外领域",
                          value: "TBI-神外领域",
                        },
                        {
                          label: "TBI-康复领域",
                          value: "TBI-康复领域",
                        },
                        {
                          label: "金蓓欣",
                          value: "金蓓欣",
                        },
                      ],
                    ]}
                    onConfirm={(v, extend) => {}}
                  >
                    {(e) => {
                      return (
                        <div>
                          {(e && e[0] && e[0].label) || (
                            <span style={{ color: "#ccc" }}>请选择</span>
                          )}
                        </div>
                      );
                    }}
                  </Picker>
                </Form.Item>
                <Form.Item
                  name="num"
                  label="新增数"
                  rules={[
                    { required: true },
                    {
                      pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
                      message: "新增数只能为整数",
                    },
                  ]}
                  initialValue={detail.num}
                >
                  <Input type={"number"} placeholder="请填写" />
                </Form.Item>
              </Form>
            </div>
          </>
          {/*footer*/}
          <>
            {formType == "create" ? (
              <div className={dataForm.footer}>
                <Button
                  disabled={btnDisabled}
                  block
                  color="primary"
                  size="middle"
                  style={{ marginTop: "5px", width: "calc(100% - 20px)" }}
                  onClick={onSubmit}
                >
                  提交
                </Button>
              </div>
            ) : reporter === "management" ? null : (
              <div className={dataForm.fotter}>
                <Button
                  onClick={onSubmit}
                  block
                  color="primary"
                  size="middle"
                  style={{ marginTop: "5px", width: "calc(50% - 5px)" }}
                >
                  更新
                </Button>
                <Button
                  onClick={deleteTable}
                  block
                  color="primary"
                  size="middle"
                  style={{ marginTop: "5px", width: "calc(50% - 5px)" }}
                >
                  删除
                </Button>
              </div>
            )}
          </>
        </CategoryLayoutUpload>
      </div>
    </div>
  );
};

export default DataForm;
