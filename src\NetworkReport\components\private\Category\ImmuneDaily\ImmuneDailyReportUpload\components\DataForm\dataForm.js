import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  createRef,
} from "react";
import { createHashHistory } from "history";
import dataForm from "./dataForm.less";

import { useNavigate, useLocation } from "react-router-dom";

import CategoryLayoutUpload from "../../../../../../public/CategoryLayoutUpload/categoryLayoutUpload";
import AddSalesAmount from "./AddSalesAmount/addSalesAmount";

import {
  Button,
  List,
  Form,
  Input,
  Dialog,
  DatePicker,
  Space,
  Radio,
  Picker,
  Popup,
  PickerView,
  SearchBar,
  Toast,
} from "antd-mobile";
import dayjs from "dayjs";
import { DataGrid, GridActionsCellItem } from "@mui/x-data-grid";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import AnalyticsIcon from "@mui/icons-material/Analytics";
import PostAddIcon from "@mui/icons-material/PostAdd";
import DoubleArrowIcon from "@mui/icons-material/DoubleArrow";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";

import { http1 } from "../../../../../../../utils/network";
import { nanoid } from "nanoid";
import StructurePicker from "./StructurePicker";
import _ from "lodash";

const DataForm = ({ formType }) => {
  const [form] = Form.useForm();
  // const hospitalId = Form.useWatch('hospitalId', form)
  const hasSales = Form.useWatch("hasSale", form);
  const childRef = useRef(null);

  const navigate = useNavigate();

  const [detail, setDetail] = useState({});
  const [products, setProducts] = useState([]);
  const [canEdit, setCanEdit] = useState(false);

  const info = useLocation();
  if (info.state) {
    var id = info.state.id;
    var time = info.state.time;
    var reporter = info.state.reporter;
  }
  console.log(reporter);
  useEffect(() => {
    // 填报人信息
    http1.post("/account/api/userInfo", {}).then((res) => {
      setEmpname(res.empname);
      setEmpNo(res.empno);
      setUserInfo(res);

      http1
        .post("/immune/jur-ins", {
          pageNum: 1,
          pageSize: 20,
          hospitalName: hospitalName,
          // buCode: "neuroendocrine",
          empNo: res.empno,
        })
        .then((res) => {
          let temp = [];
          temp =
            res.records &&
            res.records.length > 0 &&
            res.records.map((item) => {
              return {
                label: item.hospitalName,
                value: item.hospitalId,
                hospitalCode: item.hospitalCode,
              };
            });
          if (temp.length > 0) {
            setDataShow(true);
            setHospitalList([temp]);
          } else {
            setDataShow(true);
            setHospitalList([[{ label: "", value: "" }]]);
          }
        });
    });
  }, []);

  const [dataTime, setDataTime] = useState("");

  const [empname, setEmpname] = useState(""); // 上报人
  const [empNo, setEmpNo] = useState(""); // 上报人 工号
  const [userInfo, setUserInfo] = useState();

  const deleteTable = () => {
    //删除表格
    Dialog.confirm({
      content: "删除该上报后，该数据一并删除，是否确认？",
      onConfirm: () => {
        http1
          .post("/immuneDaily/immuneDaily/delete", { id: id })
          .then((res) => {
            console.log(res);
            if (res.id) {
              Toast.show({
                icon: "success",
                content: "删除成功",
              });
              navigate(
                "/category/immuneDaily/immuneDailyReportUpload/dataQuery"
              );
            }
          });
      },
    });
  };
  //
  // useEffect(() => {
  //   getProduct();
  // }, []);

  //回显数据医院等
  useEffect(() => {
    if (formType === "update") {
      http1.post("/immuneDaily/immuneDaily/get", { id: id }).then((res) => {
        setEmpname(res.createdName);
        form.setFieldsValue({
          hasSale:
            res.hasVolume !== null && res.hasVolume !== undefined
              ? res.hasVolume
                ? 1
                : 2
              : null,
          hospitalType: res.hospitalType,
        });
        setHospitalInfo({
          ...hospitalInfo,
          hospitalId: res.hospitalId,
          hospitalName: res.hospitalName,
          hospitalCode: res.hospitalCode,
        });
        let showTime = dayjs(res.insertTime).format("YYYY-MM-DD");
        setDataTime(showTime);
        http1.get(`/can-edit?date=${showTime}`).then((res) => {
          setCanEdit(res);
        });
        setDetail(res);
        if (res.medicineHospitalId) {
          form.setFieldValue("medicineHospitalId", [res.medicineHospitalId]);
          form.setFieldValue("medicineHospitalCode", [
            res.medicineHospitalCode,
          ]);
        }
      });
    }
  }, []);

  //回显数据预估管理
  useEffect(() => {
    if (formType === "update") {
      if (reporter) {
        http1
          .post("/immuneDailyPatientManagement/management/query", {
            parentId: id,
            branchEmpNo: true,
          })
          .then((res) => {
            setMagementList(
              (res || []).map((d) => ({
                ...d,
                source:
                  (d.source !== null &&
                    d.source !== undefined &&
                    `${d.source}`) ||
                  null,
              }))
            );
          });

        http1
          .post("/immuneDailyPatientStatus/status/query", {
            parentId: id,
            branchEmpNo: true,
          })
          .then((res) => {
            setStatusList(res);
          });

        // http1
        //   .post('/immuneDailyPatientQuantity/quantity/query', {
        //     parentId: id,
        //     branchEmpNo: true,
        //   })
        //   .then((res) => {
        //     setSalesList(res)
        //   })

        // http1
        //   .post("/immuneDailyPatientCustomer/customer/query", {
        //     parentId: id,
        //     branchEmpNo: true,
        //   })
        //   .then((res) => {
        //     setCustomerList(res);
        //   });
      } else {
        http1
          .post("/immuneDailyPatientManagement/management/query", {
            parentId: id,
          })
          .then((res) => {
            setMagementList(
              (res || []).map((d) => ({
                ...d,
                source:
                  (d.source !== null &&
                    d.source !== undefined &&
                    `${d.source}`) ||
                  null,
              }))
            );
          });

        // http1
        //   .post("/immuneDailyPatientStatus/status/query", {
        //     parentId: id,
        //   })
        //   .then((res) => {
        //     setStatusList(res);
        //   });

        // http1
        //   .post('/immuneDailyPatientQuantity/quantity/query', {
        //     parentId: id,
        //   })
        //   .then((res) => {
        //     setSalesList(res)
        //   })

        // http1
        //   .post("/immuneDailyPatientCustomer/customer/query", {
        //     parentId: id,
        //   })
        //   .then((res) => {
        //     setCustomerList(res);
        //   });
      }
    }
  }, []);

  const [salesAmountVisible, setSalesAmountVisible] = useState(false); //预估管理新增弹出框

  const [salesAmountType, setSalesAmountType] = useState(false); //患者状态新增弹出框

  const [puremoney, setPuremoney] = useState(false); //纯销额新增弹出框

  const [newperson, setNewperson] = useState(false); //新客户新增弹出框

  const [formDialog, setformDialog] = useState("manager"); //定义弹什么管理的弹出框

  const [userRow, setUserRow] = useState({});
  const [typeUserRow, setTypeUserRow] = useState({});
  const [saleUserRow, setSaleUserRow] = useState({});
  const [salePersonRow, setSalePersonRow] = useState({});

  //不同新增点击事件赋值
  const addSalesAmount = async () => {
    setUserRow({});
    setCode3Info({
      doctorCode: "",
      doctorName: "",
      doctorSecCode: "",
      doctorSecName: "",
    });
    setformDialog("manager");
    setSalesAmountVisible(true);
  };

  const addSalesType = async () => {
    setformDialog("type");
    setTypeUserRow({});
    setCode3Info({
      doctorCode: "",
      doctorName: "",
      doctorSecCode: "",
      doctorSecName: "",
    });
    setSalesAmountType(true);
  };

  const addSalesMoney = async () => {
    setformDialog("money");
    setSaleUserRow({});
    setCode3Info({
      doctorCode: "",
      doctorName: "",
      doctorSecCode: "",
      doctorSecName: "",
    });
    setPuremoney(true);
  };

  const addSalesPerson = async () => {
    setformDialog("person");
    setSalePersonRow({});
    setCode3Info({
      doctorCode: "",
      doctorName: "",
      doctorSecCode: "",
      doctorSecName: "",
    });
    setNewperson(true);
  };

  const [code3Info, setCode3Info] = useState({
    doctorCode: "",
    doctorName: "",
    doctorSecCode: "",
    doctorSecName: "",
  });

  const [commonInfo, setCommonInfo] = useState({
    productName: "",
    patientName: "",
    specificationName: "",
  });

  // 预估管理
  const [magementList, setMagementList] = useState([]);

  const handleSubmitMangent = async () => {
    await childRef.current.validateForm();
    let values = childRef.current.handleFormData();
    if (values.newPatNum == 0 && values.oldPatNum == 0) {
      Toast.show("新增和存量数不可同时为零");
      return false;
    }

    values.patientType = values.patientType && values.patientType[0];
    values.productType = values.productType && values.productType[0];
    values.dept = values.dept && values.dept[0];
    values.specification = values.specification && values.specification[0];
    values.prdBrand = values.prdBrand && values.prdBrand[0];
    values.channel = values.channel && values.channel[0];
    values.type = values.type && values.type[0];
    const id = nanoid();
    values["sameForm"] = nanoid();

    // if (values.newPatNum == 0) {
    //   values.productType = undefined
    //   values.specification = undefined
    //   values.patientType = undefined
    //   values.newPatientPrice = undefined
    //   values.npVolume = undefined
    //   values.newPatientAmount = undefined
    // }
    //
    // if (values.oldPatNum == 0) {
    //   values.secProductType = undefined
    //   values.secSpecification = undefined
    //   values.source = undefined
    //   values.oldPatientPrice = undefined
    //   values.opVolume = undefined
    //   values.oldPatientAmount = undefined
    // }
    if (values.oldPatNum == "0") {
      values.opVolume = "0";
    }
    // 新增
    if (!userRow.id) {
      setMagementList([
        ...magementList,
        {
          doctorCode: code3Info.doctorCode,
          doctorName: code3Info.doctorName,
          doctorSecCode: code3Info.doctorSecCode,
          doctorSecName: code3Info.doctorSecName,
          ...values,
          productName: commonInfo.productName,
          patientName: commonInfo.patientName,
          source: values.source && values.source[0],
          oldPatientAmount:
            values.oldPatientPrice &&
            values.opVolume &&
            (values.oldPatientPrice * values.opVolume).toFixed(2),
          newPatientAmount:
            values.newPatientPrice &&
            values.npVolume &&
            (values.newPatientPrice * values.npVolume).toFixed(2),
          id: id,
        },
      ]);
    } else {
      setMagementList(
        magementList.map((item) => {
          if (item.id === userRow.id) {
            return {
              ...item,
              ...values,
              source: values.source && values.source[0],
              oldPatientAmount:
                values.oldPatientPrice &&
                values.opVolume &&
                values.oldPatientPrice * values.opVolume,
              newPatientAmount:
                values.newPatientPrice &&
                values.npVolume &&
                values.newPatientPrice * values.npVolume,
            };
          }
          return item;
        })
      );
    }
    setCode3Info({ ...code3Info, doctorName: null });
    setSalesAmountVisible(false);
  };

  const handleMangentCancel = () => {
    setCode3Info({ ...code3Info, doctorName: null });
    setSalesAmountVisible(false);
  };

  //预估管理表单头
  const columns = React.useMemo(
    () => [
      {
        headerName: "产品",
        field: "productType",
        type: "string",
      },
      // {
      //   headerName: "分类",
      //   field: "type",
      //   type: "string",
      // },
      // {
      //   headerName: "购药渠道",
      //   field: "channel",
      //   type: "string",
      // },
      {
        headerName: "品类",
        field: "prdBrand",
        type: "string",
      },
      {
        headerName: "规格",
        field: "specification",
        type: "string",
      },
      {
        headerName: "科室",
        field: "dept",
        type: "string",
      },
      // {
      //   headerName: "适应症",
      //   field: "patientType",
      //   type: "string",
      //   width: "120",
      // },
      // {
      //   headerName: "新增数",
      //   field: "newPatNum",
      //   type: "string",
      //   width: "120",
      // },
      {
        headerName: "新增单价",
        field: "newPatientPrice",
        width: "120",
        valueGetter: (params) => {
          return (
            params.row.newPatientPrice &&
            Number(params.row.newPatientPrice).toFixed(2)
          );
        },
      },
      {
        headerName: "新增产品数量",
        field: "npVolume",
        type: "string",
        width: "120",
      },
      {
        headerName: "新增金额",
        field: "newPatientAmount",
        valueGetter: (params) => {
          return (
            params.row.newPatientAmount &&
            Number(params.row.newPatientAmount).toFixed(2)
          );
        },
        width: "120",
      },
      // {
      //   headerName: "存量数",
      //   field: "oldPatNum",
      //   type: "string",
      //   width: "120",
      // },
      // { headerName: '产品', field: 'secProductThreeName', type: 'string',
      //   valueGetter: (params) => {
      //     return (
      //       params.row.secProductThreeName === '赛必健' ? params.row.secProductThreeName.replace('赛必健', '睿简') : params.row.secProductThreeName
      //     )
      //   }
      // },
      // { headerName: '编码3', field: 'doctorSecCode', type: 'string' },
      // { headerName: '剂型', field: 'secSpecification', type: 'string' },
      // {
      //   headerName: '来源',
      //   field: 'source',
      //   width: '120',
      //   valueGetter: (params) => {
      //     if (params.row.source === 0 || params.row.source === '0') {
      //       return '当月确定复购存量'
      //     } else if (params.row.source === 1 || params.row.source === '1') {
      //       return '当月停药恢复存量'
      //     } else if (params.row.source === 2 || params.row.source === '2') {
      //       return '历史停药恢复存量'
      //     } else {
      //       return ''
      //     }
      //   },
      // },
      // {
      //   headerName: "存量单价",
      //   field: "oldPatientPrice",
      //   type: "string",
      //   width: "120",
      // },
      // {
      //   headerName: "存量产品数量",
      //   field: "opVolume",
      //   type: "string",
      //   width: "120",
      // },
      // {
      //   headerName: "存量金额",
      //   field: "oldPatientAmount",
      //   type: "string",
      //   width: "120",
      //   valueGetter: (params) => {
      //     return (
      //       params.row.oldPatientAmount &&
      //       Number(params.row.oldPatientAmount).toFixed(2)
      //     );
      //   },
      // },
      {
        headerName: "操作",
        field: "操作",
        type: "actions",

        getActions: (params) => [
          <GridActionsCellItem
            icon={<EditIcon />}
            label="Edit"
            onClick={() => editUser(params.row)}
          />,
        ],
      },
    ],
    [deleteUser]
  );

  //预估管理删除行
  const deleteUser = (id) =>
    setMagementList((prevRows) => prevRows.filter((row) => row.id !== id));

  // 预估管理编辑
  const editUser = (data) => {
    setformDialog("manager");
    setUserRow({
      ...data,
      earlyBirdPlanNew: data.earlyPlan,
      earlyBirdPlanOld: data.oldEarlyPlan,
      // productType: data.productThreeCode,
      // secProductType: data.secProductThreeCode,
    });
    setCode3Info({
      doctorCode: data.doctorCode,
      doctorName: data.doctorName,
      doctorSecCode: data.doctorSecCode,
      doctorSecName: data.doctorSecName,
    });
    setSalesAmountVisible(true);
    console.log(data);
  };
  // 患者状态
  const [statustList, setStatusList] = useState([]);

  const handleSubmitType = async () => {
    await childRef.current.validateForm();
    let values = childRef.current.handleFormData();
    const currentItem =
      products.find((d) => d.threeLvlVarietCode === values.productType[0]) ||
      {};
    values.productType = values.productType && values.productType[0];
    console.log(currentItem, values, "currentItemcurrentItem");
    values["productThreeCode"] = currentItem.threeLvlVarietCode;
    values["productThreeName"] = currentItem.threeLvlVarietName;
    values["productTwoCode"] = currentItem.twoLvlVarietCode;
    values["productTwoName"] = currentItem.twoLvlVarietName;
    values["prdBrand"] = currentItem.prdBrand;

    let id = nanoid();
    const _values = {
      doctorCode: code3Info.doctorCode,
      doctorName: code3Info.doctorName,
      ...values,
      productName: commonInfo.productName,
    };

    // 新增
    if (!typeUserRow.id) {
      setStatusList([
        ...statustList,
        {
          ..._values,
          id: id,
        },
      ]);
    } else {
      setStatusList(
        statustList.map((item) => {
          if (item.id === typeUserRow.id) {
            return {
              ...item,
              ..._values,
            };
          } else {
            return item;
          }
        })
      );
    }
    setCode3Info({ ...code3Info, doctorName: null });
    setSalesAmountType(false);
    console.log(values);
  };

  const handleTypeCancel = () => {
    setCode3Info({ ...code3Info, doctorName: null });
    setSalesAmountType(false);
  };

  const typecolumns = React.useMemo(
    () => [
      { headerName: "编码3", field: "doctorCode", type: "string" },
      { headerName: "产品", field: "productThreeName", type: "string" },
      { headerName: "用药患者数", field: "upVolume", type: "string" },
      { headerName: "停药患者数", field: "spVolume", type: "string" },
      {
        headerName: "操作",
        field: "操作",
        type: "actions",

        getActions: (params) => [
          <GridActionsCellItem
            icon={<EditIcon />}
            label="Edit"
            onClick={() => editTypeUser(params.row)}
          />,
        ],
      },
    ],
    [typedeleteUser]
  );
  //患者状态删除行
  const typedeleteUser = (id) =>
    setStatusList((prevRows) => prevRows.filter((row) => row.id !== id));

  // 患者状态编辑
  const editTypeUser = (data) => {
    setformDialog("type");
    setTypeUserRow({
      ...data,
      productType: data.productThreeCode,
    });
    setCode3Info({
      doctorCode: data.doctorCode,
      doctorName: data.doctorName,
    });
    setSalesAmountType(true);
  };

  // 纯销额
  const [salesList, setSalesList] = useState([]);

  const handleSubmitSales = async () => {
    await childRef.current.validateForm();
    let values = childRef.current.handleFormData();
    const currentItem = products.find(
      (d) => d.specCode === values.specification[0]
    );
    values.productType = values.productType && values.productType[0];
    values.specification = currentItem.spec;
    values["productThreeCode"] = currentItem.threeLvlVarietCode;
    values["productThreeName"] = currentItem.threeLvlVarietName;
    values["productTwoCode"] = currentItem.twoLvlVarietCode;
    values["productTwoName"] = currentItem.twoLvlVarietName;
    values["prdBrand"] = currentItem.prdBrand;
    values["productCode"] = currentItem.specCode;
    values["price"] = Number(values.priceUnit * values.volume);
    let id = nanoid();

    const _values = {
      doctorCode: code3Info.doctorCode,
      doctorName: code3Info.doctorName,
      ...values,
      productName: commonInfo.productName,
      specificationName: commonInfo.specificationName,
    };

    // 新增
    if (!saleUserRow.id) {
      setSalesList([
        ...salesList,
        {
          ..._values,
          id: id,
        },
      ]);
    } else {
      setSalesList(
        salesList.map((item) => {
          if (item.id === saleUserRow.id) {
            return {
              ...item,
              ..._values,
            };
          } else {
            return item;
          }
        })
      );
    }
    setCode3Info({ ...code3Info, doctorName: null });
    setPuremoney(false);
    console.log(values);
  };

  const handleSalesCancel = () => {
    setCode3Info({ ...code3Info, doctorName: null });
    setPuremoney(false);
  };

  const salecolumns = React.useMemo(
    () => [
      { headerName: "编码3", field: "doctorCode", type: "string" },
      { headerName: "产品", field: "productThreeName", type: "string" },
      { headerName: "剂型", field: "specification", type: "string" },
      { headerName: "处方数量", field: "volume", type: "string", width: "120" },
      { headerName: "单价", field: "priceUnit", type: "string", width: "120" },
      { headerName: "金额", field: "price", type: "string", width: "120" },
      {
        headerName: "操作",
        field: "操作",
        type: "actions",

        getActions: (params) => [
          <GridActionsCellItem
            icon={<EditIcon />}
            label="Edit"
            onClick={() => editSaleUser(params.row)}
          />,
        ],
      },
    ],
    [saledeleteUser]
  );
  //纯销额删除行
  const saledeleteUser = (id) =>
    setSalesList((prevRows) => prevRows.filter((row) => row.id !== id));

  //纯销额 编辑行
  const editSaleUser = (data) => {
    setformDialog("money");
    setSaleUserRow({
      ...data,
      productType: data.productThreeCode,
    });
    setCode3Info({
      doctorCode: data.doctorCode,
      doctorName: data.doctorName,
    });
    setPuremoney(true);
  };

  // 新客户
  const [customerList, setCustomerList] = useState([]);

  const handleSubmitCenter = async () => {
    await childRef.current.validateForm();
    let values = childRef.current.handleFormData();
    const currentItem = products.find(
      (d) => d.threeLvlVarietCode === values.productType[0]
    );
    values.productType = values.productType && values.productType[0];
    values["productThreeCode"] = currentItem.threeLvlVarietCode;
    values["productThreeName"] = currentItem.threeLvlVarietName;
    values["productTwoCode"] = currentItem.twoLvlVarietCode;
    values["productTwoName"] = currentItem.twoLvlVarietName;
    values["prdBrand"] = currentItem.prdBrand;
    let id = nanoid();
    const _values = {
      doctorCode: code3Info.doctorCode,
      doctorName: code3Info.doctorName,
      ...values,
      productName: commonInfo.productName,
    };
    // 新增
    if (!salePersonRow.id) {
      setCustomerList([
        ...customerList,
        {
          ..._values,
          id: id,
        },
      ]);
    } else {
      setCustomerList(
        customerList.map((item) => {
          if (item.id === salePersonRow.id) {
            return {
              ...item,
              ..._values,
            };
          } else {
            return item;
          }
        })
      );
    }
    setCode3Info({ ...code3Info, doctorName: null });
    setNewperson(false);
    console.log(values);
  };

  const handleCenterCancel = () => {
    setCode3Info({ ...code3Info, doctorName: null });
    setNewperson(false);
  };

  const personcolumns = React.useMemo(
    () => [
      {
        headerName: "产品",
        field: "productThreeName",
        type: "string",
        valueGetter: (params) => {
          return params.row.productThreeName === "赛必健"
            ? params.row.productThreeName.replace("赛必健", "睿简")
            : params.row.productThreeName;
        },
      },
      { headerName: "编码3", field: "doctorCode", type: "string" },
      {
        headerName: "新客户产生时间",
        field: "insertTime",
        type: "string",
        valueFormatter: (val) =>
          dayjs(new Date(val.value)).format("YYYY-MM-DD"),
      },
      { headerName: "处方新增总数", field: "newPatNum", type: "string" },
      {
        headerName: "操作",
        field: "操作",
        type: "actions",

        getActions: (params) => [
          <GridActionsCellItem
            icon={<EditIcon />}
            label="Edit"
            onClick={() => editPerson(params.row)}
          />,
        ],
      },
    ],
    [persondeleteUser]
  );
  //客户删除行
  const persondeleteUser = (id) =>
    setCustomerList((prevRows) => prevRows.filter((row) => row.id !== id));

  // 客户编辑行
  const editPerson = (data) => {
    setformDialog("person");
    setSalePersonRow({
      ...data,
      productType: data.productThreeCode,
    });
    setCode3Info({
      doctorCode: data.doctorCode,
      doctorName: data.doctorName,
    });
    setNewperson(true);
  };

  const history = createHashHistory();

  const handleTarget = () => {
    history.back();
  };

  const [hospitalList, setHospitalList] = useState([]); //合作医院
  const [hospitalShow, setHospitalShow] = useState(false);
  const [hospitalInfo, setHospitalInfo] = useState({
    hospitalId: "",
    hospitalName: "",
  });

  const [tempHospital, setTempHospital] = useState({});

  const handleConfirmHospital = () => {
    setHospitalInfo({
      ...hospitalInfo,
      hospitalId: tempHospital.value,
      hospitalName: tempHospital.label,
      hospitalCode: tempHospital.hospitalCode,
    });
    form.setFields([
      {
        name: "hospitalId",
        value: tempHospital.value,
        errors: null,
      },
    ]);
    form.setFieldValue("hospitalId", tempHospital.value);
    form.setFieldValue("hospitalCode", tempHospital.hospitalCode);
    // 默认值取合作医院
    const medicineHospitalId = form.getFieldValue("medicineHospitalId") || [];
    if (!medicineHospitalId.length) {
      form.setFields([
        {
          name: "medicineHospitalId",
          value: [tempHospital.value],
          errors: null,
        },
      ]);
      form.setFields([
        {
          name: "medicineHospitalCode",
          value: [hospitalInfo.hospitalCode || tempHospital.hospitalCode],
          errors: null,
        },
      ]);
      setDetail({
        ...detail,
        medicineHospitalId: tempHospital.value,
        medicineHospitalName: tempHospital.label,
        medicineHospitalCode:
          hospitalInfo.hospitalCode || tempHospital.hospitalCode,
      });
    }

    setHospitalShow(false);
  };

  const [hospitalName, setHospitalName] = useState("");
  const handleQueryName = (val) => {
    setHospitalName(val);
  };

  const [dataShow, setDataShow] = useState(true);
  const onSearchHospital = () => {
    http1
      .post("/immune/jur-ins", {
        pageNum: 1,
        pageSize: 20,
        hospitalName: hospitalName,
        buCode: "neuroendocrine",
        empNo: empNo,
      })
      .then((res) => {
        let temp = [];
        temp =
          res.records &&
          res.records.length > 0 &&
          res.records.map((item) => {
            return {
              label: item.hospitalName,
              value: item.hospitalId,
              hospitalCode: item.hospitalCode,
            };
          });
        if (temp.length > 0) {
          setDataShow(true);
          setHospitalList([temp]);
        } else {
          setDataShow(false);
          setHospitalList([[{ label: "", value: "" }]]);
        }
      });
  };

  const [btnDisabled, setBtnDisabled] = useState(false);

  const onSubmit = async () => {
    if (hospitalInfo.hospitalId) {
      form.setFieldValue("hospitalId", hospitalInfo.hospitalId);
    }
    await form.validateFields();
    const _management = [];
    magementList.forEach((item) => {
      _management.push({
        id: undefined,
        specification: item.specification,
        productCode: item.productCode,
        newPatNum: item.newPatNum,
        npVolume: item.npVolume,
        productType: item.productType,
        patientType: item.patientType,
        newPatientPrice: item.newPatientPrice,
        prdBrand: item.prdBrand,
        dept: item.dept,
        channel: item.channel,
        type: item.type,
        // oldPatientAmount: item.oldPatientPrice * item.opVolume || 0,
        newPatientAmount: item.newPatientPrice * item.npVolume || 0,
        earlyPlan: item.earlyBirdPlanNew,
        sameForm: item.sameForm,
      });
      _management.push({
        id: undefined,
        productCode: item.secProductCode,
        specification: item.specification,
        productType: item.productType,
        oldPatNum: item.oldPatNum,
        source: item.source,
        oldPatientPrice: item.oldPatientPrice,
        opVolume: item.opVolume,
        productThreeCode: item.secProductThreeCode,
        productThreeName: item.secProductThreeName,
        productTwoCode: item.secProductTwoCode,
        productTwoName: item.secProductTwoName,
        prdBrand: item.prdBrand,
        patientType: item.patientType,
        dept: item.dept,
        channel: item.channel,
        type: item.type,
        oldPatientAmount: item.oldPatientPrice * item.opVolume || 0,
        sameForm: item.sameForm,
        earlyPlan: item.earlyBirdPlanOld,
        // newPatientAmount: item.newPatientPrice * item.npVolume || 0,
      });
    });

    var status = statustList.map((item) => {
      return {
        id: undefined,
        doctorCode: item.doctorCode,
        productType: item.productType,
        upVolume: item.upVolume,
        spVolume: item.spVolume,
        productThreeCode: item.productThreeCode,
        productThreeName: item.productThreeName,
        productTwoCode: item.productTwoCode,
        productTwoName: item.productTwoName,
        prdBrand: item.prdBrand,
      };
    });

    var sales = salesList.map((item) => {
      return {
        id: undefined,
        doctorCode: item.doctorCode,
        productType: item.productType,
        specification: item.specification,
        volume: item.volume,
        priceUnit: item.priceUnit,
        price: item.price,
        productThreeCode: item.productThreeCode,
        productThreeName: item.productThreeName,
        productTwoCode: item.productTwoCode,
        productTwoName: item.productTwoName,
        prdBrand: item.prdBrand,
        productCode: item.productCode,
      };
    });

    var customers = customerList.map((item) => {
      return {
        id: undefined,
        doctorCode: item.doctorCode,
        insertTime: new Date(item.insertTime).getTime(),
        productType: item.productType,
        npVolume: item.npVolume,
        newPatNum: item.newPatNum,
        productThreeCode: item.productThreeCode,
        productThreeName: item.productThreeName,
        productTwoCode: item.productTwoCode,
        productTwoName: item.productTwoName,
        prdBrand: item.prdBrand,
      };
    });
    let temp = {};
    const values = form.getFieldsValue();
    if (hasSales === 1) {
      temp = {
        hasVolume: true,
        hospitalId: hospitalInfo.hospitalId,
        hospitalCode: hospitalInfo.hospitalCode,
        hospitalType: values.hospitalType,
        management: _management,
        status: status,
        quantity: sales,
        customers: customers,
      };
    } else {
      temp = {
        hasVolume: false,
        management: _management,
        status: status,
        quantity: sales,
        customers: customers,
      };
    }

    temp.medicineHospitalId =
      values.medicineHospitalId && `${values.medicineHospitalId[0]}`;

    temp.medicineHospitalCode =
      (values.medicineHospitalCode && `${values.medicineHospitalCode[0]}`) ||
      hospitalInfo.hospitalCode;

    if (
      hasSales === 1 &&
      _management.length === 0 &&
      status.length === 0 &&
      sales.length === 0 &&
      customers.length === 0
    ) {
      Dialog.alert({
        content: "表格数据不能全为空",
        onConfirm: () => {
          console.log("Confirmed");
        },
      });
    } else {
      if (formType === "update") {
        setBtnDisabled(true);
        delete temp.doctorCode;
        await http1
          .post("/immuneDaily/immuneDaily/submit", { ...temp, id: id })
          .then(
            (res) => {
              setBtnDisabled(false);
              Dialog.alert({
                content: "更新成功",
              });
              navigate(
                "/category/immuneDaily/immuneDailyReportUpload/dataQuery",
                { state: { hasSales } }
              );
            },
            (err) => {
              setBtnDisabled(false);
              Dialog.alert({
                content: "服务器异常，请稍后重试~",
              });
            }
          );
      } else {
        setBtnDisabled(true);
        await http1.post("/immuneDaily/immuneDaily/submit", temp).then(
          (res) => {
            setBtnDisabled(false);
            Dialog.alert({
              content: "新增成功",
            });
            navigate(
              "/category/immuneDaily/immuneDailyReportUpload/dataQuery",
              { state: { hasSales } }
            );
          },
          (err) => {
            setBtnDisabled(false);
            Dialog.alert({
              content: "服务器异常，请稍后重试~",
            });
          }
        );
      }
    }
  };

  const getProduct = async () => {
    try {
      const res =
        (await http1.post("/meta/api/product/page", {
          buCode: "neuroendocrine",
          size: -1,
        })) || {};
      setProducts(res.list || []);
    } catch (e) {
      if (e && e.message) {
        Toast.show(e.message);
      }
    }
  };

  const realProducts = () =>
    _.uniqBy(products, "threeLvlVarietCode").map((d) => ({
      label:
        d.threeLvlVarietName === "赛必健"
          ? d.threeLvlVarietName.replace("赛必健", "睿简")
          : d.threeLvlVarietName,
      value: d.threeLvlVarietCode,
    }));

  return (
    <div className={dataForm.neuroendocrineDayReportUpload}>
      <div className={dataForm.top}>
        <ChevronLeftIcon
          sx={{ fontSize: 50 }}
          onClick={() => {
            handleTarget();
          }}
        />
        免疫事业部
        <DoubleArrowIcon fontSize={"small"} />
        <span>日战报</span>
        <DoubleArrowIcon fontSize={"small"} />
        数据上报
      </div>
      <div className={dataForm.content}>
        <CategoryLayoutUpload>
          {/*header*/}
          <>
            <div className={dataForm.header}>
              <div className={dataForm.headerContext}>
                <PostAddIcon className={dataForm.fontMain} fontSize={"large"} />
                <div style={{ fontWeight: "bold" }}>数据上报</div>
              </div>
              <div className={dataForm.headerContext}>
                <span
                  className={dataForm.fontMain}
                  style={{ fontWeight: "bold" }}
                >
                  上报人:
                </span>
                <span className={dataForm.fontSecond}>{empname}</span>
              </div>
            </div>
          </>
          {/*body*/}
          <>
            <div className={dataForm.container}>
              <Form form={form} initialValues={{ hasSale: 1 }}>
                <Form.Item label="上报日期" trigger="onConfirm" disabled>
                  <DatePicker>
                    {(value) =>
                      formType === "update"
                        ? dataTime
                        : dayjs(new Date()).format("YYYY-MM-DD")
                    }
                  </DatePicker>
                </Form.Item>
                {/*<Form.Item*/}
                {/*  name="uploadTime"*/}
                {/*  label="上报时间"*/}
                {/*  trigger="onConfirm"*/}
                {/*  onClick={(e, datePickerRef) => {*/}
                {/*    datePickerRef.current?.open();*/}
                {/*  }}*/}
                {/*>*/}
                {/*  <DatePicker max={new Date()} min={new Date('2024-12-29')}>*/}
                {/*    {(value) =>*/}
                {/*      value ? dayjs(value).format("YYYY-MM-DD") : dayjs(new Date()).format('YYYY-MM-DD')*/}
                {/*    }*/}
                {/*  </DatePicker>*/}
                {/*</Form.Item>*/}
                <Form.Item
                  name="hasSale"
                  label="当日是否有销量"
                  rules={[{ required: true }]}
                >
                  <Radio.Group disabled={!!magementList.length}>
                    <Space>
                      <Radio value={1}>有</Radio>
                      <Radio value={2}>无</Radio>
                    </Space>
                  </Radio.Group>
                </Form.Item>
                <Form.Item
                  name="deptCode"
                  label="销量所属部门"
                  trigger="onConfirm"
                  rules={[{ required: true }]}
                  onClick={() => openDept()}
                >
                  {deptInfoForm?.label ? (
                    <Input value={deptInfoForm?.label} />
                  ) : null}
                  <Popup
                    visible={deptVisible}
                    onMaskClick={() => {
                      closeDept();
                    }}
                    bodyStyle={{
                      borderTopLeftRadius: "8px",
                      borderTopRightRadius: "8px",
                      minHeight: "40vh",
                    }}
                  >
                    <div
                      className={"adm-picker-header"}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        height: "42px",
                      }}
                    >
                      <span
                        className={"adm-picker-header-button"}
                        style={{
                          fontSize: "15px",
                          color: "var(--gensci-main)",
                        }}
                        onClick={() => closeDept()}
                      >
                        取消
                      </span>
                      <span
                        className={"adm-picker-header-button"}
                        style={{
                          fontSize: "15px",
                          color: "var(--gensci-main)",
                        }}
                        onClick={() => deptConfirm()}
                      >
                        确认
                      </span>
                    </div>
                    {showData ? (
                      <PickerView
                        columns={[deptList]}
                        onChange={(value, extend) => {
                          setDeptInfo(extend.items[0]);
                        }}
                      />
                    ) : (
                      <div
                        style={{
                          width: "60px",
                          margin: "80px auto",
                          color: "#ccc",
                        }}
                      >
                        暂无数据
                      </div>
                    )}
                  </Popup>
                </Form.Item>
                <Form.Item
                  name="jobCode"
                  label="销量所属岗位"
                  trigger="onConfirm"
                  // rules={[{ required: true }]}
                  onClick={() => openJob()}
                >
                  {jobForm?.label ? <Input value={jobForm?.label} /> : null}
                  <Popup
                    visible={jobVisible}
                    onMaskClick={() => {
                      closeJob();
                    }}
                    bodyStyle={{
                      borderTopLeftRadius: "8px",
                      borderTopRightRadius: "8px",
                      minHeight: "40vh",
                    }}
                  >
                    <div
                      className={"adm-picker-header"}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        height: "42px",
                      }}
                    >
                      <span
                        className={"adm-picker-header-button"}
                        style={{
                          fontSize: "15px",
                          color: "var(--gensci-main)",
                        }}
                        onClick={() => closeJob()}
                      >
                        取消
                      </span>
                      <span
                        className={"adm-picker-header-button"}
                        style={{
                          fontSize: "15px",
                          color: "var(--gensci-main)",
                        }}
                        onClick={() => jobConfirm()}
                      >
                        确认
                      </span>
                    </div>
                    {showData ? (
                      <PickerView
                        columns={[jobList]}
                        onChange={(value, extend) => {
                          setJobInfo(extend.items[0]);
                        }}
                      />
                    ) : (
                      <div
                        style={{
                          width: "60px",
                          margin: "80px auto",
                          color: "#ccc",
                        }}
                      >
                        暂无数据
                      </div>
                    )}
                  </Popup>
                </Form.Item>
                {hasSales === 2 ? null : (
                  <Form.Item
                    name="hospitalId"
                    label="合作医院"
                    trigger="onConfirm"
                    rules={[{ required: true }]}
                    onClick={() => setHospitalShow(true)}
                  >
                    {hospitalInfo.hospitalName ? (
                      <Input value={hospitalInfo.hospitalName} />
                    ) : null}
                    <Popup
                      visible={hospitalShow}
                      onMaskClick={() => {
                        setHospitalShow(false);
                      }}
                      bodyStyle={{
                        borderTopLeftRadius: "8px",
                        borderTopRightRadius: "8px",
                        minHeight: "40vh",
                      }}
                    >
                      <div
                        className={"adm-picker-header"}
                        style={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                          height: "42px",
                        }}
                      >
                        <span
                          className={"adm-picker-header-button"}
                          style={{
                            fontSize: "15px",
                            color: "var(--gensci-main)",
                          }}
                          onClick={() => setHospitalShow(false)}
                        >
                          取消
                        </span>
                        <span
                          className={"adm-picker-header-button"}
                          style={{
                            fontSize: "15px",
                            color: "var(--gensci-main)",
                          }}
                          onClick={handleConfirmHospital}
                        >
                          确认
                        </span>
                      </div>
                      <div
                        className={
                          dataForm.neuroendocrineDayReportUpload__searchContainer
                        }
                      >
                        <SearchBar
                          placeholder="请输入内容"
                          onChange={(e) => handleQueryName(e)}
                          style={{ marginRight: "10px", flex: "1" }}
                        />
                        <Button
                          size={"small"}
                          color={"primary"}
                          onClick={() => onSearchHospital()}
                        >
                          查询
                        </Button>
                      </div>
                      {dataShow ? (
                        <PickerView
                          columns={hospitalList}
                          onChange={(value, extend) => {
                            setTempHospital(extend.items[0] && extend.items[0]);
                          }}
                        ></PickerView>
                      ) : (
                        <div
                          style={{
                            width: "60px",
                            margin: "80px auto",
                            color: "#ccc",
                          }}
                        >
                          暂无数据
                        </div>
                      )}
                    </Popup>
                  </Form.Item>
                )}
                {hasSales === 2 ? null : (
                  <Form.Item
                    name="medicineHospitalId"
                    label="购药机构"
                    rules={[{ required: true }]}
                    arrow
                  >
                    <StructurePicker
                      data={{
                        organizationId: detail.medicineHospitalId,
                        organizationName: detail.medicineHospitalName,
                      }}
                      hiddenOther
                      noCompass
                    />
                  </Form.Item>
                )}
              </Form>
              <List header=" ">
                <List.Item>
                  <div className={dataForm.countFunc}>
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <AnalyticsIcon
                        className={dataForm.fontMain}
                        fontSize={"large"}
                      />
                      <span style={{ fontWeight: "bold", marginRight: "5px" }}>
                        预估管理
                      </span>
                    </div>
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <span style={{ color: "#ababab", marginRight: "5px" }}>
                        (单位: 人)
                      </span>
                      <Button
                        disabled={!hospitalInfo.hospitalId || hasSales === 2}
                        size="small"
                        color="primary"
                        onClick={addSalesAmount}
                      >
                        新增
                      </Button>
                      <Dialog
                        destroyOnClose
                        visible={salesAmountVisible}
                        title={userRow.id ? "预估管理-编辑" : "预估管理-新增"}
                        content={
                          <AddSalesAmount
                            childRef={childRef}
                            code3Info={code3Info}
                            setCode3Info={setCode3Info}
                            commonInfo={commonInfo}
                            setCommonInfo={setCommonInfo}
                            hospitalId={hospitalInfo.hospitalId}
                            formDialog={formDialog}
                            products={[realProducts()]}
                            fullProducts={products}
                            itemData={userRow}
                            visible={salesAmountVisible}
                            userInfo={userInfo}
                          />
                        }
                        closeOnAction
                        actions={[
                          [
                            {
                              key: "cancel",
                              text: "取消",
                              danger: true,
                              onClick: handleMangentCancel,
                            },
                            {
                              key: "del",
                              text: "删除",
                              danger: true,
                              onClick: () => {
                                Dialog.confirm({
                                  content: "确认删除吗？",
                                  onConfirm: async () => {
                                    deleteUser(userRow.id);
                                    setSalesAmountVisible(false);
                                  },
                                });
                              },
                            },
                            {
                              key: "confirm",
                              text: "确定",
                              bold: true,
                              onClick: handleSubmitMangent,
                            },
                          ].filter((d) => {
                            if (userRow.id) {
                              return true;
                            } else {
                              return d.key !== "del";
                            }
                          }),
                        ]}
                      />
                    </div>
                  </div>
                  {/*预估管理*/}
                  <DataGrid
                    columns={columns}
                    rows={magementList}
                    hideFooter={true}
                    style={{ height: "220px" }}
                    disableColumnMenu
                  />
                </List.Item>

                {/*<List.Item>
                                    <div className={dataForm.countFunc}>
                                        <div style={{ display: 'flex', alignItems: 'center' }}>
                                            <AnalyticsIcon className={dataForm.fontMain} fontSize={'large'} />
                                            <span style={{ fontWeight: 'bold', marginRight: '5px' }}>患者状态</span>
                                        </div>
                                        <div style={{ display: 'flex', alignItems: 'center' }}>
                                            <span style={{ color: '#ababab', marginRight: '5px' }}>(单位: 人)</span>
                                            <Button disabled={!hospitalInfo.hospitalId}  size='small' color='primary' onClick={addSalesType}>
                                                新增
                                            </Button>
                                            <Dialog
                                                destroyOnClose
                                                visible={salesAmountType}
                                                title={typeUserRow.id ? '患者状态编辑' : '患者状态患新增'}
                                                content={
																									<AddSalesAmount
																										code3Info={code3Info}
																										setCode3Info={setCode3Info}
																										commonInfo={commonInfo}
																										setCommonInfo={setCommonInfo}
																										childRef={childRef}
																										hospitalId={hospitalInfo.hospitalId}
																										formDialog={formDialog}
																										itemData={typeUserRow}
																										products={[realProducts()]}
																										fullProducts={products}
																										visible={salesAmountType}
																									/>
																								}
                                                closeOnAction
                                                actions={[
                                                    ([
                                                        {
                                                            key: 'cancel',
                                                            text: '取消',
                                                            danger: true,
                                                            onClick: handleTypeCancel
                                                        },
																											{
																													key: 'del',
																													text: '删除',
																													danger: true,
																													onClick: () => {
																														Dialog.confirm({
																															content: '确认删除吗？',
																															onConfirm: async () => {
																																typedeleteUser(typeUserRow.id);
																																setSalesAmountType(false);
																															},
																														})
																													}
																												},
                                                        {
                                                            key: 'confirm',
                                                            text: '确定',
                                                            bold: true,
                                                            onClick: handleSubmitType
                                                        },
                                                    ]).filter(d => {
																											if(typeUserRow.id) {
																												return true
																											}else {
																												return d.key !== 'del'
																											}
																										})
                                                ]}
                                            />
                                        </div>
                                    </div>
                                    <DataGrid columns={typecolumns} rows={statustList} hideFooter={true} style={{ height: '220px' }} disableColumnMenu />
                                </List.Item>*/}
                {/*<List.Item>*/}
                {/*    <div className={dataForm.countFunc}>*/}
                {/*        <div style={{ display: 'flex', alignItems: 'center' }}>*/}
                {/*            <AnalyticsIcon className={dataForm.fontMain} fontSize={'large'} />*/}
                {/*            <span style={{ fontWeight: 'bold', marginRight: '5px' }}>纯销额</span>*/}
                {/*        </div>*/}
                {/*        <div style={{ display: 'flex', alignItems: 'center' }}>*/}
                {/*            <span style={{ color: '#ababab', marginRight: '5px' }}>(单位: 元)</span>*/}
                {/*            <Button disabled={!hospitalInfo.hospitalId} size='small' color='primary' onClick={addSalesMoney}>*/}
                {/*                新增*/}
                {/*            </Button>*/}
                {/*            <Dialog*/}
                {/*                destroyOnClose*/}
                {/*                visible={puremoney}*/}
                {/*                title={saleUserRow.id ? '纯销额编辑' : '纯销额新增'}*/}
                {/*                content={*/}
                {/*									<AddSalesAmount*/}
                {/*										code3Info={code3Info}*/}
                {/*										setCode3Info={setCode3Info}*/}
                {/*										commonInfo={commonInfo}*/}
                {/*										setCommonInfo={setCommonInfo}*/}
                {/*										childRef={childRef}*/}
                {/*										hospitalId={hospitalInfo.hospitalId}*/}
                {/*										formDialog={formDialog}*/}
                {/*										products={[realProducts()]}*/}
                {/*										fullProducts={products}*/}
                {/*										itemData={saleUserRow}*/}
                {/*										visible={puremoney}*/}
                {/*									/>*/}
                {/*								}*/}
                {/*                closeOnAction*/}
                {/*                actions={[*/}
                {/*                    ([*/}
                {/*                        {*/}
                {/*                            key: 'cancel',*/}
                {/*                            text: '取消',*/}
                {/*                            danger: true,*/}
                {/*                            onClick: handleSalesCancel*/}
                {/*                        },*/}
                {/*												{*/}
                {/*													key: 'del',*/}
                {/*													text: '删除',*/}
                {/*													danger: true,*/}
                {/*													onClick: () => {*/}
                {/*														Dialog.confirm({*/}
                {/*															content: '确认删除吗？',*/}
                {/*															onConfirm: async () => {*/}
                {/*																saledeleteUser(saleUserRow.id);*/}
                {/*																setPuremoney(false);*/}
                {/*															},*/}
                {/*														})*/}
                {/*													}*/}
                {/*												},*/}
                {/*                        {*/}
                {/*                            key: 'confirm',*/}
                {/*                            text: '确定',*/}
                {/*                            bold: true,*/}
                {/*                            onClick: handleSubmitSales*/}
                {/*                        },*/}
                {/*                    ]).filter(d => {*/}
                {/*											if(saleUserRow.id) {*/}
                {/*												return true*/}
                {/*											}else {*/}
                {/*												return d.key !== 'del'*/}
                {/*											}*/}
                {/*										})*/}
                {/*                ]}*/}
                {/*            />*/}
                {/*        </div>*/}
                {/*    </div>*/}
                {/*    <DataGrid columns={salecolumns} rows={salesList} hideFooter={true} style={{ height: '220px' }} disableColumnMenu />*/}
                {/*</List.Item>*/}
                {/*<List.Item>*/}
                {/*  <div className={dataForm.countFunc}>*/}
                {/*    <div style={{ display: 'flex', alignItems: 'center' }}>*/}
                {/*      <AnalyticsIcon*/}
                {/*        className={dataForm.fontMain}*/}
                {/*        fontSize={'large'}*/}
                {/*      />*/}
                {/*      <span style={{ fontWeight: 'bold', marginRight: '5px' }}>*/}
                {/*        新客户*/}
                {/*      </span>*/}
                {/*    </div>*/}
                {/*    <div style={{ display: 'flex', alignItems: 'center' }}>*/}
                {/*      <span style={{ color: '#ababab', marginRight: '5px' }}>*/}
                {/*        (单位: 人)*/}
                {/*      </span>*/}
                {/*      <Button*/}
                {/*        disabled={!hospitalInfo.hospitalId}*/}
                {/*        size="small"*/}
                {/*        color="primary"*/}
                {/*        onClick={addSalesPerson}*/}
                {/*      >*/}
                {/*        新增*/}
                {/*      </Button>*/}
                {/*      <Dialog*/}
                {/*        destroyOnClose*/}
                {/*        visible={newperson}*/}
                {/*        title={salePersonRow.id ? '新客户编辑' : '新客户新增'}*/}
                {/*        content={*/}
                {/*          <AddSalesAmount*/}
                {/*            code3Info={code3Info}*/}
                {/*            setCode3Info={setCode3Info}*/}
                {/*            commonInfo={commonInfo}*/}
                {/*            setCommonInfo={setCommonInfo}*/}
                {/*            childRef={childRef}*/}
                {/*            hospitalId={hospitalInfo.hospitalId}*/}
                {/*            formDialog={formDialog}*/}
                {/*            itemData={salePersonRow}*/}
                {/*            products={[realProducts()]}*/}
                {/*            fullProducts={products}*/}
                {/*            visible={newperson}*/}
                {/*						userInfo={userInfo}*/}
                {/*					/>*/}
                {/*        }*/}
                {/*        closeOnAction*/}
                {/*        actions={[*/}
                {/*          [*/}
                {/*            {*/}
                {/*              key: 'cancel',*/}
                {/*              text: '取消',*/}
                {/*              danger: true,*/}
                {/*              onClick: handleCenterCancel,*/}
                {/*            },*/}
                {/*            {*/}
                {/*              key: 'del',*/}
                {/*              text: '删除',*/}
                {/*              danger: true,*/}
                {/*              onClick: () => {*/}
                {/*                Dialog.confirm({*/}
                {/*                  content: '确认删除吗？',*/}
                {/*                  onConfirm: async () => {*/}
                {/*                    persondeleteUser(salePersonRow.id)*/}
                {/*                    setNewperson(false)*/}
                {/*                  },*/}
                {/*                })*/}
                {/*              },*/}
                {/*            },*/}
                {/*            {*/}
                {/*              key: 'confirm',*/}
                {/*              text: '确定',*/}
                {/*              bold: true,*/}
                {/*              onClick: handleSubmitCenter,*/}
                {/*            },*/}
                {/*          ].filter((d) => {*/}
                {/*            if (salePersonRow.id) {*/}
                {/*              return true*/}
                {/*            } else {*/}
                {/*              return d.key !== 'del'*/}
                {/*            }*/}
                {/*          }),*/}
                {/*        ]}*/}
                {/*      />*/}
                {/*    </div>*/}
                {/*  </div>*/}
                {/*  <DataGrid*/}
                {/*    columns={personcolumns}*/}
                {/*    rows={customerList}*/}
                {/*    hideFooter={true}*/}
                {/*    style={{ height: '220px' }}*/}
                {/*    disableColumnMenu*/}
                {/*  />*/}
                {/*</List.Item>*/}
              </List>
            </div>
          </>
          {/*footer*/}
          <>
            {formType == "create" ? (
              <div className={dataForm.footer}>
                <Button
                  disabled={btnDisabled}
                  block
                  color="primary"
                  size="middle"
                  style={{ marginTop: "5px", width: "calc(100% - 20px)" }}
                  onClick={onSubmit}
                >
                  提交
                </Button>
              </div>
            ) : reporter === "management" || !canEdit ? null : (
              <div className={dataForm.fotter}>
                <Button
                  onClick={onSubmit}
                  block
                  color="primary"
                  size="middle"
                  style={{ marginTop: "5px", width: "calc(50% - 5px)" }}
                >
                  更新
                </Button>
                <Button
                  onClick={deleteTable}
                  block
                  color="primary"
                  size="middle"
                  style={{ marginTop: "5px", width: "calc(50% - 5px)" }}
                >
                  删除
                </Button>
              </div>
            )}
          </>
        </CategoryLayoutUpload>
      </div>
    </div>
  );
};

export default DataForm;
