const TEST_URL = "https://sales-webform-test-k8s.nbims.com.cn" // test环境
const UAT_URL = "https://sales-webform-uat.nbims.com.cn" // uat环境
const PROD_URL = "https://sales-webform.nbims.com.cn" // 生产环境

const urlMap = {
    development: TEST_URL,
    production: UAT_URL
}

let URL = urlMap.development // 默认dev环境BaiduPCS-Go
const url = () => {
    try {
        const env = process.env.NODE_ENV
        URL = urlMap[env]
        if (window.location.host === 'sales-networkreport-test.nbims.com.cn') {
            URL = TEST_URL
        } else if (window.location.host === 'sales-networkreport-uat.nbims.com.cn') {
            URL = UAT_URL
        } else if (window.location.host === 'sales-networkreport.nbims.com.cn') {
            URL = PROD_URL
        } else {
            URL = TEST_URL
        }
    } catch (e) {
        console.log(e)
        URL = urlMap.development
    }
}
url()

export { URL }
