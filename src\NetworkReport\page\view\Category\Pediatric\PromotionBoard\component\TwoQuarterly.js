import React from "react";
import styled from 'styled-components';
import ItemWrapper from "./ItemWrapper";
import {BoldText, GreenBoldText, isEmpty, realNum} from './../index';

const Wrapper = styled.div`
	.title {
		padding: 0 10px 10px 10px;
	}
	.sub-title {
		border: 1px solid #bbb;
		border-left: none;
		border-right: none;
    text-align: center;
    .top {
			font-size: 16px;
		}
    .top, .bot {
      height: 25px;
      line-height: 25px;
		}
	}
`;

const TwoQuarterly = ({ data = {}}) => {
	const _data1 = data['最近连续两个季度新患数据'] || {};
	const _data2 = data['最近连续两个季度纯销数据'] || {};
	return (
		<ItemWrapper>
			<Wrapper>
				<div className="title">
					最近连续2个季度是否达标
				</div>
				<div className="sub-title">
					<div className="top">新患</div>
					<div className="bot">
						考核期——新患达成率≥85%
					</div>
				</div>
			</Wrapper>
			<div className="content-extra">
				<div className="content" style={{ paddingBottom: 10 }}>
					<div className="item item-20">
						<div className="title">当季差距</div>
						<BoldText isBold={false} isWarning={!_data1.currentJudge}>
							<div className="value">{!_data1.currentJudge ? realNum(_data1.currentGap) : '达成'}</div>
						</BoldText>
					</div>
					<div className="item item-20">
						<div className="title">当季度指标</div>
						<div className="value">{realNum(_data1.currentIndex)}</div>
					</div>
					<div className="item item-20">
						<div className="title">当季达成</div>
						<div className="value">{realNum(_data1.currentActualAch)}</div>
					</div>
					<div className="item item-20">
						<div className="title">当季度是否符合</div>
							<BoldText isBold={false} isWarning={!_data1.currentJudge}>
								<div className="value">{ isEmpty(_data1.currentJudge) ? '-' : (_data1.currentJudge ? '是' : '否')}</div>
							</BoldText>
					</div>
					<div className="item item-20">
						<div className="title">当季度达成率</div>
						<BoldText isBold={false} isWarning={!_data1.currentJudge}>
							<div className="value">{realNum(_data1.currentAchRate, true)}</div>
						</BoldText>
					</div>
				</div>
				<div className="content" style={{ paddingBottom: 10 }}>
					<div className="item">
						<div className="title">上季度是否符合</div>
						<BoldText isBold isWarning={!_data1.lastJudge}>
							<div className="value">{ isEmpty(_data1.lastJudge) ? '-' : ( _data1.lastJudge ? '是' : '否')}</div>
						</BoldText>
					</div>
					<div className="item">
						<div className="title">上季度指标</div>
						<div className="value">{realNum(_data1.lastIndex)}</div>
					</div>
					<div className="item">
						<div className="title">上季度达成</div>
						<div className="value">{realNum(_data1.lastActualAch)}</div>
					</div>
					<div className="item">
						<div className="title">上季度达成率</div>
						<BoldText isBold isWarning={!_data1.lastJudge}>
							<div className="value">{realNum(_data1.lastAchRate, true)}</div>
						</BoldText>
					</div>
				</div>
			</div>
			<Wrapper>
				<div className="sub-title">
					<div className="top">纯销（万）</div>
					<div className="bot">
						考核期——纯销达成率≥100%
					</div>
				</div>
			</Wrapper>
			<div className="content-extra">
				<div className="content" style={{ paddingBottom: 10 }}>
					<div className="item item-20">
						<div className="title">当季差距</div>
						<BoldText isBold isWarning={!_data2.currentJudge}>
							<div className="value">{!_data2.currentJudge ? realNum(_data2.currentGap, false, true) : '达成'}</div>
						</BoldText>
					</div>
					<div className="item item-20">
						<div className="title">当季度指标</div>
						<div className="value">{realNum(_data2.currentIndex, false, true)}</div>
					</div>
					<div className="item item-20">
						<div className="title">当季达成</div>
						<div className="value">{realNum(_data2.currentActualAch, false, true)}</div>
					</div>
					<div className="item item-20">
						<div className="title">当季度是否符合</div>
						<BoldText isBold={false} isWarning={!_data2.currentJudge}>
							<div className="value">{isEmpty(_data2.currentJudge) ? '-' : (_data2.currentJudge ? '是' : '否')}</div>
						</BoldText>
					</div>
					<div className="item item-20">
						<div className="title">当季度达成率</div>
						<BoldText isBold={false} isWarning={!_data2.currentJudge}>
							<div className="value">{realNum(_data2.currentAchRate, true)}</div>
						</BoldText>
					</div>
				</div>
				<div className="content" style={{ paddingBottom: 10 }}>
					<div className="item">
						<div className="title">上季度是否符合</div>
						<BoldText isBold isWarning={!_data2.lastJudge}>
							<div className="value">{isEmpty(_data2.lastJudge) ? '-' : (_data2.lastJudge ? '是' : '否')}</div>
						</BoldText>
					</div>
					<div className="item">
						<div className="title">上季度指标</div>
						<div className="value">{realNum(_data2.lastIndex, false, true)}</div>
					</div>
					<div className="item">
						<div className="title">上季度达成</div>
						<div className="value">{realNum(_data2.lastActualAch, false, true)}</div>
					</div>
					<div className="item">
						<div className="title">上季度达成率</div>
						<BoldText isBold={false} isWarning={!_data2.lastJudge}>
							<div className="value">{realNum(_data2.lastAchRate, true)}</div>
						</BoldText>
					</div>
				</div>
			</div>
		</ItemWrapper>
	)
}

export default TwoQuarterly;
