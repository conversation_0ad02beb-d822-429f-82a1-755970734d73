import React, { useState, useEffect, RefObject ,useImperativeHandle} from 'react';

import {Button, Popup, Form, Input, SearchBar, PickerView, <PERSON><PERSON><PERSON>, <PERSON>er, TextArea, Toast} from "antd-mobile";
import dayjs from "dayjs";

const AddFormDialog = (props) => {
    console.log(props.data)
    const { data } = props
    const [form] = Form.useForm()
    const optionList = [{ label: '是', value: '是' }, { label: '否', value: '否' }, { label: '暂不覆盖', value: '暂不覆盖' }]

    useImperativeHandle((props.childRef),()=>{
        return {
            validateForm,
            handleFormData,
        }
    })
    const validateForm = async () => {
        await form.validateFields();
    };

    const handleFormData = () => {
        let values = form.getFieldsValue()
        return values
    }
    return (
        <div className={'customAddDialog'}>
            <Form form={form}>
                <Form.Item
                    name="doctorCode"
                    label="编码3"
                    rules={[{ required: true }]}
                    initialValue={data.doctorCode}>
                    <span style={{ color: '#cccccc' }}>{ data.doctorCode }</span>
                </Form.Item>
				<Form.Item
					name="prdBrand"
					label="产品"
					rules={[{ required: true }]}
					initialValue={data.prdBrand}>
					<span style={{ color: '#cccccc' }}>{ data.prdBrand }</span>
				</Form.Item>
                <Form.Item
                    name="estimateNewPat"
                    label="预估新患数"
                    rules={[{ required: true }]}
                    initialValue={data.estimateNewPat}>
                    <span style={{ color: '#cccccc' }}>{ data.estimateNewPat }</span>
                </Form.Item>
                <Form.Item
                    name="newPatMtd"
                    label="MTD新患数"
                    rules={[{ required: true }]}
                    initialValue={data.newPatMtd}>
                    <span style={{ color: '#cccccc' }}>{ data.newPatMtd }</span>
                </Form.Item>
                <Form.Item
                    name="newPatRate"
                    label="MTD达成率"
                    rules={[{ required: true }]}
                    initialValue={data.newPatRate}
                >
                    <span style={{ color: '#cccccc' }}>{ !data.newPatRate ? 0 : (data.newPatRate * 100).toFixed(2)}%</span>
                </Form.Item>
				{/* JSM */}
	            {
					data.prdBrand === 'JSM' && (
			            <>
				            <Form.Item
					            name="patNumWeek"
					            label="本周门诊患者数"
					            initialValue={data.patNumWeek}
					            rules={[
						            { required: true },
						            {
							            pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
							            message: '本周门诊患者数只能为整数',
						            },
					            ]}
				            >
					            <Input type={'number'} placeholder="请填写" max={99999} />
				            </Form.Item>
				            <Form.Item
					            name="adpddPatNumWeek"
					            label="AD&PDD本周患者数"
					            initialValue={data.adpddPatNumWeek}
					            rules={[
						            { required: true },
						            {
							            pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
							            message: 'AD&PDD本周患者数只能为整数',
						            },
					            ]}
				            >
					            <Input type={'number'} placeholder="请填写" max={99999} />
				            </Form.Item>
				            <Form.Item
					            name="patchPatNumWeek"
					            label="本周客户提及贴剂的患者数"
					            initialValue={data.patchPatNumWeek}
					            rules={[
						            { required: true },
						            {
							            pattern: new RegExp(/^[0-9]*[0-9][0-9]*$/),
							            message: '本周客户提及贴剂的患者数只能为整数',
						            },
					            ]}
				            >
					            <Input type={'number'} placeholder="请填写" max={99999} />
				            </Form.Item>
				            <Form.Item
					            name="patchPatType"
					            label="提及贴剂的患者类型"
					            rules={[{ required: true }]}
					            initialValue={data.patchPatType}
				            >
					            <TextArea
						            placeholder='请输入内容'
						            maxLength={1000}
						            rows={3}
						            showCount
					            />
				            </Form.Item>
			            </>
		            )
	            }
	            {/* GH */}
	            {
					data.prdBrand === 'GH' && (
			            <>
				            <Form.Item
					            name="patientFlow"
					            label="患者流"
					            rules={[{ required: true }]}
					            onClick={(e, pickerRef) => {
						            pickerRef.current?.open()
					            }}
					            initialValue={[data.patientFlow].filter(Boolean)}
					            trigger="onConfirm"
				            >
					            <Picker columns={[optionList]}>
						            {
							            (e) => <div>{ e[0]?.label || <span style={{ color: '#ccc' }}>请选择</span> }</div>
						            }
					            </Picker>
				            </Form.Item>
				            <Form.Item
					            name="screening"
					            label="筛查"
					            rules={[{ required: true }]}
					            onClick={(e, pickerRef) => {
						            pickerRef.current?.open()
					            }}
					            trigger="onConfirm"
					            initialValue={[data.screening].filter(Boolean)}
				            >
					            <Picker columns={[optionList]}>
						            {
							            (e) => <div>{ e[0]?.label || <span style={{ color: '#ccc' }}>请选择</span> }</div>
						            }
					            </Picker>
				            </Form.Item>
			            </>
		            )
	            }
	            <Form.Item
		            name="benchmarkCustomerRemarks"
		            label="标杆客户备注"
		            // rules={[{ required: true }]}
		            initialValue={data.benchmarkCustomerRemarks}
	            >
		            <span>{data.benchmarkCustomerRemarks}</span>
	            </Form.Item>
                <Form.Item
                    name="customerClassification"
                    label="客户分类"
                    // rules={[{ required: true }]}
                    initialValue={data.customerClassification}
                >
                    { data.customerClassification }
                </Form.Item>
                <Form.Item
                    name="problemClassification"
                    label="问题归类"
                    rules={[{ required: true }]}
                    onClick={(e, pickerRef) => {
                        pickerRef.current?.open()
                    }}
                    trigger="onConfirm"
                    initialValue={[data.problemClassification].filter(Boolean)}
                >
                    <Picker columns={[[
                        {
                            label: '筛查ITT',
                            value: '筛查ITT'
                        }, {
                            label: '患者流',
                            value: '患者流'
                        }, {
                            label: '药物经济',
                            value: '药物经济'
                        }, {
                            label: '必要性',
                            value: '必要性'
                        }, {
                            label: '筛查IGF1',
                            value: '筛查IGF1'
                        }, {
                            label: '安全性',
                            value: '安全性'
                        }, {
                            label: '购药渠道',
                            value: '购药渠道'
                        }, {
                            label: '暂不覆盖',
                            value: '暂不覆盖'
                        }
                    ]]}>
                        {
                            (e) => <div>{ e[0]?.label || <span style={{ color: '#ccc' }}>请选择</span> }</div>
                        }
                    </Picker>
                </Form.Item>
                <Form.Item
                    name="problemDescription"
                    label="影响新患产生的具体问题描述"
                    rules={[{ required: true }]}
                    initialValue={data.problemDescription}
                >
                    <TextArea
                        placeholder='请输入内容'
                        maxLength={1000}
                        rows={3}
                        showCount
                    />
                </Form.Item>
                <Form.Item
                    name="safeguardMeasures"
                    label="保障措施"
                    rules={[{ required: true }]}
                    initialValue={data.safeguardMeasures}
                >
                    <TextArea
                        placeholder='请输入内容'
                        maxLength={1000}
                        rows={3}
                        showCount
                    />
                </Form.Item>
	            <Form.Item
		            name="personResponsible"
		            label="责任人"
		            rules={[{ required: true }]}
		            initialValue={data.personResponsible}
	            >
		            <TextArea
			            placeholder='请输入内容'
			            maxLength={1000}
			            rows={3}
			            showCount
		            />
	            </Form.Item>
                <Form.Item
                    name="timeNode"
                    label="时间节点"
                    rules={[{ required: true }]}
                    trigger="onConfirm"
                    onClick={(e, datePickerRef) => {
                        datePickerRef.current?.open()
                    }}
                    initialValue={data.timeNode ? new Date(data.timeNode) : null}
                >
                    <DatePicker>
                        {value =>
                            value ? dayjs(value).format('YYYY-MM-DD') : <div style={{color: "#ccc"}}>请选择日期</div>
                        }
                    </DatePicker>
                </Form.Item>
                <Form.Item
                    name="safeguardMeasuresW"
                    label={`W${data.gsWeek || ''}保障措施进展`}
                    rules={[{ required: true }]}
                    initialValue={data.safeguardMeasuresW}
                >
                    <TextArea
                        placeholder='请输入内容'
                        maxLength={1000}
                        rows={3}
                        showCount
                    />
                </Form.Item>
            </Form>
        </div>
    )
}

export default AddFormDialog
