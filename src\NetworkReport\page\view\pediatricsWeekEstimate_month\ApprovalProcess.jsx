import React, {useEffect, useState} from 'react';
import styled from 'styled-components';
import { http1 } from "../../../utils/network";
import {Divider, Toast} from "antd-mobile";
import dayjs from "dayjs";

const Wrapper = styled.div`
  .item {
    .dot {
      height: 6px;
      width: 6px;
      border-radius: 50%;
      background: #C6CAD1;
      display: inline-block;
    }
    .label {
      display: inline-block;
      padding-left: 18px;
      .time {
        color: #869199;
      }
    }
    
  }
  .l {
    width: 1px;
    background: #C6CAD1;
    height: 36px;
    margin-left: 2px;
  }
`;

const ApprovalProcess = props => {

  const [data, setData] = useState({});

  useEffect(() => {
    getData();
  }, [props.deptCode])

  const getData = async () => {
    try {
      const res = await http1.post(`/pediatrics/approval/query/flow?deptCode=${props.deptCode}`) || {};
      setData(res)
    } catch (e) {
      if(e && e.message) {
        Toast.show(e.message)
      }
    }
  }
  return (
    <Wrapper>
      <div className="item">
        <div className="dot" style={{ background: '#2551F2'}}></div>
        <div className='label'>
          <span className='title'>数据填报</span>
        </div>
      </div>
      <div className="l" style={{ background: '#2551F2'}}></div>
      <div className="item">
        <div className="dot" style={data.submitName ? { background: '#2551F2'} : {}}></div>
        <div className='label'>
          <span className='title'>提交审核</span>
          {
            data.submitName  && (
              <>
                <Divider direction="vertical" />
                <span className='time'>{data.submitName}，{data.subTime ? dayjs(data.subTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</span>
              </>
            )
          }
        </div>
      </div>

      {
        data.approveName ?
          <>
            <div className="l" style={data.approveName ? { background: '#2551F2'} : {}}></div>
            <div className="item">
              <div className="dot" style={data.approveName ? { background: '#2551F2'} : {}}></div>
              <div className='label'>
                <span className='title'>大区审核</span>
                {
                  data.approveName && (
                    <>
                      <Divider direction="vertical" />
                      <span className='time'>{data.approveName},{data.approveTime ? dayjs(data.approveTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</span>
                    </>
                  )
                }
              </div>
            </div>
          </>
          :
          <>
            {
              data.rejectName && (
                <>
                  <div className="l" style={data.rejectName ? { background: '#2551F2'} : {}}></div>
                  <div className="item">
                    <div className="dot" style={data.rejectName ? { background: '#2551F2'} : {}}></div>
                    <div className='label'>
                      <span className='title'>大区审核</span>
                      {
                        data.rejectName && (
                          <>
                            <Divider direction="vertical" />
                            <span className='time'>{data.rejectName},驳回,<span style={{ color: 'red' }}>{data.rejectContent}</span>,{data.rejectTime ? dayjs(data.rejectTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</span>
                          </>
                        )
                      }
                    </div>
                  </div>
                </>
              )
            }

          </>
      }
      <div className="l" style={data.approveName ? { background: '#2551F2'} : {}}></div>
      <div className="item">
        <div className="dot" style={data.approveName ? { background: '#2551F2'} : {}}></div>
        <div className='label'>
          <span className='title'>审核完成</span>
        </div>
      </div>
    </Wrapper>
  )
}

export default ApprovalProcess
