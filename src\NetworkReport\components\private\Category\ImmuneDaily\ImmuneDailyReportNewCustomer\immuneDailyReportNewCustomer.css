.neuroendocrineDayReportNewCustomer {
    height: 100%;
}
.neuroendocrineDayReportNewCustomer .top{
    height: 25px;
    background: var(--gensci-second);
    color: white;
    font-size: 15px;
    display: flex;
    align-items: center;
    padding: 0 8px;
    box-shadow: 0 2px 2px #d2d2d2;
}
.neuroendocrineDayReportNewCustomer .header{
     height: 35px;
    display: flex;
    top: 0;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px 0 5px;
    background: #3644ac0f;
    border-bottom: 1px solid #3f5eb7;
}
.neuroendocrineDayReportNewCustomer .headerContext {
    display: flex;
    align-items: center;
    font-size: 15px;
}
.neuroendocrineDayReportNewCustomer .fontMain{
    color: orangered;
}
.neuroendocrineDayReportNewCustomer .fontSecond{
    color: orangered;
}
.neuroendocrineDayReportNewCustomer .content{
    height: calc(100% - 60px);
    overflow: auto;

}
.neuroendocrineDayReportNewCustomer .footer{
    width: 100%;
    height: 60px;
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    align-items: center;
    position: fixed;
    bottom: 0;
}
.neuroendocrineDayReportNewCustomer .historynote{
    width: 100%;
    border-bottom: 1px solid #3f5eb7;
}
  .historymenu{
    width: 100%;
    height: 192px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
 .icons{
    width: 15%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
 }
 .rightlist{
  width: 85%;
  height: 100%;
  display: flex;
  flex-direction: column; 
  border-bottom: 1px solid lightgray;
  padding: 10px 10px;
}
.fontMains{
    color:orange ;
}
